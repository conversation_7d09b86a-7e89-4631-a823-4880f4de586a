import http from '../utils/http.js'
import BaseConfig from '../config/index.config.js'
export default {
	//发送验证码
	sendCode: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/sendcode', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 登录
	h5Login: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/login', e, type, mask, loading, method,isCompleteUrl,contentType),

	selectbyid: (e, type = false, mask = true, loading = true, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectgrouptb', e, type, mask, loading, method,isCompleteUrl), //智慧社区的梯控数据
	selectEr: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectEr', e, type, mask, loading, method,isCompleteUrl), //智慧社区二维码（门禁）
	selectTkEr: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectTkEr', e, type, mask, loading, method,isCompleteUrl), //智慧社区二维码（梯控）
	selectDoorTkEr: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectDoorTkEr', e, type, mask, loading, method,isCompleteUrl), //智慧社区二维码（梯控）
	selectUserAllProject: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectUserAllProject', e, type, mask, loading, method,isCompleteUrl), //获取项目列表
	// 梯控校准时间
	checkTime: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/updateStartTime', e, type, mask, loading, method,isCompleteUrl), //获取项目列表
	// 设置常用门禁权限
	setCommonDevice: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/setCommonDevice', e, type, mask, loading, method,isCompleteUrl),
	
	// 获取项目人员信息
	getUserInfo: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/getUserInfo', e, type, mask, loading, method,isCompleteUrl),
	// 获取已开通了门禁的项目
	getFaceEstate: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/queryFaceEstate', e, type, mask, loading, method,isCompleteUrl),
	// 根据comid 获取区域列表
	getAllFaceVillage: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/getAllFaceVillage', e, type, mask, loading, method,isCompleteUrl),
	// 根据区域villageld 获取楼栋信息
	getAllBuildingInfo: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/getAllBuildingInfo', e, type, mask, loading, method,isCompleteUrl),
	// 根据楼栋buildingId 获取楼层信息
	getAllFloorInfo: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/getAllFloorInfo', e, type, mask, loading, method,isCompleteUrl),
	// 根据楼层floorId 获取房屋信息
	getAllHouseInfo: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/getdata/getAllHouseInfo', e, type, mask, loading, method,isCompleteUrl),
	// 基本信息提交
	faceAddUser: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/faceUserManage/adduser', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取所有门禁信息
	getDevicesByMobile: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'cloud/faceEntering/getDevicesByMobile', e, type, mask, loading, method,isCompleteUrl),
	//一键开门
	openDoor: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/device/toDeviceOperate', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取梯控权限
	getLadderPermissions: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/getLadderPermissions', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 点亮梯控功能
	takeLadder: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/takeLadder', e, type, mask, loading, method,isCompleteUrl),

	// 根据手机号获取所有项目
	getProjectInfoByPhone: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/getdata/getAllProjectInfoByPhone', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 根据手机号获取所有项目
	getAllProjectInfoByPhone: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facegetdata/getAllProjectInfoByPhone', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取民族字典
	getNation: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/getdata/getNation', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取文案
	getDescNew: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/face/getDescNew', e, type, mask, loading, method,isCompleteUrl, contentType),
	//实名认证
	faceUserRealName: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'cloud/face/faceUserRealName', e, type, mask, loading, method,isCompleteUrl),
	// face录入
	addFacePhoto: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceEntering/addFacePhotonew', e, type, mask, loading, method,isCompleteUrl, contentType),
	// face 图片上传
	uploadFace: (e) => http.upload(BaseConfig.cloudUrl+'cloud/upload/facePic', e), // 图片上传
	// 获取成员列表
	getFaceMember: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceMember/query', e, type, mask, loading, method,isCompleteUrl),
	// 获取租户成员列表
	getTenantMember: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceMember/tenantsquery', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 添加成员
	addFaceMember: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceMember/commitInfo', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 编辑成员
	editFaceMember: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceMember/edit', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 删除成员
	delFaceMember: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceMember/delete', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 缓存分享时间
	faceShareTimeCache: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceEntering/setCache', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取访客列表
	getVisitorsList: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceCaller/queryCaller', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取事由列表
	getCauseList: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceCallerReason/queryNew', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取门禁权限
	getAccessControl: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/getAllDeviceByUserId', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 添加临时访客
	addVisitores: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'cloud/faceCaller/yiMaAdd', e, type, mask, loading, method,isCompleteUrl),
	// 根据jsCode 查看分享后的信息
	getSharePreview: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'cloud/faceCaller/getCallerByOpenId', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取访客详情
	getVisitorDetails: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/queryById', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 删除访客
	delVisitor: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/delete', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取截止时间
	getEndTime: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceConfigParameter/select', e, type, mask, loading, method,isCompleteUrl),
	// 获取设备组
	getAllDoorsGroup: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/getAllDoorsGroup', e, type, mask, loading, method,isCompleteUrl),
	// 获取时间组
	getPermitTime: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/getPermitTime', e, type, mask, loading, method,isCompleteUrl),
	// 获取设备列表
	getAllDeviceInfoByProjectId: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/getAllDeviceInfoByProjectId', e, type, mask, loading, method,isCompleteUrl),
	// 获取梯控列表
	selectall: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/face/selectall', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取待审核人员
	getFaceUser: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/faceUser', e, type, mask, loading, method,isCompleteUrl),
	// 新增人员
	addFaceUserInfo: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/addFaceUserInfo', e, type, mask, loading, method,isCompleteUrl),
	// 获取总人数
	getFaceUserNum: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/faceUserNum', e, type, mask, loading, method,isCompleteUrl),
	// 获取用户地址信息
	queryUserHouseByUserId: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/queryUserHouseByUserId', e, type, mask, loading, method,isCompleteUrl),
	// 获取梯控权限信息
	queryAccessPower: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/selectbyid', e, type, mask, loading, method,isCompleteUrl),
  	// 获取梯控权限配置信息
	queryAccessParam: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/query', e, type, mask, loading, method,isCompleteUrl),
  	// 获取所有房屋信息
  	queryAllHouse: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/queryAllHouse', e, type, mask, loading, method,isCompleteUrl),
  	// 审核用户信息
	examine: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/face/examine', e, type, mask, loading, method,isCompleteUrl),
	// 成员审核
	menberExamine: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceMember/examine', e, type, mask, loading, method,isCompleteUrl),
	// 扫码开门
	scanQrCodeDoor: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/xinShiJufaceWork/scanQrCode', e, type, mask, loading, method,isCompleteUrl, contentType),
	
	// 查询报修信息
	getFaceReportQuery: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true) =>
		http.request( BaseConfig.cloudUrl + "wit/faceReport/query",e,type, mask,loading,method,isCompleteUrl),
	// 查询报修类型
	getFaceReportTypeQuery: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true,contentType="application/x-www-form-urlencoded") =>
		http.request( BaseConfig.cloudUrl + "wit/faceReportType/query",e,type, mask,loading,method,isCompleteUrl,contentType),
	// 新增报修内容
	getFaceReportTypeAdd: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true) =>
		http.request( BaseConfig.cloudUrl + "wit/faceReport/add",e,type, mask,loading,method,isCompleteUrl),
	// 查询报修详细信息	
	getFaceReportTypeQueryById: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true,contentType="application/x-www-form-urlencoded") =>
		http.request( BaseConfig.cloudUrl + "wit/faceReport/queryById",e,type, mask,loading,method,isCompleteUrl,contentType),
	// 获取便民电话信息
	getConveniencePhone: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true,contentType="application/x-www-form-urlencoded") =>
		http.request( BaseConfig.cloudUrl + "wit/faceConveniencePhone/query",e,type, mask,loading,method,isCompleteUrl,contentType),
	// 获取部门列表
	getFaceDepartmentList: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true) =>
		http.request( BaseConfig.cloudUrl + "wit/faceDepartment/queryAll",e,type, mask,loading,method,isCompleteUrl),
		
	// 推送管理服务是否可用查询
	pushMessage: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>
		http.request(BaseConfig.cloudUrl + "cloud/pushmessage/available",e,type,mask,loading,method,isCompleteUrl),
	/* 关注列表查询 */
	queryFocusOnApi: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true
	) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/queryFocusOn",e,type,mask,loading,method,
			isCompleteUrl),
	/* 公众号推送保存 */
	wxSave: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/bind",e,type,mask,loading,method,isCompleteUrl),
	/* 短信推送保存 */
	SMSSave: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/bind",e,type,mask,loading,method,isCompleteUrl),
	/* 取消关注 */
	cancelAttention: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/unbind",e,type,mask,loading,method,isCompleteUrl),
	/* 删除推送方式 */
	deletePushWay: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>
		http.request(BaseConfig.cloudUrl + "cloud/pushmessage/deletePushWay",e,type,mask,loading,method,isCompleteUrl),
	/* 推送管理-待确认页面 */
	confirmed: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/confirm",e,type,mask,loading,method,isCompleteUrl),
		/* 推送管理用户绑定信息 */
		getUserInfoApi: (e,type = false,mask = true,loading = false,method = "POST",isCompleteUrl = true) =>http.request(BaseConfig.cloudUrl + "cloud/pushmessage/getUserInfo",e,type,mask,loading,method,isCompleteUrl),

	// 公告查看详情
  	getBulletinDetail: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => 
		http.request(BaseConfig.cloudUrl+`wit/bulletin/getBulletin`, e, type, mask, loading, method,isCompleteUrl),

// 公告信息
		getBulletinPage: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+`wit/bulletin/getBulletinPage`,e, type, mask, loading, method,isCompleteUrl),

		// 根据用户id查询设置信息首页通行码
		getSettingByUserId: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+`wit/faceUserManage/getSettingByUserId`,e, type, mask, loading, method,isCompleteUrl),
		// 更新用户设置信息
	updateSettingCode: (e,type = false,mask = true,loading = false, method = "POST", isCompleteUrl = true,) =>
	http.request( BaseConfig.cloudUrl + "wit/faceUserManage/updateSetting",e,type, mask,loading,method,isCompleteUrl),

	// 获取当前项目下的所有区域
	getAllFaceVillageByProjectId: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) =>
		http.request(BaseConfig.cloudUrl+`wit/faceVillage/getAllFaceVillageByUserId`,e, type, mask, loading, method,isCompleteUrl),
	// 根据区域查询设备
	getDevicesByVillage: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) =>
		http.request(BaseConfig.cloudUrl+`wit/faceEntering/getDevicesByVillage`,e, type, mask, loading, method,isCompleteUrl),
	// 获取虚拟卡号
	getVirtualCard: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) =>
		http.request(BaseConfig.cloudUrl+`wit/virtualCard/selectVirtualCard`,e, type, mask, loading, method,isCompleteUrl),

		// 查询访客门禁和梯控权限
		getYiMaPermission: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+`wit/faceuserpermissiongrouptb/getYiMaPermission`,e, type, mask, loading, method,isCompleteUrl),
	// 获取生成门禁二维码的字符串 json格式
	getSelectYiMaDoorEr: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectYiMaDoorEr', e, type, mask, loading, method,isCompleteUrl),
	// 获取生成梯控二维码的字符串
	getSelectYiMaTkEr: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/selectYiMaTkEr', e, type, mask, loading, method,isCompleteUrl),
	// 获取访客邀约必填和显示信息
		getYiMaCallerSet: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/getYiMaCallerSet', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 分页查询访客凭证
	getFaceCallerPagingQuery: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/facecaller/pagingQuery', e, type, mask, loading, method,isCompleteUrl),
	// 分页查询待审核访客管理
	queryToAudit: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/facecaller/queryToAudit', e, type, mask, loading, method,isCompleteUrl),
	// 分页查询访客记录
	getFaceCallerQueryRecord: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/facecaller/queryRecord', e, type, mask, loading, method,isCompleteUrl),
	// 访客审核
	updatecaller: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/updatecaller', e, type, mask, loading, method,isCompleteUrl, contentType),
		// H5页面微信登录
		wxH5Login: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceuserpermissiongrouptb/wxH5Login', e, type, mask, loading, method,isCompleteUrl,'application/x-www-form-urlencoded'),
	// 访客凭证手机验证码下发
	facecallerSendCode: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/facecaller/sendcode', e, type, mask, loading, method,isCompleteUrl,'application/x-www-form-urlencoded'),
	// 访客信息登记
	getcallerset: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'wit/facecaller/getcallerset', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 查询房屋
	getAllAddrByComId: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceEntering/getAllAddrByComId', e, type, mask, loading, method,isCompleteUrl,'application/x-www-form-urlencoded'),
	// 添加访客
	commitCaller: (e, type = true, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/facecaller/commitCaller', e, type, mask, loading, method,isCompleteUrl,'application/x-www-form-urlencoded'),
	// 查询社区项目
	faceProjectInfoQueryById: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+`wit/faceProjectInfo/queryById`,e, type, mask, loading, method,isCompleteUrl),
	
	
	
	/**
	 * @description  语音通话相关的接口
	 * 
	 */
	getParkInfo: (e, type = true, mask = true, loading = true, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'zld/parkingos/getComByUuid', e, type, mask, loading, method,isCompleteUrl),
	getRoomInfo: (e, type = true, mask = true, loading = true, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'zld/parkingos/callOut', e, type, mask, loading, method,isCompleteUrl),
	noticeService: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'zld/parkingos/noticeUser', e, type, mask, loading, method,isCompleteUrl, contentType),
	hangUpAfterToService: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(BaseConfig.cloudUrl+'zld/parkingos/handelVideo', e, type, mask, loading, method,isCompleteUrl, contentType),
	sendCallHeartbate: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'videoBeat', e, type, mask, loading, method,isCompleteUrl),
	
	/**  voice call end  **/
}