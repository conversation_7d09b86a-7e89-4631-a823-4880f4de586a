<template>
	<view class="container">
		<custom-info-list v-if="isAllowShow" @talkCarScroll="talkCarScroll"  title="热门资讯" :dataList="infoData" @praiseClick="praiseClick" :isEnd="isEnd" :navHeight ="navHeight" :statusBarHeight="statusBarHeight"></custom-info-list>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	import CustomInfoList from './CustomInfoList.vue';
	let app = getApp();
	export default {
		components: {
			CustomInfoList,
		},
		props:{
			infoData: {
				type: Array,
				default: ()=>{
					return []
				},
			},
			isAllowShow: {
				type: Boolean,
				default: false,
			},
			isEnd: {
				type: Boolean,
				default: false,
			},
			statusBarHeight:{
				type:Number,				
			},
			navHeight:{
				type:Number,				
			}
		},
		data(){
			return {

			}
		},

		methods:{
			praiseClick(item,index){
				this.$emit('praiseClick',{item,index})
			},
			talkCarScroll(){
				this.$emit('talkCarScroll')
			},
		}
	}
</script>

<style lang="less" scoped>
	.container{
		width:100%;
		height:100%;
	}

</style>
