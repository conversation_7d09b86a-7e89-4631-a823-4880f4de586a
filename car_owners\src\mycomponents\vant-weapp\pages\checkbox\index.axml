<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block title='基本用法' ref='saveChildRef1'>
    <van-checkbox value='{{ checkbox1 }}' data-key='checkbox1' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef2'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='禁用状态' ref='saveChildRef3'>
    <van-checkbox disabled=" " value='{{ false }}' custom-class='demo-checkbox' ref='saveChildRef4'>
      复选框
    </van-checkbox>
    <van-checkbox disabled=" " value='{{ true }}' custom-class='demo-checkbox' ref='saveChildRef5'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='自定义形状' ref='saveChildRef6'>
    <van-checkbox value='{{ checkboxShape }}' data-key='checkboxShape' shape='square' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef7'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='自定义颜色' ref='saveChildRef8'>
    <van-checkbox value='{{ checkbox2 }}' data-key='checkbox2' checked-color='#07c160' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef9'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='自定义大小' ref='saveChildRef10'>
    <van-checkbox icon-size='25px' value='{{ checkboxSize }}' data-key='checkboxSize' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef11'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='自定义图标' ref='saveChildRef12'>
    <van-checkbox use-icon-slot=" " value='{{ checkbox3 }}' data-key='checkbox3' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef13'>
      自定义图标      <image slot='icon' class='icon' mode='widthFix' src='{{ checkbox3 ? activeIcon : inactiveIcon }}'>
      </image>
    </van-checkbox>
  </demo-block>
  <demo-block title='禁用文本点击' ref='saveChildRef14'>
    <van-checkbox label-disabled=" " value='{{ checkboxLabel }}' data-key='checkboxLabel' custom-class='demo-checkbox' onChange='onChange' ref='saveChildRef15'>
      复选框
    </van-checkbox>
  </demo-block>
  <demo-block title='复选框组' ref='saveChildRef16'>
    <van-checkbox-group value='{{ result }}' data-key='result' onChange='onChange' ref='saveChildRef17'>
      <van-checkbox a:for='{{ list }}' a:key='{{*this}}' name='{{ item }}' custom-class='demo-checkbox' ref-numbers='{{ list }}' ref='saveChildRef18'>
        复选框 {{ item }}
      </van-checkbox>
    </van-checkbox-group>
  </demo-block>
  <demo-block title='限制最大可选数' ref='saveChildRef19'>
    <van-checkbox-group value='{{ result2 }}' data-key='result2' max='2' onChange='onChange' ref='saveChildRef20'>
      <van-checkbox a:for='{{ list }}' a:key='{{*this}}' name='{{ item }}' custom-class='demo-checkbox' ref-numbers='{{ list }}' ref='saveChildRef21'>
        复选框 {{ item }}
      </van-checkbox>
    </van-checkbox-group>
  </demo-block>
  <demo-block title='搭配单元格组件使用' ref='saveChildRef22'>
    <van-checkbox-group value='{{ result3 }}' data-key='result3' onChange='onChange' ref='saveChildRef23'>
      <van-cell-group ref='saveChildRef24'>
        <van-cell a:for='{{ list }}' a:key='{{*this}}' title='复选框 {{ item }}' value-class='value-class' clickable=" " data-index='{{ index }}' onClick='toggle' ref-numbers='{{ list }}' ref='saveChildRef25'>
          <van-checkbox class='checkboxes-{{ index }}' name='{{ item }}' catchTap='antmoveAction' data-antmove-tap='noop' ref='saveChildRef26'>
          </van-checkbox>
        </van-cell>
      </van-cell-group>
    </van-checkbox-group>
  </demo-block>
</view>