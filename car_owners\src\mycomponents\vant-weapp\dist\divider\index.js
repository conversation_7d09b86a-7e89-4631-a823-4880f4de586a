"use strict";

var _component = require("../common/component");

(0, _component.VantComponent)({
  props: {
    dashed: {
      type: Boolean,
      value: false
    },
    hairline: {
      type: Boolean,
      value: false
    },
    contentPosition: {
      type: String,
      value: ""
    },
    fontSize: {
      type: Number,
      value: ""
    },
    borderColor: {
      type: String,
      value: ""
    },
    textColor: {
      type: String,
      value: ""
    },
    customStyle: {
      type: String,
      value: ""
    }
  }
});