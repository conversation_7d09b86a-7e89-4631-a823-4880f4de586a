export default class WebGLRenderingContext {
    constructor(canvas: any, type: any, attrs: any);
    className: string;
    _canvas: any;
    _type: any;
    _version: string;
    _attrs: any;
    _map: Map<any, any>;
    get canvas(): any;
    activeTexture: (textureUnit: any) => void;
    attachShader: (progarm: any, shader: any) => void;
    bindAttribLocation: (program: any, index: any, name: any) => void;
    bindBuffer: (target: any, buffer: any) => void;
    bindFramebuffer: (target: any, framebuffer: any) => void;
    bindRenderbuffer: (target: any, renderBuffer: any) => void;
    bindTexture: (target: any, texture: any) => void;
    blendColor: (r: any, g: any, b: any, a: any) => void;
    blendEquation: (mode: any) => void;
    blendEquationSeparate: (modeRGB: any, modeAlpha: any) => void;
    blendFunc: (sfactor: any, dfactor: any) => void;
    blendFuncSeparate: (srcRGB: any, dstRGB: any, srcAlpha: any, dstAlpha: any) => void;
    bufferData: (target: any, data: any, usage: any) => void;
    bufferSubData: (target: any, offset: any, data: any) => void;
    checkFramebufferStatus: (target: any) => number;
    clear: (mask: any) => void;
    clearColor: (r: any, g: any, b: any, a: any) => void;
    clearDepth: (depth: any) => void;
    clearStencil: (s: any) => void;
    colorMask: (r: any, g: any, b: any, a: any) => void;
    compileShader: (shader: any) => void;
    compressedTexImage2D: (target: any, level: any, internalformat: any, width: any, height: any, border: any, pixels: any) => void;
    compressedTexSubImage2D: (target: any, level: any, xoffset: any, yoffset: any, width: any, height: any, format: any, pixels: any) => void;
    copyTexImage2D: (target: any, level: any, internalformat: any, x: any, y: any, width: any, height: any, border: any) => void;
    copyTexSubImage2D: (target: any, level: any, xoffset: any, yoffset: any, x: any, y: any, width: any, height: any) => void;
    createBuffer: () => Buffer;
    createFramebuffer: () => Framebuffer;
    createProgram: () => Program;
    createRenderbuffer: () => Renderbuffer;
    createShader: (type: any) => Shader;
    createTexture: () => Texture;
    cullFace: (mode: any) => void;
    deleteBuffer: (buffer: any) => void;
    deleteFramebuffer: (framebuffer: any) => void;
    deleteProgram: (program: any) => void;
    deleteRenderbuffer: (renderbuffer: any) => void;
    deleteShader: (shader: any) => void;
    deleteTexture: (texture: any) => void;
    depthFunc: (func: any) => void;
    depthMask: (flag: any) => void;
    depthRange: (zNear: any, zFar: any) => void;
    detachShader: (program: any, shader: any) => void;
    disable: (cap: any) => void;
    disableVertexAttribArray: (index: any) => void;
    drawArrays: (mode: any, first: any, count: any) => void;
    drawElements: (mode: any, count: any, type: any, offset: any) => void;
    enable: (cap: any) => void;
    enableVertexAttribArray: (index: any) => void;
    flush: () => void;
    framebufferRenderbuffer: (target: any, attachment: any, textarget: any, texture: any, level: any) => void;
    framebufferTexture2D: (target: any, attachment: any, textarget: any, texture: any, level: any) => void;
    frontFace: (mode: any) => void;
    generateMipmap: (target: any) => void;
    getActiveAttrib: (progarm: any, index: any) => ActiveInfo;
    getActiveUniform: (progarm: any, index: any) => ActiveInfo;
    getAttachedShaders: (progarm: any) => any;
    getAttribLocation: (progarm: any, name: any) => any;
    getBufferParameter: (target: any, pname: any) => any;
    getError: () => any;
    getExtension: (name: any) => null;
    getFramebufferAttachmentParameter: (target: any, attachment: any, pname: any) => any;
    getParameter: (pname: any) => any;
    getProgramInfoLog: (progarm: any) => any;
    getProgramParameter: (program: any, pname: any) => any;
    getRenderbufferParameter: (target: any, pname: any) => any;
    getShaderInfoLog: (shader: any) => any;
    getShaderParameter: (shader: any, pname: any) => any;
    getShaderPrecisionFormat: (shaderType: any, precisionType: any) => ShaderPrecisionFormat;
    getShaderSource: (shader: any) => any;
    getSupportedExtensions: () => string[];
    getTexParameter: (target: any, pname: any) => any;
    getUniformLocation: (program: any, name: any) => UniformLocation | null;
    getVertexAttrib: (index: any, pname: any) => any;
    getVertexAttribOffset: (index: any, pname: any) => number;
    isBuffer: (buffer: any) => boolean;
    isContextLost: () => boolean;
    isEnabled: (cap: any) => boolean;
    isFramebuffer: (framebuffer: any) => boolean;
    isProgram: (program: any) => boolean;
    isRenderbuffer: (renderBuffer: any) => boolean;
    isShader: (shader: any) => boolean;
    isTexture: (texture: any) => boolean;
    lineWidth: (width: any) => void;
    linkProgram: (program: any) => void;
    pixelStorei: (pname: any, param: any) => void;
    polygonOffset: (factor: any, units: any) => void;
    readPixels: (x: any, y: any, width: any, height: any, format: any, type: any, pixels: any) => any;
    renderbufferStorage: (target: any, internalFormat: any, width: any, height: any) => void;
    sampleCoverage: (value: any, invert: any) => void;
    scissor: (x: any, y: any, width: any, height: any) => void;
    shaderSource: (shader: any, source: any) => void;
    stencilFunc: (func: any, ref: any, mask: any) => void;
    stencilFuncSeparate: (face: any, func: any, ref: any, mask: any) => void;
    stencilMask: (mask: any) => void;
    stencilMaskSeparate: (face: any, mask: any) => void;
    stencilOp: (fail: any, zfail: any, zpass: any) => void;
    stencilOpSeparate: (face: any, fail: any, zfail: any, zpass: any) => void;
    texImage2D: (...args: any[]) => void;
    texParameterf: (target: any, pname: any, param: any) => void;
    texParameteri: (target: any, pname: any, param: any) => void;
    texSubImage2D: (...args: any[]) => void;
    uniform1f: (location: any, v0: any) => void;
    uniform1fv: (location: any, value: any) => void;
    uniform1i: (location: any, v0: any) => void;
    uniform1iv: (location: any, value: any) => void;
    uniform2f: (location: any, v0: any, v1: any) => void;
    uniform2fv: (location: any, value: any) => void;
    uniform2i: (location: any, v0: any, v1: any) => void;
    uniform2iv: (location: any, value: any) => void;
    uniform3f: (location: any, v0: any, v1: any, v2: any) => void;
    uniform3fv: (location: any, value: any) => void;
    uniform3i: (location: any, v0: any, v1: any, v2: any) => void;
    uniform3iv: (location: any, value: any) => void;
    uniform4f: (location: any, v0: any, v1: any, v2: any, v3: any) => void;
    uniform4fv: (location: any, value: any) => void;
    uniform4i: (location: any, v0: any, v1: any, v2: any, v3: any) => void;
    uniform4iv: (location: any, value: any) => void;
    uniformMatrix2fv: (location: any, transpose: any, value: any) => void;
    uniformMatrix3fv: (location: any, transpose: any, value: any) => void;
    uniformMatrix4fv: (location: any, transpose: any, value: any) => void;
    useProgram: (progarm: any) => void;
    validateProgram: (program: any) => void;
    vertexAttrib1f: (index: any, v0: any) => void;
    vertexAttrib2f: (index: any, v0: any, v1: any) => void;
    vertexAttrib3f: (index: any, v0: any, v1: any, v2: any) => void;
    vertexAttrib4f: (index: any, v0: any, v1: any, v2: any, v3: any) => void;
    vertexAttrib1fv: (index: any, value: any) => void;
    vertexAttrib2fv: (index: any, value: any) => void;
    vertexAttrib3fv: (index: any, value: any) => void;
    vertexAttrib4fv: (index: any, value: any) => void;
    vertexAttribPointer: (index: any, size: any, type: any, normalized: any, stride: any, offset: any) => void;
    viewport: (x: any, y: any, width: any, height: any) => void;
}
import Buffer from "./Buffer";
import Framebuffer from "./Framebuffer";
import Program from "./Program";
import Renderbuffer from "./Renderbuffer";
import Shader from "./Shader";
import Texture from "./Texture";
import ActiveInfo from "./ActiveInfo";
import ShaderPrecisionFormat from "./ShaderPrecisionFormat";
import UniformLocation from "./UniformLocation";
