.loading-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.loading-index {
    font-size: 0;
    line-height: 1;
}

.loading-index .van-loading {
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    color: #c8c9cc;
    color: var(--loading-spinner-color, #c8c9cc);
}

.loading-index .van-loading__spinner {
    position: relative;
    box-sizing: border-box;
    width: 30px;
    width: var(--loading-spinner-size, 30px);
    max-width: 100%;
    max-height: 100%;
    height: 30px;
    height: var(--loading-spinner-size, 30px);
    -webkit-animation: van-rotate 0.8s linear infinite;
    animation: van-rotate 0.8s linear infinite;
    -webkit-animation: van-rotate
        var(--loading-spinner-animation-duration, 0.8s) linear infinite;
    animation: van-rotate var(--loading-spinner-animation-duration, 0.8s) linear
        infinite;
}

.loading-index .van-loading__spinner--spinner {
    -webkit-animation-timing-function: steps(12);
    animation-timing-function: steps(12);
}

.loading-index .van-loading__spinner--circular {
    border: 1px solid transparent;
    border-top-color: initial;
    border-radius: 100%;
}

.loading-index .van-loading__text {
    margin-left: 8px;
    margin-left: var(--padding-xs, 8px);
    color: #969799;
    color: var(--loading-text-color, #969799);
    font-size: 14px;
    font-size: var(--loading-text-font-size, 14px);
    line-height: 20px;
    line-height: var(--loading-text-line-height, 20px);
}

.loading-index .van-loading__text:empty {
    display: none;
}

.loading-index .van-loading--vertical {
    -webkit-flex-direction: column;
    flex-direction: column;
}

.loading-index .van-loading--vertical .van-loading__text {
    margin: 8px 0 0;
    margin: var(--padding-xs, 8px) 0 0;
}

.loading-index .van-loading__dot {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.loading-index .van-loading__dot:before {
    display: block;
    width: 2px;
    height: 25%;
    margin: 0 auto;
    background-color: currentColor;
    border-radius: 40%;
    content: " ";
}

.loading-index .van-loading__dot:first-of-type {
    -webkit-transform: rotate(30deg);
    transform: rotate(30deg);
    opacity: 1;
}

.loading-index .van-loading__dot:nth-of-type(2) {
    -webkit-transform: rotate(60deg);
    transform: rotate(60deg);
    opacity: 0.9375;
}

.loading-index .van-loading__dot:nth-of-type(3) {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    opacity: 0.875;
}

.loading-index .van-loading__dot:nth-of-type(4) {
    -webkit-transform: rotate(120deg);
    transform: rotate(120deg);
    opacity: 0.8125;
}

.loading-index .van-loading__dot:nth-of-type(5) {
    -webkit-transform: rotate(150deg);
    transform: rotate(150deg);
    opacity: 0.75;
}

.loading-index .van-loading__dot:nth-of-type(6) {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    opacity: 0.6875;
}

.loading-index .van-loading__dot:nth-of-type(7) {
    -webkit-transform: rotate(210deg);
    transform: rotate(210deg);
    opacity: 0.625;
}

.loading-index .van-loading__dot:nth-of-type(8) {
    -webkit-transform: rotate(240deg);
    transform: rotate(240deg);
    opacity: 0.5625;
}

.loading-index .van-loading__dot:nth-of-type(9) {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
    opacity: 0.5;
}

.loading-index .van-loading__dot:nth-of-type(10) {
    -webkit-transform: rotate(300deg);
    transform: rotate(300deg);
    opacity: 0.4375;
}

.loading-index .van-loading__dot:nth-of-type(11) {
    -webkit-transform: rotate(330deg);
    transform: rotate(330deg);
    opacity: 0.375;
}

.loading-index .van-loading__dot:nth-of-type(12) {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
    opacity: 0.3125;
}

@-webkit-keyframes van-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn);
    }
}

@keyframes van-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn);
    }
}
