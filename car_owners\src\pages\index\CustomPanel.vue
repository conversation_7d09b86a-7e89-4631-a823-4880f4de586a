<template>
	<view class="custom-panel" :style="styleName">
		<view class="custom-panel--title" v-if="title&&title!=='热门资讯'">
		{{title}}
		<slot name="operate"></slot>
		</view>
		<view class="custom-panel--body">
			<slot></slot>
		</view>
	</view>
</template>
<script>
	export default {
		name: "CustomPanel",
		props: {
			title:String,
			styleName: {
				type: Object,
				default(){
					return {}
				}
			}
		},
		
	}
</script>
<style lang="less">
	.custom-panel {
		margin: 0 26rpx 20rpx 26rpx;
		position: relative;
		padding: 28rpx 27rpx;
		border-radius: 30rpx;
		background: #fff;
		box-shadow: 0 4px 26px 0 rgba(0,0,0,0.04); 
		&--title {
			margin-bottom: 22rpx;
			height: 50rpx;
			line-height: 50rpx;
			font-size: 36rpx;
			font-weight: 800;
			text-align: left;
			color: #242e3e;
			
		}
	}
</style>