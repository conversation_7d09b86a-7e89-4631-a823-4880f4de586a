<template>
	<view class="bl-empty" :style="[styleName]">
		<view class="bl-empty-image">
			<image src="https://image.bolink.club/bl_empty_default.png" mode="aspectFit" v-if="image === 'default'"></image>
			<image src="https://image.bolink.club/bl_empty_member.png" mode="aspectFit" v-if="image === 'member'"></image>
			<image src="https://image.bolink.club/bl_empty_visitors.png" mode="aspectFit" v-if="image === 'visitors'"></image>
			<image src="https://image.bolink.club/yima/bl_network_offline.png" mode="aspectFit" v-if="image === 'offline'"></image>
			<image :src="image" mode="aspectFit" v-if="isCustom"></image>
		</view>
		<view class="bl-empty-text">{{description}}</view>
	</view>
</template>

<script>
	export default {
		name:"bl-empty",
		props: {
			styleName: Object,
			image: {
			 type: String,
			 default: 'default'
			},
			description: String,
		},
		computed: {
			isCustom() {
				return this.image.match('https');
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="less" scoped>
	.bl-empty {
		width: 100%;
		text-align: center;
	}
	.bl-empty-image {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		margin-bottom: 60rpx;
	}
	.bl-empty-text {
		font-size: 26rpx;
		font-weight: 500;
		color: #999999;
		line-height: 37rpx;
	}
</style>
