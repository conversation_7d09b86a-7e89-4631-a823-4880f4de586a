{"version": 3, "file": "bridge-weex.js", "sourceRoot": "", "sources": ["../../../../../packages/plugins/gcanvas/gcanvas/bridge/bridge-weex.js"], "names": [], "mappings": "AAAA,MAAM,MAAM,GAAG,OAAO,aAAa,KAAK,WAAW,CAAC;AACpD,MAAM,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;AAE3C,OAAO,QAAQ,MAAM,2BAA2B,CAAC;AAEjD,MAAM,aAAa,GACf,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC,OAAO,gBAAgB,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAEpG,IAAI,WAAW,GAAG,KAAK,CAAC;AAExB,IAAI,eAAe,GAAG,KAAK,CAAC;AAE5B,MAAM,UAAU,GAAG,CAAC;IAChB,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAChC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IACrC,CAAC,CAAC,CAAA;IACF,MAAM,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE;QACvB,OAAO,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC;IACzD,CAAC,CAAA;IACD,MAAM,UAAU,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,aAAa,KAAK,WAAW,IAAI,EAAE,CAAC,CAAC;IACvF,CAAC,CAAA;IACD,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC,EAAE,CAAC;AAEL,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG;IACvB,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,KAAK,CAAC,EAAE;YACT,GAAG,IAAI,GAAG,CAAC;SACd;QACD,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AAED,MAAM,aAAa,GAAG,EAAE,CAAA;AAExB,MAAM,OAAO,GAAG;IAEZ,UAAU,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;QAE7B,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAExB,OAAO,aAAa,CAAC,MAAM,CAAC;YACxB,WAAW,EAAE,GAAG;YAChB,MAAM,EAAE,WAAW;SACtB,CAAC,CAAC;IACP,CAAC;IAED,eAAe,EAAE,GAAG,EAAE;QAClB,WAAW,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,sBAAsB,EAAE,GAAG,EAAE;QACzB,eAAe,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED,kBAAkB,EAAE,UAAU,WAAW,EAAE,YAAY;QACnD,aAAa,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED,SAAS,EAAE,UAAS,EAAE;QAClB,aAAa,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,UAAU,WAAW;QACrC,OAAO,aAAa,CAAC,gBAAgB,CAAC;YAClC,SAAS,EAAE,WAAW;YACtB,IAAI,EAAE,UAAU;SACnB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,UAAU,WAAW;QACrB,OAAO,qBAAqB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,UAAU,WAAW,EAAE,QAAQ,EAAE,QAAQ;QAE3D,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,QAAQ,CAAC,CAAC;SAC5C;QAED,aAAa,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAA,CAAC,CAAA,IAAI,CAAA,CAAC,CAAA,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IAEjF,CAAC,CAAC,CAAC,CAAC,UAAU,WAAW,EAAE,QAAQ,EAAC,QAAQ;QAExC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,QAAQ,CAAC,CAAC;SAC5C;QAED,qBAAqB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAG,QAAQ,EAAC;YACZ,QAAQ,EAAE,CAAC;SACV;IACC,CAAC;IAED,oBAAoB,EAAE,SAAS,CAAC,CAAC,CAAC,UAAU,WAAW,EAAE,OAAO;QAE5D,MAAM,6BAA6B,GAAG,OAAO,CAAC;IAElD,CAAC,CAAC,CAAC,CAAC,UAAU,WAAW,EAAE,OAAO;QAE9B,MAAM,6BAA6B,GAAG,OAAO,CAAC;IAElD,CAAC;IAGD,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,UAAU,WAAW;QAE1C,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3D,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAEhC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC;SAC3C;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,CAAC;YAC1C,WAAW,EAAE,WAAW;YACxB,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,OAAO;SAClB,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;QAEpC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC;SACrC;QAED,OAAO,GAAG,CAAC;IAEf,CAAC,CAAC,CAAC,CAAC,UAAU,WAAW;QAErB,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3D,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAEhC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC;SAC3C;QAED,MAAM,MAAM,GAAG,qBAAqB,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAEvE,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC;SACxC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,UAAU,EAAE,UAAU,WAAW,EAAE,OAAO,EAAE,KAAK;QAE7C,IAAI,WAAW,EAAE;YACb,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACpC;QAED,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,KAAK,IAAI,eAAe,EAAE;YAC3B,OAAO,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAC3C;aAAM;YACH,OAAO,SAAS,CAAC;SACpB;IACL,CAAC;IAED,UAAU,CAAC,WAAW,EAAE,GAAG,IAAI;QAC3B,IAAI,SAAS,EAAE;YACX,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnB,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;gBAClE,OAAO,CAAC,UAAU,CACd,WAAW,EACX,QAAQ,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,CAClI,CAAA;aACJ;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;gBACzF,OAAO,CAAC,UAAU,CACd,WAAW,EACX,QAAQ,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG;oBACnI,CAAE,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACxD,CAAA;aACJ;SACJ;aAAM,IAAI,aAAa,EAAE;YACtB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnB,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;gBAClE,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;aACjG;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;gBACzF,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtI;SACJ;IACL,CAAC;IAED,aAAa,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK;QAC3E,IAAI,SAAS,EAAE;YACX,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO,CAAC,UAAU,CACd,WAAW,EACX,QAAQ,CAAC,aAAa,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,EAAE,CAAE,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,CAC/I,CAAA;aACJ;SACJ;aAAM,IAAI,aAAa,EAAE;YACtB,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;SACtG;IACL,CAAC;IAED,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO;QACtC,aAAa,CAAC,gBAAgB,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC;IAChE,CAAC;IAED,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,QAAQ;QAC5B,aAAa,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,UAAU,KAAK;YACjD,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;YAChB,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC;YACd,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAEJ,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW;QACpC,OAAO,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAED,YAAY,CAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ;QAC9C,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,WAAW,EAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,YAAY,CAAE,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ;QACpD,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC,EAAC,WAAW,EAAC,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED,cAAc,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;QAClG,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IACrH,CAAC;CACD,CAAA;AAED,eAAe,OAAO,CAAC"}