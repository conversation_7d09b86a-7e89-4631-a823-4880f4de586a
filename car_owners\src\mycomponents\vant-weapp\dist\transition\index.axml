<view class='transition-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <view a:if='{{ inited }}' class='van-transition {{customClass}} {{ classes }}' style="-webkit-transition-duration:{{ currentDuration }}ms; transition-duration:{{ currentDuration }}ms; {{ display ? '' : 'display: none;' }} {{ customStyle }}" onTransitionend='onTransitionEnd' onTap='antmoveAction' data-antmove-tap='dealTap'>
    <slot>
    </slot>
  </view>
</view>