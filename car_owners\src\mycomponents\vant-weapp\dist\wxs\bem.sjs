var antmove_export = {};
import antmove_1_module from './array.sjs';
import antmove_2_module from './object.sjs';
var array = antmove_1_module;
var object = antmove_2_module;
var PREFIX = 'van-';

function join(name, mods) {
  name = PREFIX + name;
  mods = mods.map(function (mod) {
    return name + '--' + mod;
  });
  mods.unshift(name);
  return mods.join(' ');
}

function traversing(mods, conf) {
  if (!conf) {
    return;
  }

  if (typeof conf === 'string' || typeof conf === 'number') {
    mods.push(conf);
  } else if (array.isArray(conf)) {
    conf.forEach(function (item) {
      traversing(mods, item);
    });
  } else if (typeof conf === 'object') {
    object.keys(conf).forEach(function (key) {
      conf[key] && mods.push(key);
    });
  }
}

function bem(name, conf) {
  var mods = [];
  traversing(mods, conf);
  return join(name, mods);
}

antmove_export.bem = bem;
export default antmove_export;