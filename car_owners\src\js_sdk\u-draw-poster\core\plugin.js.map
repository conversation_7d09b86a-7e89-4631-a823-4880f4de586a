{"version": 3, "file": "plugin.js", "sourceRoot": "", "sources": ["../../packages/core/plugin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAA;AAgCvD;;;;GAIG;AACH,MAAM,gBAAgB,GAAG,CAAC,OAA2B,EAAE,GAAG,IAAW,EAAE,EAAE;IACvE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;KAChE;IACD,IAAI,QAAQ,GAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,CAAA;IAC7C,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACvB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;KAC3B;IACD,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,QAAQ,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;KACzC;IACD,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,QAAQ,GAAQ,IAAI,CAAC,CAAC,CAAC,CAAA;KACxB;IACD,IAAI,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;QACzE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACtB,OAAO,QAAQ,CAAA;KAChB;IACD,OAAO,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;AAC1C,CAAC,CAAA;AAED,MAAM,aAAa,GAAuB,EAAE,CAAA;AAE5C,MAAM,CAAC,MAAM,SAAS,GAAkB,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,gBAAgB,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAA;AAEpG,MAAM,OAAO,OAAO;IAMC;IALnB,QAAQ,GAAuB,EAAE,CAAA;IACjC,IAAI,OAAO;QACT,OAAO,CAAC,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7C,CAAC;IAED,YAAmB,EAA6B;QAA7B,OAAE,GAAF,EAAE,CAA2B;QAC9C,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO;YAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACvE,CAAC;IAED,GAAG,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;YAAE,MAAM,EAAE,OAAO,EAAE,CAAM,IAAI,CAAC,EAAE,CAAC,CAAA;IACxD,CAAC,CAAA;IAED,GAAG,GAAG,CAAC,aAAyC,EAAE,EAAE;QAClD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACjC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAM,IAAI,CAAC,EAAE,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;CACF"}