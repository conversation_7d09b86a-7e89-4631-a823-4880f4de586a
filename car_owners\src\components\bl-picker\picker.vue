<template>
    <view class="bl-picker">
		<view class="bl-flex">
			<button type="default" class="bl-picker--button bl-picker--plain" @click="_handleCanel">取消</button>
			<view class="bl-picker--title">{{title}}</view>
			<button type="default" class="bl-picker--button bl-picker--primary" @click="_handleConfirm">确定</button>
		</view>
		<view class="bl-flex bl-picker--header">
			<view class="header-item" v-for="item in headerList" :key="item">{{item}}</view>
		</view>
        <picker-view
		 :key="count"
		 :indicator-style="indicatorStyle"
		 :value="value"
		 @pickstart="bindPickStart"
		 @change="bindChange"
		 @pickend="bindPickEnd"
		 class="bl-picker-view">
            <picker-view-column v-for="key in column_keys" :key="key" >
				<block v-if="type === 'custom'">
					<view class="bl-picker-view--item" v-for="(item,index) in columnData[key]" :key="index">{{item[value_name]}}</view>
				</block>
				<block v-if="type === 'time'">
					<view class="bl-picker-view--item"  v-for="(item,index) in columnData[key]" :key="index">{{item}}</view>
				</block>
            </picker-view-column>
        </picker-view>
    </view>
</template>
<script>
    export default {
		props: {
			// header item
			headerList: {
				type: Array,
				default(){
					return []
				}
			},
			// picker 类型
			type: {
				type: String,
				default: "custom"
			},
			// 标题
			title: String,
			// picker 的元数据
			listData: {
				type: Array,
				default(){
					return []
				}
			},
			value_name: {
				type: String,
				default: 'name'
			},
			value_no: {
				type: String,
				default: 'id'
			},
			// picker级别，默认3级联动
			level: {
				type: Number,
				default: 3
			}
		},
        data() {
			const hours = [],minutes=[];
			for(let i=0;i<24;i++) {
				let t = i<10?'0'+i:i
				hours.push(t)
			}
			for(let i=0;i<60;i++) {
				let t = i<10?'0'+i:i
				minutes.push(t)
			}
            return {
				//每次更新数据后，强制刷新
				count: 0,
				// 滚动有延时，滚动结束前，不允许提交
				is_can_submit: true,
				hours,
				minutes,
                value: [0,0,0],
                visible: true,
                indicatorStyle: `height: 45px;`,
				columnData:{},
				column_level_1:[],
				column_level_2:[],
				column_level_3:[],
				column_keys:["hours", "minutes"]
            }
        },
		watch: {
			listData: {
				handler(n) {
					this.count++;
					this._formatMetadata(n)
				},
				immediate: true,
			},
			type: {
				handler(val) {
					if (val === 'custom') {
						let arr = [];
						for (let i=0;i<this.level;i++) {
							arr.push('column_level_'+i);
						}
						this.column_keys = arr;
						this.value = Array.apply(null, Array(this.level)).map(()=>0);
					}
					else if(val === 'time') {
						this.columnData = {
							hours: this.hours,
							minutes: this.minutes
						}
						this.column_keys = ["hours", "minutes"];
						this.value = [0, 0];
					}
				},
				immediate: true,
			},
		},
        methods: {
			_handleConfirm() {
				if(!this.is_can_submit){return false}
				if (this.type === "custom") {
					let data = [],i=0;
					formatData(this.listData, 0,this);
					this.$emit("confirm",{
						detail: data
					})
					
					function formatData(row, i,self) {
						let maxLen = self.level -1;
						let e =  self.value[i];
						let value = row[e];
						let isNotEmpty =value && value['children'] && value['children'].length > 0 ;
						data.push({
							id: value[self.value_no],
							name: value[self.value_name],
							rows: value.rows || {}
						})
						if (isNotEmpty && i < maxLen) {
							formatData(value.children,++i,self);
						}
					}
					
				}
				else if (this.type === 'time') {
					this.$emit("confirm",{
						detail: this.value
					})
				}
				
			},
			_handleCanel() {
				this.$emit("cancel",false)
			},
			_formatMetadata(data) {
				if (this.type === 'custom' && data) {
					this.$set(this.columnData,'column_level_0', data);
					if (data[0] && data[0].children) {
						this.$set(this.columnData,'column_level_1', data[0].children);
						if (data[0].children[0] && data[0].children[0].children) {
							this.$set(this.columnData,'column_level_2', data[0].children[0].children);
						}
					}
				}
			},
			bindPickStart(e) {
				this.is_can_submit = false;
			},
			bindPickEnd(e) {
				this.is_can_submit = true;
			},
            bindChange(e) {
				this.is_can_submit = true;
				const arr = e.detail.value;
				if (this.type === "custom") {
					// 先遍历判断，是那一级有改变 level
					let index = 0,level = 0;
					for (let i=0; i < arr.length; i++) {
						if (arr[i] !== this.value[i]) {
							index = i;
							level = i;
							break;
						}
					}
					
					// 当前改变的值
					let currentVal = arr[index];
					let data = this.columnData[`column_level_${index}`][currentVal];
					if (data && data.children) {
						assembleData(data.children,level,this);
					}
					// index为0时，说明动了第一级别的内容，其余项要重置
					if (index === 0) {
						this.value = [currentVal, 0, 0];
					}
					else if(index === 1){
						if (this.level>2) {
							this.value[1] = currentVal;
							this.value[2] = 0;
						}else {
							this.value = arr;
						}
					}
					else {
						this.value = arr;
					}
				}
				else if (this.type === 'time') {
					this.value = arr;
				}
				function assembleData(data,level,self) {
					level++;
					if (data) {
						self.columnData[`column_level_${level}`] = data;
						if (data[0] && data[0].children && data[0].children.length > 0) {
							assembleData(data[0].children, level, self)
						}
					}
						
				}
                
            }
        }
    }
</script>
<style lang="less">
	.bl-flex {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.bl-picker {
		padding: 42rpx 50rpx;
		background-color: #fff;
		&--header {
			padding: 50rpx 0 20rpx 0;
			.header-item {
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #b0b6c1;
			}
		}
		&--title {
			flex: 1;
			height: 64rpx;
			line-height: 64rpx;
			text-align: center;
			font-weight: 700;
			font-size: 30rpx;
			color: #666666;
			
		}
		&--button {
			padding: 0 34rpx;
			height: 64rpx;
			line-height: 64rpx;
			font-size: 30rpx;
			font-weight: 500;
		}
		&--plain {
			background-color: transparent;
			color: #3970F0 !important;
			border: 2rpx solid #3970F0;
			border-radius: 47rpx;
		}
		&--primary {
			background: linear-gradient(293deg,#3663f4 0%, #468cfe);
			border-radius: 55px;
			color: #fff !important;
		}
	}
    .bl-picker-view {
        height: 490rpx;
    }
	.bl-picker-view--item {
		line-height: 45px;
		text-align: center;
	}
</style>
