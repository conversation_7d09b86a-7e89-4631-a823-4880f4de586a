<view class='card-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='{{customClass}} van-card'>
    <view class="{{ utils.bem('card__header', { center: centered }) }}">
      <view class='van-card__thumb' onTap='antmoveAction' data-antmove-tap='onClickThumb'>
        <image a:if='{{ thumb }}' src='{{ thumb }}' mode='{{ thumbMode }}' lazy-load='{{ lazyLoad }}' class='van-card__img {{thumbClass}}'>
        </image>
        <slot a:else  name='thumb'>
        </slot>
        <van-tag a:if='{{ tag }}' mark=" " type='danger' custom-class='van-card__tag' ref='saveChildRef1'>
          {{ tag }}
        </van-tag>
        <slot a:else  name='tag'>
        </slot>
      </view>
      <view class="van-card__content {{ utils.bem('card__content', { center: centered }) }}">
        <view>
          <view a:if='{{ title }}' class='van-card__title {{titleClass}}'>
            {{ title }}
          </view>
          <slot a:else  name='title'>
          </slot>
          <view a:if='{{ desc }}' class='van-card__desc {{descClass}}'>
            {{ desc }}
          </view>
          <slot a:else  name='desc'>
          </slot>
          <slot name='tags'>
          </slot>
        </view>
        <view class='van-card__bottom'>
          <slot name='price-top'>
          </slot>
          <view a:if='{{ price || price === 0 }}' class='van-card__price {{priceClass}}'>
            <text>
              {{ currency }}            </text>            <text class='van-card__price-integer'>
              {{ integerStr }}            </text>            <text class='van-card__price-decimal'>
              {{ decimalStr }}            </text>
          </view>          <slot a:else  name='price'>
          </slot>
          <view a:if='{{ originPrice || originPrice === 0 }}' class='van-card__origin-price {{originPriceClass}}'>
            {{ currency }} {{ originPrice }}
          </view>
          <slot a:else  name='origin-price'>
          </slot>
          <view a:if='{{ num }}' class='van-card__num {{numClass}}'>
            x {{ num }}
          </view>
          <slot a:else  name='num'>
          </slot>
          <slot name='bottom'>
          </slot>
        </view>
      </view>
    </view>    <view class='van-card__footer'>
      <slot name='footer'>
      </slot>
    </view>
  </view>
</view>