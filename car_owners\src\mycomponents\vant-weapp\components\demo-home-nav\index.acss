.demo-home-nav-index {
    display: block;
    height: initial;
}
.demo-home-nav-index .demo-home-nav__title {
    margin: 24px 0 8px 16px;
    color: rgba(69, 90, 100, 0.6);
    font-size: 14px;
}

.demo-home-nav-index .demo-home-nav__block {
    position: relative;
    display: flex;
    margin: 0 0 12px;
    padding-left: 20px;
    color: #323233;
    font-weight: 500;
    font-size: 14px;
    line-height: 40px;
    background: #f7f8fa;
    border-radius: 99px;
    transition: background 0.3s;
}

.demo-home-nav-index .demo-home-nav__block:hover {
    background: darken(#f7f8fa, 3%);
}

.demo-home-nav-index .demo-home-nav__block:active {
    background: darken(#f7f8fa, 6%);
}

.demo-home-nav-index .demo-home-nav__icon {
    position: absolute !important;
    top: 50%;
    right: 16px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    color: rgb(182, 195, 210);
    font-weight: 900 !important;
}
