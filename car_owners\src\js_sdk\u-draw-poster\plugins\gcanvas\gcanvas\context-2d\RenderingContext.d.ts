export default class CanvasRenderingContext2D {
    _drawCommands: string;
    _globalAlpha: number;
    _fillStyle: string;
    _strokeStyle: string;
    _lineWidth: number;
    _lineCap: string;
    _lineJoin: string;
    _miterLimit: number;
    _globalCompositeOperation: string;
    _textAlign: string;
    _textBaseline: string;
    _font: string;
    _savedGlobalAlpha: any[];
    timer: any;
    componentId: any;
    _notCommitDrawImageCache: any[];
    _needRedrawImageCache: any[];
    _redrawCommands: string;
    _autoSaveContext: boolean;
    className: string;
    setFillStyle(value: any): void;
    set fillStyle(arg: string);
    get fillStyle(): string;
    set globalAlpha(arg: number);
    get globalAlpha(): number;
    setGlobalAlpha(value: any): void;
    set strokeStyle(arg: string);
    get strokeStyle(): string;
    setStrokeStyle(value: any): void;
    set lineWidth(arg: number);
    get lineWidth(): number;
    setLineWidth(value: any): void;
    set lineCap(arg: string);
    get lineCap(): string;
    setLineCap(value: any): void;
    set lineJoin(arg: string);
    get lineJoin(): string;
    setLineJoin(value: any): void;
    set miterLimit(arg: number);
    get miterLimit(): number;
    setMiterLimit(value: any): void;
    set globalCompositeOperation(arg: string);
    get globalCompositeOperation(): string;
    set textAlign(arg: string);
    get textAlign(): string;
    setTextAlign(value: any): void;
    set textBaseline(arg: string);
    get textBaseline(): string;
    setTextBaseline(value: any): void;
    set font(arg: string);
    get font(): string;
    setFontSize(size: any): void;
    setTransform(a: any, b: any, c: any, d: any, tx: any, ty: any): void;
    transform(a: any, b: any, c: any, d: any, tx: any, ty: any): void;
    resetTransform(): void;
    scale(a: any, d: any): void;
    rotate(angle: any): void;
    translate(tx: any, ty: any): void;
    save(): void;
    restore(): void;
    createPattern(img: any, pattern: any): FillStylePattern;
    createLinearGradient(x0: any, y0: any, x1: any, y1: any): FillStyleLinearGradient;
    createRadialGradient: (x0: any, y0: any, r0: any, x1: any, y1: any, r1: any) => FillStyleRadialGradient;
    createCircularGradient: (x0: any, y0: any, r0: any) => FillStyleRadialGradient;
    strokeRect(x: any, y: any, w: any, h: any): void;
    clearRect(x: any, y: any, w: any, h: any): void;
    clip(): void;
    resetClip(): void;
    closePath(): void;
    moveTo(x: any, y: any): void;
    lineTo(x: any, y: any): void;
    quadraticCurveTo(cpx: any, cpy: any, x: any, y: any): void;
    bezierCurveTo(cp1x: any, cp1y: any, cp2x: any, cp2y: any, x: any, y: any): void;
    arcTo(x1: any, y1: any, x2: any, y2: any, radius: any): void;
    beginPath(): void;
    fillRect(x: any, y: any, w: any, h: any): void;
    rect(x: any, y: any, w: any, h: any): void;
    fill(): void;
    stroke(path: any): void;
    arc(x: any, y: any, radius: any, startAngle: any, endAngle: any, anticlockwise: any): void;
    fillText(text: any, x: any, y: any): void;
    strokeText(text: any, x: any, y: any): void;
    measureText(text: any): any;
    isPointInPath: (x: any, y: any) => never;
    drawImage(image: any, sx: any, sy: any, sw: any, sh: any, dx: any, dy: any, dw: any, dh: any): void;
    __drawImage(image: any, sx: any, sy: any, sw: any, sh: any, dx: any, dy: any, dw: any, dh: any, ...args: any[]): void;
    _flush(reserve: any, callback: any): void;
    _needRender: boolean | undefined;
    _redrawflush(reserve: any, callback: any): void;
    draw(reserve: any, callback: any): void;
    getImageData(x: any, y: any, w: any, h: any, callback: any): void;
    putImageData(data: any, x: any, y: any, w: any, h: any, callback: any): void;
    toTempFilePath(x: any, y: any, width: any, height: any, destWidth: any, destHeight: any, fileType: any, quality: any, callback: any): void;
}
import FillStylePattern from "./FillStylePattern";
import FillStyleLinearGradient from "./FillStyleLinearGradient";
import FillStyleRadialGradient from "./FillStyleRadialGradient";
