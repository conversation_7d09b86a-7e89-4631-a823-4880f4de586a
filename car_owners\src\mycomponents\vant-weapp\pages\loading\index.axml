<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block title='加载类型' padding=" " ref='saveChildRef1'>
    <van-loading custom-class='demo-loading' ref='saveChildRef2'>
    </van-loading>
    <van-loading custom-class='demo-loading' type='spinner' ref='saveChildRef3'>
    </van-loading>
  </demo-block>
  <demo-block title='自定义颜色' padding=" " ref='saveChildRef4'>
    <van-loading custom-class='demo-loading' color='#1989fa' ref='saveChildRef5'>
    </van-loading>
    <van-loading custom-class='demo-loading' type='spinner' color='#1989fa' ref='saveChildRef6'>
    </van-loading>
  </demo-block>
  <demo-block title='加载文案' padding=" " ref='saveChildRef7'>
    <van-loading custom-class='demo-loading' size='24px' ref='saveChildRef8'>
      加载中...
    </van-loading>
  </demo-block>
  <demo-block title='垂直排列' padding=" " ref='saveChildRef9'>
    <van-loading custom-class='demo-loading' size='24px' vertical=" " ref='saveChildRef10'>
      加载中...
    </van-loading>
  </demo-block>
</view>