<template>
    <view @tap.stop="close">
        <canvas
            class="my-canvas"
            canvas-id="myCanvas"
            id="myCanvas"
            :style="{'width': `${poster.width}px`, 'height': `${poster.height}px`}"/>
        <popup  
            :is-show="isShow"
            animate-type="middle">
            <view class="poster">
                <view class="poster-img">
                    <image class="img" :src="posterImg" />
                </view>
				<view class="poster-btns">
					<view class="poster-btn poster-save" @tap.stop="saveImage">保存图片</view>
					<button open-type="share">
						<view class="poster-btn poster-share">分享好友</view>
					</button>
				</view>
                
            </view>
        </popup>
    </view>
</template>

<script>
	import popup from '../popup/popup.vue'
    import { saveImageToPhotosAlbum, showToast, downloadFile } from '../sakura-canvas/js_sdk/util'
    import Draw from '../sakura-canvas/js_sdk/draw'
	let draw = null
	export default {
		components:{
			popup
		},
		data() {
			return {
                isShow: false,
				poster: {},
                posterImg: '',
                canvasId: 'myCanvas',
				img: 'https://image.bolink.club/FsjnvXXG9k6qEcl4xHRgyacmjQEx',
			}
		},

        async created() {
        },
		
		methods: {
            // 生成分享图
			/**
			 * @param {String} img 
			 * @param {Object} data
			 */
            async createdPoster(img,data) {
				
                let { canvasId } = this;
				
                draw = new Draw({
                    width: 750,
					height: 1334,
					canvasId,
					_this: this,
                })
                let res = await draw.createdSharePoster(({ bgObj }) => {
                    let { width, height } = bgObj;
                    this.poster = bgObj;
					data.unshift({ type: 'image', x: 0, y: 0,
					src: img, w: width, h: height});
                    return data;
                })
                console.log('res', res)
                if (!res.success) return
                this.posterImg = res.data;
				this.$emit("change", this.posterImg);
                this.isShow = true
            },

            // 保存图片
            async saveImage() {
                let { posterImg } = this
                let res = await saveImageToPhotosAlbum(posterImg)
				if (!res.success) return
                showToast('保存成功')
				this.isShow = false
            },
			// 分享好友
			shareFriend() {
				
			},
            close() {
                this.isShow = false
            }
		},
	}
</script>

<style lang="scss" scoped>

    .demo{
        width: 700rpx;
        height: 400rpx;
        background-color: pink;
    }

    view, image{
        box-sizing: border-box;
    }
    .my-canvas{
        position: fixed;
        top: -99999999999rpx;
        left: -99999999999rpx;
        z-index: -99999999999;
        opacity: 0;
    }
    .poster{
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
		align-items: center;
        overflow: hidden;
        &-img{
   //          width: 85.07vw;
			// height: 59.4vh;
			width: 638rpx;
			height: 1064rpx;
            position: relative;
			margin: 0 auto;
			border-radius: 40rpx;
			overflow: hidden;
            .img{
				margin: 0;
				padding: 0;
                width: 100%;
				height: 100%;
            }
        }
		&-btns {
			margin: 104rpx auto 0;
			display: flex;
			width: 85.07vw;
			height: 96rpx;
			justify-content: space-between;
		}
        &-btn{
			width: 280rpx;
            height: 96rpx;
            border-radius: 77rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            // &:last-child{
            //     margin-left: 79rpx;
            // }
        }
		&-save {
			background-color: #FFFFFF;
			color: #488AFF;
		}
        &-share{
			background: linear-gradient(287deg,#517aff 0%, #468cfe) #517aff;
            color: #fff;
        }
    }

</style>
