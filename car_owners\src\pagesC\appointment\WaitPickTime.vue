<template>
	<view class="flex100">
		<view class="date-select">
			<view class="bl-flex mar-30">
				<button type="default" class="bl-picker--button bl-picker--plain" @click="handleCanel">取消</button>
				<view class="bl-picker--title">{{title}}</view>
				<button type="default" class="bl-picker--button bl-picker--primary" @click="handleConfirm">确定</button>
			</view>
			<picker-view v-if="showPick" class="time-picker-group" :value="value" :indicator-style="indicatorStyle"
				@change="bindChange">
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in years" :key="index">{{item}}年</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in 12" :key="index">{{index+1}}月</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in days" :key="index">{{index + 1}}日</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in 24" :key="index">{{index}}时</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in minutes" :key="index">{{item}}分</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: '请选择',
			},
			defaultTime: {
				type: Number,
				default: new Date().getTime()
			},
		},
		data() {
			return {
				years: [],
				months: [],
				days: 31,
				minutes: ['00', '30'],
				value: [0,0,0,0,0],
				indicatorStyle: 'height: 50px',
				selectTime: '',
				showPick: false,
			}
		},
		watch: {
			'defaultTime':{
				immediate: true,
				deep: true,
				handler: function(newVal, oldVal){
					this.showPick = false
					this.init(newVal)
				}
			}
		},
		onShow() {
			
		},
		methods: {
			init(time){
				const date = new Date(time || new Date())
				const years = []
				const year = new Date().getFullYear()
				const months = []
				const month = date.getMonth() + 1
				const day = date.getDate()
				const days = this.getDays(month,year)
				for (let i = 0; i <= 1; i++) {
					years.push(Number(year) + i)
				}
				for (let i = 1; i <= 12; i++) {
					months.push(i)
				}
				this.years = years;
				this.months = months;
				this.days = days;
				let minute = this.minutes.length != 2 ? date.getMinutes() : date.getMinutes() == 30 ? 1 : 0
 				this.value = [0, month - 1, day - 1, date.getHours(), minute]
				setTimeout(()=>{
					this.showPick = true
				},100)
			},
			getDays(month,year){
			  if(!month || !year){
				return 31
			  }
			  if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
				return 31
			  } else if (month == 4 || month == 6 || month == 11 || month == 9) {
				return 30
			  } else if (month == 2) {
				if (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) {
				  return 29
				} else {
				  return 28
				}
			  }
			  return 31
			},
			bindChange(e) {
				let a;
				let y;
				let value = e.detail.value;
				y = this.years[value[0]];
				a = this.months[value[1]];
				this.days = this.getDays(a,y);
				console.log(this.days)
				this.value = value
				console.log(this.selectTime)
			},
			handleConfirm() {
				let value = this.value
				let year = this.years[value[0]]
				let month = this.months[value[1]]
				let day = value[2] + 1
				let hour = value[3]
				let minute = this.minutes[value[4]]
				console.log(value,this.years,this.days,year,month,day,hour,minute)
				this.selectTime = new Date(`${year}/${month}/${day} ${hour}:${minute}:00`).getTime()
				let time = this.selectTime
				let flag = time < new Date().getTime()
				if (flag) {
					uni.showToast({
						title: '选择的时间不能小于当前时间',
						icon: "none"
					})

				} else {
					let data = {
						time: String(time).substring(0, 10)
					}
					this.$emit("timeConfirm", data)
				}
			},
			handleCanel() {
				this.$emit("timeCancel", false)
			},
		}
	}
</script>

<style scoped lang="scss">
	.date-select {
		width: 100%;
		height: 700rpx;
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		background-color: #FFFFFF;
		position: relative;

		.time-picker-group {
			background-color: #FFF;
			// position: absolute;
			width: 100%;
			height: 500rpx;
			bottom: 20rpx;

			.time-item {
				height: 50px;
				line-height: 50px;
				font-size: 34rpx;
				text-align: center;
			}
		}

		.bl-picker {
			padding: 42rpx 50rpx;
			background-color: #fff;

			&--header {
				padding: 50rpx 0 20rpx 0;

				.header-item {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #b0b6c1;
				}
			}

			&--title {
				flex: 1;
				height: 64rpx;
				line-height: 64rpx;
				text-align: center;
				font-weight: 700;
				font-size: 30rpx;
				color: #666666;

			}

			&--button {
				padding: 0 34rpx;
				height: 64rpx;
				line-height: 64rpx;
				font-size: 30rpx;
				font-weight: 500;
			}

			&--plain {
				background-color: transparent;
				color: #3970F0 !important;
				border: 2rpx solid #3970F0;
				border-radius: 47rpx;
			}

			&--primary {
				background: linear-gradient(293deg, #3663f4 0%, #468cfe);
				border-radius: 55px;
				color: #fff !important;
			}
		}

	}
</style>
