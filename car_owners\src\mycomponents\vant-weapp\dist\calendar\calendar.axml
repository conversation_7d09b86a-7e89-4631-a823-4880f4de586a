<import-sjs from='./index.sjs' name='computed'>
</import-sjs><template name='calendar' ref='saveChildRef0'>
  <view class='van-calendar'>
    <header title='{{ title }}' showTitle='{{ showTitle }}' subtitle='{{ subtitle }}' showSubtitle='{{ showSubtitle }}'>
      <slot name='title' slot='title'>
      </slot>
    </header>
    <scroll-view class='van-calendar__body' scroll-y=" " scroll-into-view='{{ scrollIntoView }}'>
      <month a:for='{{ computed.getMonths(minDate, maxDate) }}' a:key='{{index}}' id='month{{ index }}' class='month' data-date='{{ item }}' date='{{ item }}' type='{{ type }}' color='{{ color }}' minDate='{{ minDate }}' maxDate='{{ maxDate }}' showMark='{{ showMark }}' onFormatter='onFormatter' rowHeight='{{ rowHeight }}' currentDate='{{ currentDate }}' showSubtitle='{{ showSubtitle }}' allowSameDay='{{ allowSameDay }}' showMonthTitle="{{ typeof index === 'number' || !showSubtitle }}" ref-numbers='{{ computed.getMonths(minDate, maxDate) }}' onClick='onClickDay'>
      </month>
    </scroll-view>
    <view class="van-calendar__footer {{ safeAreaInsetBottom ? 'van-calendar__footer--safe-area-inset-bottom' : '' }}">
      <slot name='footer'>
      </slot>
    </view>
    <view class="van-calendar__footer {{ safeAreaInsetBottom ? 'van-calendar__footer--safe-area-inset-bottom' : '' }}">
      <van-button a:if='{{ showConfirm }}' round=" " block=" " type='danger' color='{{ color }}' custom-class='van-calendar__confirm' disabled='{{ computed.getButtonDisabled(type, currentDate) }}' nativeType='text' onClick='onConfirm'>
        {{ computed.getButtonDisabled(type, currentDate) ? confirmDisabledText : confirmText }}
      </van-button>
    </view>
  </view>
</template>