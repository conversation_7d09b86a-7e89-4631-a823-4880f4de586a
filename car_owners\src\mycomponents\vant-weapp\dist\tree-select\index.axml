<view class='tree-select-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='wxs'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-tree-select' style='height: {{ utils.addUnit(height) }}'>
    <scroll-view scroll-y=" " class='van-tree-select__nav'>
      <van-sidebar active-key='{{ mainActiveIndex }}' custom-class='van-tree-select__nav__inner' onChange='onClickNav' ref='saveChildRef1'>
        <van-sidebar-item a:for='{{ items }}' a:key='{{*this}}' custom-class='{{mainItemClass}}' active-class='{{mainActiveClass}}' disabled-class='{{mainDisabledClass}}' badge='{{ item.badge }}' dot='{{ item.dot }}' title='{{ item.text }}' disabled='{{ item.disabled }}' ref-numbers='{{ items }}' ref='saveChildRef2'>
        </van-sidebar-item>
      </van-sidebar>
    </scroll-view>
    <scroll-view scroll-y=" " class='van-tree-select__content'>
      <slot name='content'>
      </slot>
      <view a:for='{{ subItems }}' a:key='{{id}}' class="van-ellipsis {{contentItemClass}} {{ utils.bem('tree-select__item', { active: wxs.isActive(activeId, item.id), disabled: item.disabled }) }} {{ wxs.isActive(activeId, item.id) ? 'content-active-class' : '' }} {{ item.disabled ? 'content-disabled-class' : '' }}" data-item='{{ item }}' ref-numbers='{{ subItems }}' onTap='antmoveAction' data-antmove-tap='onSelectItem'>
        {{ item.text }}        <van-icon a:if='{{ wxs.isActive(activeId, item.id) }}' name='{{ selectedIcon }}' size='16px' class='van-tree-select__selected' ref='saveChildRef3'>
        </van-icon>
      </view>
    </scroll-view>
  </view>
</view>