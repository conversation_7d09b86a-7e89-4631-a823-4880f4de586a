const plugin = requirePlugin('xlightPlugin'); // 引入插件的实例


Component({
  data() {
    return {
      interstitialAd: null,
      // spaceCode: '50_2023102325000067512'
    }
  },
  props: {
    onAdLoad: () => { },
    onClose: () => { },
    onAdError: () => { },
    onSuccess: () => { },
	onInsuranceStatus:()=>{},
	spaceCode:String,
  },
  onInit() {
    // 实例化
    const interstitialAd = new plugin.CreateInterstitialAd();
    // 将实例放入data中
    this.setData({
      interstitialAd,
    });

    // 广告插件加载成功
    this.data.interstitialAd.onLoad(() => {
		console.log('初始化广告',this.props.spaceCode,interstitialAd)
		this.props.onAdLoad(interstitialAd, this.props.spaceCode)

    });

    // 广告加载成功
    this.data.interstitialAd.onSuccess(() => {
      console.log('广告组件-广告加载成功')
      this.props.onSuccess()
    });

    // 广告关闭
    this.data.interstitialAd.onClose(() => {
      console.log('广告组件-广告关闭')
      this.props.onClose()
    });

    // 广告加载失败
    this.data.interstitialAd.onError((err) => {
      console.log('广告组件-广告加载失败',err)
      this.props.onAdError(err)
    });
  },
  methods:{
	  onInsuranceStatus(res){
	    console.log('广告组件-广告结束',res)
		this.props.onInsuranceStatus(res)
	  }
  }
});