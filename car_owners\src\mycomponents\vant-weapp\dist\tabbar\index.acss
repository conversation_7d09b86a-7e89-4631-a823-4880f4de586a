.tabbar-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.tabbar-index .van-tabbar {
    display: -webkit-flex;
    display: flex;
    box-sizing: initial;
    width: 100%;
    height: 50px;
    height: var(--tabbar-height, 50px);
    background-color: #fff;
    background-color: var(--tabbar-background-color, #fff);
}

.tabbar-index .van-tabbar--fixed {
    position: fixed;
    bottom: 0;
    left: 0;
}

.tabbar-index .van-tabbar--safe {
    padding-bottom: env(safe-area-inset-bottom);
}
