<template>
	<view :style="{position: 'fixed',top: (statusBarHeight+60)+'px'}" class="guide-cont">
		<view class="guide-box flex-row alc">
			<view class="guide-pointto">
				<image style="width: 55rpx;height: 27rpx;" src="https://image.bolink.club/yima/guide-point-to.png"></image>
			</view>
			<view style="margin-left: 40rpx;">
				<image style="width: 112rpx;height: 182rpx;" src="https://image.bolink.club/yima/guide-people.png"></image>
			</view>
			<view style="140rpx"></view>
			<view class="flex-row flex-1 alc jue marl-20 marr-20">
				<view class="cor-6 font-28" style="width: 395rpx;">{{content}}</view>
				<view class="marl-10 marr-10" @click="close" style="padding: 40rpx 40rpx 30rpx 0;">
					<van-icon name="cross" size="24rpx" color="#c4c4c4" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			content: {
				type: String,
				default: '',
			},
		},
		data() {
			return {
				windowHeight: null,
				statusBarHeight: null,
				navHeight: null,
				screenWidth: null,
			};
		},
		created() {
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
				this.screenWidth = res.screenWidth;
			});
		},
		methods: {
			close () {
				this.$emit("close");
			},
			jumpToDis () {
				uni.navigateTo({
					url: '/pages/activity/dis',
					complete: (res) => {
						getApp().eventRecord({
							keyWord: '点击吐槽图标-跳转',
							clickType: '图片',
							jumpType: '本程序页面',
							jumpDesc: '吐槽页面',
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: new Date().getTime(),
							endTime: new Date().getTime()
						});
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.guide-cont {
		z-index: 1000;
		width: 100%;
	}
	.guide-box {
		z-index: 1000;
		width: 620rpx;
		height: 140rpx;
		position: absolute;
		top: 0;
		right: 34rpx;
		border-radius: 100rpx;
		background: linear-gradient(90deg,#d3eaff 0%, #ffffff 100%);
		box-shadow: 0px 4rpx 16rpx 0px rgba(0,0,0,0.12); 
	}
	.guide-pointto {
		z-index: 1000;
		position: absolute;
		top: -32rpx;
		right: 90rpx
	}
</style>
