<template>
	<view class="ticket-item flex-row">
		<view class="ticket-msg flex-1">
			<view class="ticket-content">
				<view class="img-box">
<!--					<image class="img" src="../../static/privilege/ticket.png" mode=""></image>-->
					<image class="img" src="https://image.bolink.club/ticket.png" mode=""></image>

				</view>
				<view class="message">
					<view class="price flex-row alc">
						<span>{{message.discount}}元停车券</span>
						<span class="recommend">推荐</span>
					</view>
					<view class="use-limit">满{{message.useLimit}}元可用</view>
				</view>
			</view>
			<view class="ticket-tip">立即领券，停车更优惠</view>
		</view>
		<view class="flex-row alc jue free-collection">免 费 领 取 </view>
	</view>
</template>

<script>
	export default{
		// name: parkTicket,
		props:{
			message:{
				type: Object,
				default: ()=>{
					return {
						discount: 2,
						useLimit: 10.01,
					}
				}
			}
		},
	}
</script>

<style  lang="less" scoped>
	.ticket-item{
		width: 100%;
		height: 267rpx;
		display: inline-flex;
		justify-content: left;
		margin: 0 34rpx 34rpx 34rpx;
	}
	.ticket-msg{
		height: 267rpx;
		border-radius: 20rpx;
		box-shadow: 0px 7rpx 28rpx 0px rgba(0,0,0,0.08);
		display: flex;
		flex-direction: column;
	}
	.ticket-content{
		height: 220rpx;
		display: inline-flex;
	}
	.img-box{
		width: 153rpx;
		height: 160rpx;
		margin: 30rpx 26rpx 30rpx 30rpx;
	}
	.img-box .img{
		width: 153rpx;
		height: 160rpx;
	}
	.ticket-content .message{
		display: flex;
		flex-direction: column;
		height: 44rpx;
		line-height: 44rpx;
	}
	.ticket-content .price{
		font-size: 34rpx;
		font-weight: 700;
		color: #333333;
		margin: 55rpx 0 20rpx;
	}
	.recommend{
		display: inline-block;
		width: 70rpx;
		font-size: 22rpx;
		font-weight: 500;
		opacity: 1;
		background: linear-gradient(90deg,#ff5f1e 0%, #ff8a4f 100%);
		border-radius: 20rpx 0 20rpx 0;
		color: #FFFFFF;
		text-align: center;
		margin-left: 14rpx;
	}
	.use-limit{
		margin-top: 18rpx;
		font-size: 28rpx;
		height: 38rpx;
		line-height: 38rpx;
		font-weight: 500;
		color: #999999;
	}
	.ticket-tip{
		height: 47rpx;
		line-height: 47rpx;
		color: #FFFFFF;
		background: linear-gradient(90deg,#355be0, #23e6ce 100%);
		font-size: 22rpx;
		padding-left: 30rpx;
		border-radius: 0 0 20rpx 20rpx;
	}
	.free-collection{
		height: 267rpx;
		line-height: 1.3;
		width: 110rpx;
		background: linear-gradient(359deg,#ff5454 0%, #fe5c2b 100%);
		margin-left: 20rpx;
		margin-right: 60rpx;
		border-radius: 20rpx;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 0 42rpx;
		box-shadow: 0px 6rpx 28rpx 0px rgba(255,86,75,0.50);
	}
</style>
