import http from '../utils/http.js'
import BaseConfig from '../config/index.config.js'
export default {
  userRegister: (e, type = false) => http.request('capp/api/user/userRegister', e, type), // login获取token
  getOpenId: (e, type = false) => http.request('capp/api/user/getOpenId', e, type), // login获取token
  getH5OpenId: (e, type = false) => http.request('capp/api/user/getH5OpenId', e, type), // 新版H5login获取token
  updateUserInfo: (e) => http.request('capp/api/user/updateUserInfo', e), // 更新用户信息
  getcarinfo: (e, type = true, mask = false, loading = false) => http.request('capp/api/car/getcarinfo', e, type, mask, loading), // 车牌信息
  imageUpload: (e) => http.upload('capp/upload/pic', e), // 图片上传
  getCarType: (e) => http.request('capp/api/carenum/getCarType', e), // 车辆类型
  getCarApplication: (e) => http.request('capp/api/carenum/getCarApplication', e), // 车辆使用性质
  driverlicence: (e) => http.request('capp/api/car/driverlicence', e), // 识别行驶证图片
  getcitytree: (e) => http.request('capp/api/carenum/getcitytree', e), // 城市信息
  getbrandnode: (e, type = true, mask = false, loading = false) => http.request('capp/api/carenum/getbrandnode', e, type, mask, loading), // 车辆品牌信息
  savecarinfo: (e) => http.request('capp/api/car/savecarinfo', e), // 保存车辆行驶证信息
  getinformationtype: (e, type = true, mask = true, loading = false) => http.request('capp/api/carenum/getinformationtype', e, type, mask, loading), // 资讯类型
  getInformation: (e, type = true, mask = true, loading = false) => http.request('capp/api/information/getInformation', e, type, mask, loading), // 首页热点资讯
  getInfomationByType: (e, type = true, mask = true, loading = false) => http.request('capp/api/information/getInfomationByType', e, type, mask, loading), // 资讯页信息
  informationviews: (e) => http.request('capp/api/information/views', e), // 资讯详情页日志记录
  getad: (e, type = true, mask = true) => http.request('capp/api/information/getad', e, type, mask), // 获取广告
  getweather: (e, type = true, mask = false, loading = false) => http.request('capp/api/information/getweather', e, type, mask, loading), // 获取天气
  getcode: (e, type = true, mask = false, loading = false) => http.request('capp/api/information/getcode', e, type, mask, loading), // 通过经纬度获取城市
  commonuse: (e) => http.request('capp/api/car/commonuse', e), // 设为常用车和删除车
  subscribe: (e) => http.request('capp/api/msg/subscribe', e), // 订阅通知
  sendmsg: (e) => http.request('capp/api/msg/sendmsg', e), // 通知车主挪车
  getmsglist: (e, type = true, mask = false, loading = false) => http.request('capp/api/msg/getmsglist', e, type, mask, loading), // 消息通知列表
  replymesg: (e) => http.request('capp/api/msg/replymesg', e), // 消息通知里-回复操作
  getweizhang: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/car/weiZhangInfo', e, type, mask, loading, method), // 违章信息
  getcardrivertype: (e) => http.request('capp/api/carenum/getcardrivertype', e), // 准驾车型
  mydriverlicense: (e) => http.request('capp/api/user/mydriverlicense', e), // 我的驾驶证信息
  adddriverlicense: (e) => http.request('capp/api/user/adddriverlicense', e), // 添加修改我的驾驶证
  getnearpark: (e, type = true, mask = false, loading = true) => http.request('capp/api/park/getnearpark', e, type, mask, loading), // 获取附近停车场信息 
  existorder: (e, type = true, mask = false, loading = false) => http.request('capp/api/pay/existorder', e, type, mask, loading), // 获取停车信息
  queryprice: (e) => http.request('capp/api/pay/queryprice', e), // 查询停车费用
  getAnXinAd: (e, type = true, mask = true) => http.request('capp/api/information/getAnXinAd', e, type, mask), // 获取安心出行广告
  payminipay: (e) => http.request('capp/api/pay/minipay', e), // 查询支付信息
  paycallback: (e) => http.request('capp/api/pay/paycallback', e), // 支付完成回调
  queryRecord: (e, type = true) => http.request('capp/api/pay/queryRecord', e, type), // 缴费记录
  querybyplatenumber: (e) => http.request('capp/api/pay/querybyplatenumber', e), // 临停查询
  getProblemInfoList: (e, type = true, mask = false, loading = false) => http.request('capp/api/problem/getProblemInfoList', e, type, mask, loading), // 常见问题
  getnearstation: (e, type = true, mask = true) => http.request('capp/api/oilstation/getnearstation', e, type, mask), // 附近加油站
  getusermobile: (e) => http.request('capp/api/oilstation/getusermobile', e), // 获取手机号
  memberAccountQuery: (e) => http.request('capp/api/oilstation/memberAccountQuery', e), // 会员资产信息
  memberAccountApply: (e) => http.request('capp/api/oilstation/memberAccountApply', e), // 认证接口
  memberAccountInfoCreate: (e) => http.request('capp/api/oilstation/memberAccountInfoCreate', e), // 油卡开通确认接口
  productInfoQuery: (e) => http.request('capp/api/oilstation/productInfoQuery', e), // 充值产品套餐
  preorder: (e) => http.request('capp/api/weixin/preorder', e), // 充值-点击支付
  payQrcodeCreate: (e) => http.request('capp/api/oilstation/payQrcodeCreate', e), // 获取支付二维码的数字串
  payQrcodeCheckQuery: (e, type = true, mask = false, loading = false) => http.request('capp/api/oilstation/payQrcodeCheckQuery', e, type, mask, loading), // 二维码支付状态查询
  orderRefuelListQuery: (e) => http.request('capp/api/oilstation/orderRefuelListQuery', e), // 加油订单
  queryOilCardList: (e) => http.request('capp/api/weixin/queryOilCardList', e), // 充值记录
  forwardUrl: (e) => http.request('capp/api/changyouMarket/forwardUrl', e), // 畅由链接
  getcoupon: (e) => http.request('capp/api/pay/getcoupon', e), // 代金券列表
  getcouponByopenId: (e,type = true, mask = true, loading = false,) => http.request('capp/api/pay/getcouponByopenId', e,type, mask, loading), // 我的代金券列表
  selectPayRecordRankList: (e, type = true, mask = true) => http.request('capp/api/rankanlysis/selectPayRecordRankList', e, type, mask), // 停车榜
  selectCurRanking: (e, type = true, mask = false, loading = false) => http.request('capp/api/rankanlysis/selectCurRanking', e, type, mask, loading), // 我的停车排行
  isShowChangyouBanner: (e) => http.request('capp/api/pay/isShowChangyouBanner', e), // 是否显示移动畅由的banner图
  addUserChannelInfo: (e, type = true, mask = false, loading = false) => http.request('capp/api/channelstatisiccontroller/addUserChannelInfo', e, type, mask, loading), // 记录渠道用户信息
  addEventCollenction: (e, type = true, mask = false, loading = false) => http.request('capp/api/EventCollectionController/addEventCollenction', e, type, mask, loading), // 记录渠道用户信息
  getADInfo: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/cappadinfo/getADInfo', e, type, mask, loading, method), // 广告信息
  getProblemInfoById: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/problem/getProblemInfoById', e, type, mask, loading, method), // 问题详情
  sendByTicketNo: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/bolinkcoupon/sendCouponByTicketNo', e, type, mask, loading, method), // 停车券兑换
  collectADInfo: (e, type = true, mask = false, loading = false) => http.request('capp/api/ADInfoCollectController/collectADInfo', e, type, mask, loading), // 统计广告位
  collectADClick: (e) => http.request('capp/api/ADInfoCollectController/collectADClick', e), // 广告事件记录
  forumAdd: (e) => http.request('capp/api/forum/add', e), // 发送吐槽
  forumFind: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/find', e, type, mask, loading, method), // 查询吐槽列表
	forumGetReplyForum   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/getReplyForum', e, type, mask, loading, method), // 查询吐槽子评论列表
	forumGetReplyList   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/getReplyList', e, type, mask, loading, method), // 查询消息列表
	forumSetIsReadReply   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/setIsReadReply', e, type, mask, loading, method), // 消息设置已读
	forumSetIsviewReply   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/setIsviewReply', e, type, mask, loading, method), // 消息删除
	forumGetReplyLocation   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/getReplyLocation', e, type, mask, loading, method), // 获取置顶
	forumDelete   : (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/forum/delete', e, type, mask, loading, method), // 获取置顶
  isnewuser: (e) => http.request('capp/api/pingan/isnewuser', e), // 平安银行-活动资格查询
  getcheckcode: (e) => http.request('capp/api/pingan/getcheckcode', e), // 平安银行-获取验证码
  bankpic: (e) => http.upload('capp/upload/bankpic', e), // 平安银行-银行卡识别
  openthirdacct: (e) => http.request('capp/api/pingan/openthirdacct', e), // 平安银行-开通账户
  commonTransferIn: (e) => http.request('capp/api/pingan/commonTransferIn', e), // 平安银行-支付
  getPinganUserInfo: (e) => http.request('capp/api/pingan/getPinganUserInfo', e), // 平安银行-获取用户信息
  operasendcoupon: (e) => http.request('capp/api/pingan/operasendcoupon', e), // 平安银行-点击按钮发券接口
  transferInResultQuery: (e, type = true, mask = false, loading = false) => http.request('capp/api/pingan/transferInResultQuery', e, type, mask, loading), // 平安银行-轮询支付状态
  sharecoupon: (e, type = true, mask = true, loading = true, method = 'POST', isCompleteUrl = true) => http.request('https://12123.bolink.club/couponplatform/coupon/sharecoupon', e, type, mask, loading, method, isCompleteUrl), // 分享停车券
  getsharecoupon: (e, type = true, mask = true, loading = true, method = 'POST', isCompleteUrl = true) => http.request('https://12123.bolink.club/couponplatform/coupon/getsharecoupon', e, type, mask, loading, method, isCompleteUrl), // 查询-分享停车券
  querysharecoupon: (e, type = true, mask = true, loading = true, method = 'POST', isCompleteUrl = true) => http.request('https://12123.bolink.club/couponplatform/coupon/querysharecoupon', e, type, mask, loading, method, isCompleteUrl), // 领取-分享停车券
  getImageList: (e, type = true, mask = true, loading = true, method = 'GET', isCompleteUrl = true) => http.request('https://12123.bolink.clubcapp/product/image/list?position=2', e, type, mask, loading, method, isCompleteUrl), // 获取-首页banner
  queryNotice: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/cappNotice/queryNotice', e, type, mask, loading, method), // 查询滚动公告
  subscribeSta: (e, type = true, mask = false, loading = false,) => http.request('capp/api/subscribe/subscribe', e, type, mask, loading), // 订阅统计
  mySubscribe: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/subscribe/pri_tmpl_ids', e, type, mask, loading, method), // 查询已订阅的模板
  queryBatchConfig: (e, type = true, mask = false, loading = false, method = 'GET') => http.request('capp/api/weixin/queryBatchConfig', e, type, mask, loading, method), // 券续期-查询
  delayorder: (e) => http.request('capp/api/weixin/delayorder', e), // 券续期-支付
  applysign: (e) => http.request('capp/api/pingan/applysign', e), // 签约页面-获取验证码
  presigncontract: (e) => http.request('capp/api/pingan/presigncontract', e), // 签约页面-签约验证
  // clickad: (e) => http.request('capp/api/information/clickad', e), // 统计广告点击
  clickad: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/information/clickad', e, type, mask, loading, method), // 统计广告点击
  getInvoice: (e) => http.request('capp/api/invoice/getInvoice', e), // 申请开票
  praiseComment: (e, type = false, mask = false, loading = false) => http.request('capp/api/praise/comment', e, type, mask, loading), // 吐槽点赞
  praiseArtical: (e, type = false, mask = false, loading = false) => http.request('capp/api/praise/artical', e, type, mask, loading), // 文章点赞
  oiltime: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/oil/price/next_update_time', e, type, mask, loading, method), // 查询-油价更新时间
  calendar: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/oil/price/calendar', e, type, mask, loading, method), // 查询-油价日历
  oillist: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/oil/price/province', e, type, mask, loading, method), // 查询-油价省份变化
 oilpage: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/oilrecode/oilpage', e, type, mask, loading, method), //查询油耗 首页
 oilrecodelist: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/oilrecode/list', e, type, mask, loading, method), //获取油价历史记录
 oilrecodeadd: (e, type = true, mask = true, loading = true, method = 'POST') => http.request('capp/oilrecode/add', e, type, mask, loading, method), //添加油耗记录
 province: (e, type = true, mask = true, loading = true, method = 'GET') => http.request('capp/api/car/province/list', e, type, mask, loading, method), // 查询-油价省份
 articlecomment: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/information/articlecomment', e, type, mask, loading, method), // 文章评论
 articlereview: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/api/information/articlereview', e, type, mask, loading, method), //查询文章评论
 articlecommentpraise: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/api/information/articlecommentpraise', e, type, mask, loading, method), //文章详情评论点赞


chargingpayment: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/chargingPilePay/prepay', e, type, mask, loading, method), //充电桩支付接口
queryNewEnergyPlateNumber: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/chargingPilePay/queryNewEnergyPlateNumber', e, type, mask, loading, method), //获取用户新能源车牌的接口

queryMyWallet: (e, type = true, mask = true, loading = true, method = 'POST') => http.request('capp/chargingPilePay/queryMyWallet', e, type, mask, loading, method), // 余额显示

queryPayOrder: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/chargingPilePay/queryPayOrder', e, type, mask, loading, method), //充值订单详情
//获取优惠加油ad跳转地址
getOilAdPath: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/oil/redirect/gas_station', e, type, mask, loading, method),
// 获取咪咕优惠券
getMiguCoupon: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/cappmigu/sendCouponSecond', e, type, mask, loading, method),
// 查询用户绑定的车辆
getUserPlateNumber: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/api/car/queryUserPlateNumber', e, type, mask, loading, method),
//获取订单列表
queryInpayMentMore: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/pay/queryInpayMentMore', e, type, mask, loading, method),
// 充电退款
pileRefund: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/cha_pile/deduct_button', e, type, mask, loading, method),
// 月卡审核
monthlyExamine: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/monthly_card/examine', e, type, mask, loading, method),
// 折扣列表 
discountList: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/monthly_card/discount/list', e, type, mask, loading, method),
// 月卡调支付
monthlyPay: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/monthly_card/pay', e, type, mask, loading, method),
// 月卡调H5支付
monthly_cardPay : (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/monthly_card/h5Pay', e, type, mask, loading, method),
// 获取月卡信息
monthlyCard: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/monthly_card/info', e, type, mask, loading, method),
//获取月卡价格
getprodsum: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cappUrl+'capp/coupon/carowerproduct/bolinkgetprodprice', e, type, mask, loading, method,isCompleteUrl,'application/x-www-form-urlencoded'),
//获取结束时间
getEndTime: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cappUrl+'capp/coupon/getdata/getEndTime', e, type, mask, loading, method,isCompleteUrl),
//获取续费时长限制
getMonthCarLimitTime: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cappUrl+'capp/coupon/parkset/getMonthCarLimitTime', e, type, mask, loading, method,isCompleteUrl),
// 发送邮件
  sendEmail: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/pay/sendEmail', e, type, mask, loading, method),
// 获取开票列表
  getMakeInvoiceRecord: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/pay/getMakeInvoiceRecord', e, type, mask, loading, method),
// 获取首页停车搜索列表
suggestion: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/api/park/suggestion', e, type, mask, loading, method),
// 获取首页显示的主页
getIsProject: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/api/user/getIsProject', e, type, mask, loading, method),
//获取首页充电搜索列表
queryPowerStationList: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/powerStation/queryPowerStationList', e, type, mask, loading, method,isCompleteUrl),
//获取首页充电信息列表
queryBillingSet: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/powerStation/queryBillingSet', e, type, mask, loading, method,isCompleteUrl),
//充电导航线路
mapService: (e, type = true, mask = false, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/api/mapService', e, type, mask, loading, method,isCompleteUrl),
// 获取用户头像
uploadPic: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/avatar', e, type, mask, loading, method,false,'multipart/form-data;boundary='+new Date().getTime()), 
// 获取首页我的图标信息
getElementIcon: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/getElementIcon', e, type, mask, loading, method),
// 获取空白码信息
microcode: (e, type = false, mask = true, loading = false, method = 'POST') => http.request('capp/chargingPilePay/microcode', e, type, mask, loading, method),
// 获取项目人脸等信息
faceProjectInfo: (e, type = false, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/faceProjectInfo/queryById', e, type, mask, loading, method,isCompleteUrl),
// 点击按钮埋点
buttonClickAdd: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/buttonClick/add', e, type, mask, loading, method),
// 退出登录
logOut: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/logOut', e, type, mask, loading, method),

// 畅由积分
// 校验手机号是否为移动手机号
checkMobile: (e, type = true, mask = true, loading = false, method = 'GET') => http.request('capp/changyou/checkMobile', e, type, mask, loading, method),
// 获取用户积分信息
queryCmccBalance: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/queryCmccBalance', e, type, mask, loading, method),
// 查询兑换福利
queryCouponInfo: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/queryCouponInfo', e, type, mask, loading, method),
// 畅由-兑分下单接口
commitOrder: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/commitOrder', e, type, mask, loading, method),
// 获取用户积分信息
sendCmccSms: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/sendCmccSms', e, type, mask, loading, method),
// 获取用户积分信息
deduct: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/deduct', e, type, mask, loading, method),
// 订单查询
queryChangyouOrder: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/queryOrder', e, type, mask, loading, method),
// 订单记录
queryChangyouRecord: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/changyou/queryChangyouRecord', e, type, mask, loading, method),
// 获取附近停车场详情充电信息
getChargePlieInfo: (e, type = true, mask = false, loading = true, method = 'get') => http.request('capp/api/park/getChargePlieInfo', e, type, mask, loading,method), 

// 畅游openid绑定
saveOpenRedis: (e, type = true, mask = false, loading = true, method = 'get') => http.request('capp/changyou/saveOpenRedis', e, type, mask, loading,method), 
// 绑定车牌
addPlateCarNumber: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/chargingPilePay/addPlateCarNumber', e, type, mask, loading, method),
// 根据openid查询 用户手机号码
getMobile: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/getMobile', e, type, mask, loading, method),
// 用户openid 和手机号码关联
updateMobile: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/updateMobile', e, type, mask, loading, method),
// 小程序获取手机验证码
verifyCode: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/verifyCode/send', e, type, mask, loading, method),
// 小程序获取用户昵称
getUserInfoNickname: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/getUserInfo', e, type, mask, loading, method),
// 保存用户昵称
setUserInfo: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/setUserInfo', e, type, mask, loading, method),
// 新增挪车码
addMoveCarCode: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/car/addMoveCarCode', e, type, mask, loading, method),
// 发送验证码
MoveCarCodeVerifyCode: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/user/verifyCode/put', e, type, mask, loading, method),
// 查询用户挪车码
findMoveCarCodeByUserNo: (e, type = true, mask = true, loading = false, method = 'POST') => http.request('capp/api/car/findMoveCarCodeByUserNo', e, type, mask, loading, method),
// 扫挪车码
getMoveCarCodeByCode: (e, type = true, mask = false, loading = true, method = 'get') => http.request('capp/api/car/getMoveCarCodeByCode', e, type, mask, loading,method), 


// 新版钱包相关接口
// 钱包查询接口
queryWallet: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = false,contentType="application/x-www-form-urlencoded") =>  http.request('capp/walletCapp/queryWallet', e, type, mask, loading,method,isCompleteUrl,contentType),
// 订单查询接口
queryWalletOrder: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/walletCapp/order/queryOrder', e, type, mask, loading,method), 
// 充值下单接口
toRecharge: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/walletCapp/fund/recharge', e, type, mask, loading,method),
// 查询钱包订单明细接口
queryWalletOrdersDetails: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/walletCapp/order/queryOrders', e, type, mask, loading,method), 
// 钱包退款接口
toWalletRefund: (e, type = true, mask = false, loading = false, method = 'POST') => http.request('capp/walletCapp/refund', e, type, mask, loading,method),

// 一码停车支付
// 获取无牌车车牌
generateNolienceByUnionId: (e, type = false, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/generateNolienceByUnionId', e, type, mask, loading,method),
// 无牌车入场
nolienceinpark: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/nolienceinpark', e, type, mask, loading,method),
// 查询进场状态
getHandleResult: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/getHandleResult', e, type, mask, loading = false,method),
// 获取订单详情
queryOrderDetail: (e, type = true, mask = false, loading = false, method = 'POST') => http.request('capp/CappScanCodePay/queryOrderDetail', e, type, mask, loading,method),
// 直付后查询订单
queryOrderResult: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/queryOrderResult', e, type, mask, loading,method),
// 获取车牌列表
getCarNumbers: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/getCarNumbers', e, type, mask, loading,method),
// 获取订单列表
queryOrderList: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/queryOrderList', e, type, mask, loading,method),
// app-登录验证码获取
getMobileCode: (e, type = false, mask = false, loading = true, method = 'POST') => http.request('capp/app/getMobileCode', e, type, mask, loading,method), 
// app绑定openid
bindWxOpeid: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/app/wxCheckLogin', e, type, mask, loading,method), 
// app-登录
appLogin: (e, type = false, mask = false, loading = true, method = 'POST') => http.request('capp/app/login', e, type, mask, loading,method),

// 获取unionId(支付宝)
getUnionId: (e, type = false, mask = false, loading = true, method = 'GET') => http.request('capp/api/user/getUnionId', e, type, mask, loading,method),

// 消息设置（我的）
// 订阅列表获取
getCarSubscribe: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/carMessageSet/query', e, type, mask, loading,method), 
// 关闭通知
clearCarSubscribe: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/carMessageSet/clear', e, type, mask, loading,method), 
// 消息订阅
setCarSubscribe: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/carMessageSet/set', e, type, mask, loading,method), 

//积分查询
getExchange: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/integral/exchange/get', e, type, mask, loading,method), 
//积分绑定
integralBind: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/integral/exchange/bind', e, type, mask, loading,method), 
//重新发送绑定畅由验证码
sendBindCode: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('/capp/integral/exchange/sendBindCode', e, type, mask, loading,method), 
// 自主下单
choiceOrder: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('/capp/integral/exchange/choiceOrder', e, type, mask, loading,method), 
//自主下单重发验证码
orderSMS: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('/capp/integral/exchange/orderSMS', e, type, mask, loading,method), 
//提交积分兑换券下单
orderPayPlace: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('/capp/integral/exchange/orderPayV3', e, type, mask, loading,method), 
//查询订单积分
queryPointsDetailByPhone: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/queryPointsDetailByPhone', e, type, mask, loading,method),
//兑换积分
redeemPoints: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('capp/CappScanCodePay/redeemPoints', e, type, mask, loading,method),
}