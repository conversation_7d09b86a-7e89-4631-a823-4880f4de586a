<view class='picker-column-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='getOptionText'>
  </import-sjs>
  <view class='van-picker-column {{customClass}}' style='height: {{ itemHeight * visibleItemCount }}px' catchTouchStart='antmoveAction' data-antmove-touchstart='onTouchStart' catchTouchMove='antmoveAction' data-antmove-touchmove='onTouchMove' catchTouchEnd='antmoveAction' data-antmove-touchend='onTouchEnd' catchTouchCancel='antmoveAction' data-antmove-touchcancel='onTouchEnd'>
    <view style='transition: transform {{ duration }}ms; line-height: {{ itemHeight }}px; transform: translate3d(0, {{ offset + (itemHeight * (visibleItemCount - 1)) / 2 }}px, 0)'>
      <view a:for='{{ options }}' a:for-item='option' a:key='{{index}}' data-index='{{ index }}' style='height: {{ itemHeight }}px' class="van-ellipsis van-picker-column__item {{ option && option.disabled ? 'van-picker-column__item--disabled' : '' }} {{ index === currentIndex ? 'van-picker-column__item--selected active-class' : '' }}" ref-numbers='{{ options }}' onTap='antmoveAction' data-antmove-tap='onClickItem'>
        {{ getOptionText(option, valueKey) }}
      </view>
    </view>
  </view>
</view>