<template>
	<view class="gunCode">
		
		<!-- #ifdef MP-WEIXIN || MP-ALIPAY || APP-PLUS -->
		<view class="custom-back-nav"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}">
			<view class="flex-row alc">
				<van-icon @click="goBack" name="arrow-left" color="#000000" size="36rpx" />
				<view @click="goBack" style="margin-left: 4rpx;color: #000000;">枪编码</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<custom-nav-bar title=""></custom-nav-bar>
		<!-- #endif -->
		<view class="bgFFF"></view>
		<!-- 输入充电 -->
		<template  v-if="!mobile">
			<view class="charge-item-bottom">
				<input v-model="gunNo" maxlength="50" class="item-bottm-text1" placeholder="请输入枪编码" />
			</view>
			<button class="item-bottm-clil" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" style="
						display: flex;
						align-items: center;
						justify-content: center;
					">
					确定
				</button>
	</template>
	<template  v-else>
		<view class="charge-item-bottom">
			<input v-model="gunNo" maxlength="20" class="item-bottm-text1" placeholder="请输入枪编码" />
		</view>
		<button class="item-bottm-clil" @click="Recharge" style="
					display: flex;
					align-items: center;
					justify-content: center;
				">
				确定
			</button>
	</template>
		<van-icon name="http://image.bolink.club/FtKl5M3RFYXDEsqHiaLN9kWi3EMs" size="120rpx" class="scanning-code"  @click="handleScanQr" />

		<van-dialog id="van-dialog" />

	</view>
</template>

<script>
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	import util from "../../common/utils/util.js"
	import apis from "../../common/apis/index";
	const app = getApp();

	export default {
		data() {
			return {
				windowHeight: 0,
				statusBarHeight: 0,
				navHeight: 0,
				mobile: uni.getStorageSync('mobile') || '', // 用户手机号
				gunNo: '',
			}
		},
		onLoad(options) {
			console.log('options-->', options);
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
			getApp().login().then(res => {

			})
		},
		onShow() {
			// 判断是否有token和session_key
			getApp().isloginSuccess();
		},
		methods: {
			/* 获取充电桩信息 */
			getPileMessage() {
				util.getPileMessage().then().catch(err => {
					if (err && err.code == 4002) {
						uni.removeStorageSync('cloudToken')
						this.getPileMessage()
					} else {
						uni.showToast({
							title: err,
							icon: 'none',
							duration: 3000
						});
					}
				})
			},
			// 打开扫码功能
			handleScanQr() {
				let that = this
				// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
				uni.scanCode({
					success: (res) => {
						console.log(res)
						util.dealQrcode(res.result || res).then(result => {
							console.log(result)
							if (result && result.code == 200) {
								console.log(result)
								that.getPileMessage()
							}
						}).catch(err => {
							uni.showToast({
								title: err,
								duration: 3000,
								icon: 'none'
							})
							uni.hideLoading();
						})
					},
					fail(err) {
						console.log(err)
					}
				})
				// #endif
				// #ifdef H5
				uni.chooseImage({
					sizeType: ['original'],
					count: 1,
					success(res) {
						uni.showLoading({
							title: '加载中......'
						})
						const tempFilePaths = res.tempFilePaths[0]
						qrcode.decode(tempFilePaths)
						qrcode.callback = code => {
							if (code == "error decoding QR Code") {
								uni.showToast({
									title: "无效的二维码，请重新上传!",
									duration: 2000,
									icon: 'none'
								})
								uni.hideLoading();
							} else {
								util.dealQrcode(code).then(result => {
									console.log(result)
									if (result && result.code == 200) {
										console.log(result)
										that.getPileMessage()
									}
								}).catch(err => {
									uni.showToast({
										title: err,
										duration: 3000,
										icon: 'none'
									})
									uni.hideLoading();
								})
							}
						}
					},
					fail: (err) => {

					}
				});
				// #endif
			},
			/* 输入电桩编码查询充电桩 */
			Recharge() {
				let {
					gunNo
				} = this
				if (!gunNo || gunNo.length < 3) {
					uni.showToast({
						title: '请输入正确的枪编码',
						icon: "none",
						duration: 2000
					});
				} else {
					let queryType={type:1}
					// let hasGun = gunNo ? null : 2
					gunNo = (gunNo ? util.supplyZelo(gunNo) : '')
					uni.setStorageSync('gunNo', gunNo)
					util.getPileMessage(0,queryType).then().catch(err=>{
						uni.showToast({
							title: err,
							icon: 'none'
						})
					})
				}
			},
			
			

			getPhoneNumber(e) {
				getApp().getPhoneNumber(e, this)
			},
			goBack() {
				uni.navigateBack({
					delta: 1,
				});
			},
		}
	}
</script>

<style lang="less">
	.gunCode {
		width: 100%;
		background-color:#f0f2f5;
	}

	.bgFFF {
		width: 100%;
		height: 10vh;
		background-color: #fff;
	}

	.charge-item-bottom {
		width: 90%;
		height: 200rpx;
		margin-top: 5%;
		text-align: center;
		// background-color: #ffffff;
		display: flex;
		flex-flow: column;
		border-radius: 40rpx;
		// box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
	}

	.item-bottm-text {
		font-size: 26rpx;
		font-weight: 500;
		color: #b0b6c1;
		width: 90%;
		margin-left: 5%;
		margin-top: 50rpx;
		text-align: center;
	}

	.item-bottm-clil {
		font-size: 26rpx;
		width: 80%;
		height: 80rpx;
		margin:0 auto;
		margin-top: 50rpx;
		line-height: 96rpx;
		background: #1989fa;
		border-radius: 50px;
		box-shadow: 0px 12px 32px 0px rgba(57, 112, 240, 0.28),
			0px 4px 8px 0px rgba(0, 0, 0, 0.06);
		overflow: hidden;
		color: #ffffff;

		::v-deep.van-button {
			width: 100%;
			height: 100%;
		}
	}

	.item-bottm-text1 {
		font-size: 26rpx;
    font-weight: 500;
    color: #b0b6c1;
    /* padding-left: 40rpx; */
    background-color: #ffffff;
    width: 90%;
    height: 100rpx;
    margin-left: 70rpx;
    margin-top: 50rpx;
    border-radius: 36rpx;
	}
	.scanning-code{
		background-color: #1989fa;
		width: 150rpx;
    height: 150rpx;
    border-radius: 50%;
    position:fixed;
    bottom: 100rpx;
		left:50%;
    transform: translateX(-50%);
    box-shadow: 0px 12px 32px 0px rgba(57, 112, 240, 0.28), 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
	}
</style>
