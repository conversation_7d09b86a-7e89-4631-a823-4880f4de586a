<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block title='基础用法' ref='saveChildRef1'>
    <van-cell-group ref='saveChildRef2'>
      <van-cell title='单元格' value='内容' ref='saveChildRef3'>
      </van-cell>
      <van-cell title='单元格' value='内容' label='描述信息' border='{{ false }}' ref='saveChildRef4'>
      </van-cell>
    </van-cell-group>
  </demo-block>
  <demo-block title='单元格大小' ref='saveChildRef5'>
    <van-cell-group ref='saveChildRef6'>
      <van-cell title='单元格' value='内容' size='large' ref='saveChildRef7'>
      </van-cell>
      <van-cell title='单元格' value='内容' size='large' use-label-slot=" " border='{{ false }}' ref='saveChildRef8'>
        <view slot='label'>
          描述信息
        </view>
      </van-cell>
    </van-cell-group>
  </demo-block>
  <demo-block title='展示图标' ref='saveChildRef9'>
    <van-cell title='单元格' value='内容' icon='location-o' border='{{ false }}' ref='saveChildRef10'>
    </van-cell>
  </demo-block>
  <demo-block title='展示箭头' ref='saveChildRef11'>
    <van-cell title='单元格' is-link=" " ref='saveChildRef12'>
    </van-cell>
    <van-cell title='单元格' value='内容' is-link=" " ref='saveChildRef13'>
    </van-cell>
    <van-cell title='单元格' is-link=" " arrow-direction='down' value='内容' border='{{ false }}' ref='saveChildRef14'>
    </van-cell>
  </demo-block>
  <demo-block title='页面跳转' ref='saveChildRef15'>
    <van-cell title='单元格' is-link=" " url='/pages/dashboard/index' ref='saveChildRef16'>
    </van-cell>
    <van-cell title='单元格' is-link=" " url='/pages/dashboard/index' link-type='redirectTo' ref='saveChildRef17'>
    </van-cell>
  </demo-block>
  <demo-block title='分组标题' ref='saveChildRef18'>
    <van-cell-group title='分组 1' ref='saveChildRef19'>
      <van-cell title='单元格' value='内容' ref='saveChildRef20'>
      </van-cell>
    </van-cell-group>
    <van-cell-group title='分组 2' ref='saveChildRef21'>
      <van-cell title='单元格' value='内容' ref='saveChildRef22'>
      </van-cell>
    </van-cell-group>
  </demo-block>
  <demo-block title='使用插槽' ref='saveChildRef23'>
    <van-cell value='内容' icon='shop-o' is-link=" " ref='saveChildRef24'>
      <view slot='title'>
        <view class='title'>
          单元格
        </view>
        <van-tag type='danger' ref='saveChildRef25'>
          标签
        </van-tag>
      </view>
    </van-cell>
    <van-cell title='单元格' border='{{ false }}' ref='saveChildRef26'>
      <van-icon slot='right-icon' name='search' ref='saveChildRef27'>
      </van-icon>
    </van-cell>
  </demo-block>
  <demo-block title='垂直居中' ref='saveChildRef28'>
    <van-cell center=" " title='单元格' value='内容' label='描述信息' ref='saveChildRef29'>
    </van-cell>
  </demo-block>
</view>