<template>
	<view class="qa-popup" :class="ease ? 'qa-popup-ease-in' : 'qa-popup-ease-out'"> 	
		
		<view class="qa-abso-box" :style="{top: (windowHeight*0.3)+'px'}" :class="ease ? 'ease-in' : 'ease-out'">
			<view style="position: relative;">
				<view style="position: absolute;top: -84rpx;width: 100%;height: 84rpx;">
					<image src="https://image.bolink.club/yima/qa-popup.png" style="width: 100%;height: 84rpx;"></image>
					<view class="qa-arrow" @click="hideQaPopup">
						<image src="https://image.bolink.club/yima/qa-popup-arrow.png" style="width: 42rpx;height: 18rpx;"></image>
					</view>
				</view>
				<view class="qa-title">常见问题</view>
				<scroll-view scroll-y="true" :style="{height: (windowHeight*0.7)+'px'}" @scrolltolower="scrolltolower">
					<view v-if="listData.length>0">
						<van-cell
							v-for="(item,index) in listData" 
							:key="index" 
							:title="item.problem" 
							is-link 
							center
							@click="jumpDetail(item)"
							custom-class="qa-cell"
						></van-cell>
						<view style="text-align: center;color: #666;font-size: 26rpx;padding: 20rpx;">{{isEnd ? '到底了' : '加载中...'}}</view>
					</view>
					
					<empty v-else />
					<view style="height: 80rpx;"></view>
				</scroll-view>
			</view>	
		</view>
		
		
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import empty from '../../components/empty/empty.vue';
	
	export default {
		props: {
			item: {
				type: Object,
				default: null
			},
			transition: {
				type: Boolean,
				default: false
			}
		},
		components: {
			empty
		},
		data () {
			return {
				ease: false,
				listData: [],
				pageNum: 1,
				pageSize: 20,
				isEnd: false,
				windowHeight: null,
				statusBarHeight: null,
				navHeight: null,
			};
		},
		created () {
			this.ease = this.transition;
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
			this.initData();
		},
		methods: {
			hideQaPopup () {
				this.ease = false;
				setTimeout(()=> {
					this.$emit('hideQaPopup')
				}, 600)
			},
			scrolltolower () {
				this.pageNum += 1;
				this.initData();
			},
			initData () {
				let params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
				}
				apis.homeApis.getProblemInfoList(params).then((res) => {
					uni.stopPullDownRefresh();
					this.listData = this.listData.concat(res.data.rows);
					if (res.data.total > 0 && res.data.rows.length == 0) {
						this.isEnd = true;
					} else if (res.data.total === res.data.rows.length) {
						this.isEnd = true;
					}
				})
			},
			jumpDetail (item) {
				uni.setStorageSync("questionDetail", item)
				uni.navigateTo({
					url: "/pagesE/user/questionDetail"
				})
			}
		}
	}
</script>

<style lang="less">
	.qa-popup {
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0);
	}
	.qa-popup-ease-in {
		background-color: rgba(0, 0, 0, 0.4);
		transition: background-color 0.6s ease-in 0.2s;
	}
	.qa-popup-ease-out {
		background-color: rgba(0, 0, 0, 0);
		transition: background-color 0.6s ease-out 0s;
	}
	.ease-in {
		top: 30%!important;
		transition: top 0.6s ease-in 0s;
	}
	.ease-out {
		top: 150%!important;
		transition: all 0.6s ease-out 0s;
	}
	.qa-abso-box {
		position: absolute;
		top: 150%;
		background-color: #fff;
		width: 100%;
	}
	.qa-title {
		color: #333;
		font-size: 34rpx;
		margin-left: 34rpx;
		margin-bottom: 20rpx;
	}
	.qa-arrow {
		position: absolute;
		top: -44rpx;
		left: calc(50% - 48rpx);
		width: 94rpx;
		height: 94rpx;
		line-height: 94rpx;
		text-align: center;
		background-color: #fff;
		border-radius: 50%;
	}
	.qa-arrow:active {
		transform: scale(0.95);
		background-color: rgba(255,255,255,0.8);
	}
</style>
<style>
	.qa-cell {
		color: #666;
	}
</style>
