"use strict";

var _component = require("../common/component");

var _button = require("../mixins/button");

var _openType = require("../mixins/open-type");

(0, _component.VantComponent)({
  mixins: [_button.button, _openType.openType],
  props: {
    show: <PERSON><PERSON><PERSON>,
    title: String,
    cancelText: String,
    description: String,
    round: {
      type: Boolean,
      value: true
    },
    zIndex: {
      type: Number,
      value: 100
    },
    actions: {
      type: Array,
      value: []
    },
    overlay: {
      type: Boolean,
      value: true
    },
    closeOnClickOverlay: {
      type: Boolean,
      value: true
    },
    closeOnClickAction: {
      type: <PERSON><PERSON><PERSON>,
      value: true
    },
    safeAreaInsetBottom: {
      type: <PERSON>olean,
      value: true
    }
  },
  methods: {
    onSelect: function onSelect(event) {
      var index = event.currentTarget.dataset.index;
      var item = this.data.actions[index];

      if (item && !item.disabled && !item.loading) {
        this.$emit("select", item);

        if (this.data.closeOnClickAction) {
          this.onClose();
        }
      }
    },
    onCancel: function onCancel() {
      this.$emit("cancel");
    },
    onClose: function onClose() {
      this.$emit("close");
    },
    onClickOverlay: function onClickOverlay() {
      this.$emit("click-overlay");
      this.onClose();
    }
  }
});