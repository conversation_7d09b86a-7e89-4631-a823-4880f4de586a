.dialog-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.dialog-index .van-dialog {
    top: 45% !important;
    overflow: hidden;
    width: 320px;
    width: var(--dialog-width, 320px);
    font-size: 16px;
    font-size: var(--dialog-font-size, 16px);
    border-radius: 16px;
    border-radius: var(--dialog-border-radius, 16px);
    background-color: #fff;
    background-color: var(--dialog-background-color, #fff);
}

@media (max-width: 321px) {
    .van-dialog {
        width: 90%;
        width: var(--dialog-small-screen-width, 90%);
    }
}

.dialog-index .van-dialog__header {
    text-align: center;
    padding-top: 24px;
    padding-top: var(--dialog-header-padding-top, 24px);
    font-weight: 500;
    font-weight: var(--dialog-header-font-weight, 500);
    line-height: 24px;
    line-height: var(--dialog-header-line-height, 24px);
}

.dialog-index .van-dialog__header--isolated {
    padding: 24px 0;
    padding: var(--dialog-header-isolated-padding, 24px 0);
}

.dialog-index .van-dialog__message {
    overflow-y: auto;
    text-align: center;
    -webkit-overflow-scrolling: touch;
    font-size: 14px;
    font-size: var(--dialog-message-font-size, 14px);
    line-height: 20px;
    line-height: var(--dialog-message-line-height, 20px);
    max-height: 60vh;
    max-height: var(--dialog-message-max-height, 60vh);
    padding: 24px;
    padding: var(--dialog-message-padding, 24px);
}

.dialog-index .van-dialog__message-text {
    word-wrap: break-word;
}

.dialog-index .van-dialog__message--hasTitle {
    padding-top: 8px;
    padding-top: var(--dialog-has-title-message-padding-top, 8px);
    color: #646566;
    color: var(--dialog-has-title-message-text-color, #646566);
}

.dialog-index .van-dialog__message--round-button {
    padding-bottom: 16px;
    color: #323233;
}

.dialog-index .van-dialog__message--left {
    text-align: left;
}

.dialog-index .van-dialog__message--right {
    text-align: right;
}

.dialog-index .van-dialog__footer {
    display: -webkit-flex;
    display: flex;
}

.dialog-index .van-dialog__footer--round-button {
    position: relative !important;
    padding: 8px 24px 16px !important;
}

.dialog-index .van-dialog__button {
    -webkit-flex: 1;
    flex: 1;
}

.dialog-index .van-dialog__cancel,
.dialog-index .van-dialog__confirm {
    border: 0 !important;
}

.dialog-index .van-dialog-bounce-enter {
    -webkit-transform: translate3d(-50%, -50%, 0) scale(0.7);
    transform: translate3d(-50%, -50%, 0) scale(0.7);
    opacity: 0;
}

.dialog-index .van-dialog-bounce-leave-active {
    -webkit-transform: translate3d(-50%, -50%, 0) scale(0.9);
    transform: translate3d(-50%, -50%, 0) scale(0.9);
    opacity: 0;
}

.dialog-index .btn_box {
    width: 100%;
}
