const authority = {
	// 一码App wxfc2f68263c3dea9d
	
	//一码App
	"wxe551bcb8271420f0": {
		name: '一码App',
		shareName: '一码App',
	},
	// 博思高小程序
	"wxbc3774fb5b5b2bf9": {
		name: '博思高充电',
		companyName: '深圳市博思高科技有限公司',
		shareName: '博思高充电',
		shareChargeBgImage: 'https://image.bolink.club/FlRNszHgltepW3Spbd_V9tmtTDmS', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FvZL6T7mMGDLui8uNyqvxhB5iOIG', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FrmPpOQuS2iEQs_ltC9rPcNq8L7D', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/FmiB_INLvznt1gRa4-9XXgCN8RID', // 分享枪下载背景
		qrcodePrefix: "https://bsg.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/FviwkHED8jfMsxJkjZh2oDYum33x',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '0755-********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FqAYWz_zkTZuJBiNn-Pwmhkvw_Gb",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入开发票",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "开发票"
			// 	},
			// 	appId: null,
			// 	clickType: "Button",
			// 	jumpDesc: "开发票",
			// 	jumpType: "本程序页面",
			// 	keyWork: "我的-点击进入开发票",
			// 	name: "开发票",
			// 	order: null,
			// 	path: "/pagesE/user/record",
			// 	target: "navigateTo",
			// 	type: "getphonenumber",
			// },
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FgGS7r14ZyYLRAS9c-wKzBp_3AoU",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入常见问题",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "常见问题"
			// 	},
			// 	name: "常见问题",
			// 	order: null,
			// 	path: "/pagesF/other/question",
			// 	target: "navigateTo",
			// 	type: "null",
			// },
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	
	// 天耀充电小程序
	"wx5390b14377cfa093": {
		name: '天耀充电',
		companyName: '深圳天曜汽车技术有限公司',
		shareName: '天曜充电',
		shareChargeBgImage: 'https://image.bolink.club/Fm7VX2SVTHIkC1N-VDIsAsgOqX2m', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FuwgY80e3nIoDzGsSf4hupgvRNHw', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FmUi5priVaWYir6p42cGbAyCJwjz', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/Fq8eZnsfEKP2fbllyZ-J9_zeXrNj', // 分享枪下载背景
		qrcodePrefix: "https://ty.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/Fq6ahHLcDUX5tXW6ROOnBKfn9l6n',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '************', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		chargeAgreementType: 'tianyao',
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FqAYWz_zkTZuJBiNn-Pwmhkvw_Gb",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入开发票",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "开发票"
			// 	},
			// 	appId: null,
			// 	clickType: "Button",
			// 	jumpDesc: "开发票",
			// 	jumpType: "本程序页面",
			// 	keyWork: "我的-点击进入开发票",
			// 	name: "开发票",
			// 	order: null,
			// 	path: "/pagesE/user/record",
			// 	target: "navigateTo",
			// 	type: "getphonenumber",
			// },
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FgGS7r14ZyYLRAS9c-wKzBp_3AoU",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入常见问题",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "常见问题"
			// 	},
			// 	name: "常见问题",
			// 	order: null,
			// 	path: "/pagesF/other/question",
			// 	target: "navigateTo",
			// 	type: "null",
			// },
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 八品科技EV充电小程序
	"wxfd50b951c73c335d": {
		name: '八品科技EV充电',
		companyName: '浙江八品科技有限公司',
		shareName: '八品科技EV充电',
		shareChargeBgImage: 'https://image.bolink.club/Fm7VX2SVTHIkC1N-VDIsAsgOqX2m', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FuwgY80e3nIoDzGsSf4hupgvRNHw', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FmUi5priVaWYir6p42cGbAyCJwjz', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/Fq8eZnsfEKP2fbllyZ-J9_zeXrNj', // 分享枪下载背景
		qrcodePrefix: "https://bpkj.bolink.club/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/bapin/basicprofile',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '***********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		chargeAgreementType: 'oulin',
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "我的桩",
				order: null,
				path: "/pagesJ/device/pile",
				target: "navigateTo",
				type: "getphonenumber",
			},
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FqAYWz_zkTZuJBiNn-Pwmhkvw_Gb",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入开发票",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "开发票"
			// 	},
			// 	appId: null,
			// 	clickType: "Button",
			// 	jumpDesc: "开发票",
			// 	jumpType: "本程序页面",
			// 	keyWork: "我的-点击进入开发票",
			// 	name: "开发票",
			// 	order: null,
			// 	path: "/pagesE/user/record",
			// 	target: "navigateTo",
			// 	type: "getphonenumber",
			// },
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FgGS7r14ZyYLRAS9c-wKzBp_3AoU",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入常见问题",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "常见问题"
			// 	},
			// 	name: "常见问题",
			// 	order: null,
			// 	path: "/pagesF/other/question",
			// 	target: "navigateTo",
			// 	type: "null",
			// },
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 驿电新能源小程序
	"wx9672390ccd059e76": {
		name: '驿电新能源',
		companyName: '深圳市驿电新能源科技有限公司',
		shareName: '驿电新能源',
		shareChargeBgImage: 'https://image.bolink.club/Fm7VX2SVTHIkC1N-VDIsAsgOqX2m', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FuwgY80e3nIoDzGsSf4hupgvRNHw', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FmUi5priVaWYir6p42cGbAyCJwjz', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/Fq8eZnsfEKP2fbllyZ-J9_zeXrNj', // 分享枪下载背景
		qrcodePrefix: "https://bpkj.bolink.club/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/yzbasicprofile',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '***********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		chargeAgreementType: 'oulin',
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "我的桩",
				order: null,
				path: "/pagesJ/device/pile",
				target: "navigateTo",
				type: "getphonenumber",
			},
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FqAYWz_zkTZuJBiNn-Pwmhkvw_Gb",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入开发票",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "开发票"
			// 	},
			// 	appId: null,
			// 	clickType: "Button",
			// 	jumpDesc: "开发票",
			// 	jumpType: "本程序页面",
			// 	keyWork: "我的-点击进入开发票",
			// 	name: "开发票",
			// 	order: null,
			// 	path: "/pagesE/user/record",
			// 	target: "navigateTo",
			// 	type: "getphonenumber",
			// },
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FgGS7r14ZyYLRAS9c-wKzBp_3AoU",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入常见问题",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "常见问题"
			// 	},
			// 	name: "常见问题",
			// 	order: null,
			// 	path: "/pagesF/other/question",
			// 	target: "navigateTo",
			// 	type: "null",
			// },
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 易停易充小程序
	"wx189cc7cabbd1e988": {
		name: '易停易充',
		companyName: '易停云(深圳)物联网集团有限公司',
		shareName: '易停易充',
		shareChargeBgImage: 'https://image.bolink.club/Fm7VX2SVTHIkC1N-VDIsAsgOqX2m', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FuwgY80e3nIoDzGsSf4hupgvRNHw', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FmUi5priVaWYir6p42cGbAyCJwjz', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/Fq8eZnsfEKP2fbllyZ-J9_zeXrNj', // 分享枪下载背景
		qrcodePrefix: "https://ytyc.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/FsADt6W3AZ2RJFr7nXE-GNQRGpVQ',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '************', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FqAYWz_zkTZuJBiNn-Pwmhkvw_Gb",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入开发票",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "开发票"
			// 	},
			// 	appId: null,
			// 	clickType: "Button",
			// 	jumpDesc: "开发票",
			// 	jumpType: "本程序页面",
			// 	keyWork: "我的-点击进入开发票",
			// 	name: "开发票",
			// 	order: null,
			// 	path: "/pagesE/user/record",
			// 	target: "navigateTo",
			// 	type: "getphonenumber",
			// },
			// {
			// 	flag: null,
			// 	icon: "https://image.bolink.club/FgGS7r14ZyYLRAS9c-wKzBp_3AoU",
			// 	meta: {
			// 		jumpType: "本程序页面",
			// 		keyWork: "我的-点击进入常见问题",
			// 		appId: null,
			// 		clickType: "Button",
			// 		jumpDesc: "常见问题"
			// 	},
			// 	name: "常见问题",
			// 	order: null,
			// 	path: "/pagesF/other/question",
			// 	target: "navigateTo",
			// 	type: "null",
			// },
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 小威充电
	"wxace55c8b8a96db54": {
		name: '小威充电',
		companyName: '深圳市云泊实业有限公司',
		shareName: '小威充电',
		shareChargeBgImage: 'https://image.bolink.club/FlRNszHgltepW3Spbd_V9tmtTDmS', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FvZL6T7mMGDLui8uNyqvxhB5iOIG', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FrmPpOQuS2iEQs_ltC9rPcNq8L7D', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/FmiB_INLvznt1gRa4-9XXgCN8RID', // 分享枪下载背景
		qrcodePrefix: "https://xw.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'http://image.bolink.club/FhcSxkUIRdQQHeOqjnmyvp_n6zLQ',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '0755-********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 智泊惠
	"wxdd607ab6c39717e8": {
		name: '智泊惠',
		companyName: '智泊惠（重庆）信息技术有限公司',
		shareName: '智泊惠',
		shareChargeBgImage: 'https://image.bolink.club/FlRNszHgltepW3Spbd_V9tmtTDmS', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FvZL6T7mMGDLui8uNyqvxhB5iOIG', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FrmPpOQuS2iEQs_ltC9rPcNq8L7D', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/FmiB_INLvznt1gRa4-9XXgCN8RID', // 分享枪下载背景
		qrcodePrefix: "https://zbh.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/Fra46oLLtm55kQk2dEK1c-ar-dVl',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '***********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 易卡智充
	"wx51efd36103fe8bc1": {
		name: '易卡智充',
		companyName: '南京易卡智联科技有限公司',
		shareName: '易卡智充',
		shareChargeBgImage: 'https://image.bolink.club/FlRNszHgltepW3Spbd_V9tmtTDmS', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FvZL6T7mMGDLui8uNyqvxhB5iOIG', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FrmPpOQuS2iEQs_ltC9rPcNq8L7D', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/FmiB_INLvznt1gRa4-9XXgCN8RID', // 分享枪下载背景
		qrcodePrefix: "https://ykyc.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/Flxx5ckds533zNohd6NdK_L-hU3F',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '***********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	// 智安云服
	"wxa1bd273f70771db5": {
		name: '智安云服',
		companyName: '南京易卡智联科技有限公司',
		shareName: '智安云服',
		shareChargeBgImage: 'https://image.bolink.club/FlRNszHgltepW3Spbd_V9tmtTDmS', // 分享电站背景图片
		shareChargeDownLoadBgImage: 'https://image.bolink.club/FvZL6T7mMGDLui8uNyqvxhB5iOIG', // 分享电站下载背景图片
		shareChargeGunBgImage: 'https://image.bolink.club/FrmPpOQuS2iEQs_ltC9rPcNq8L7D', // 分享枪背景图片
		shareChargeGunDownLoadBgImage: 'https://image.bolink.club/FmiB_INLvznt1gRa4-9XXgCN8RID', // 分享枪下载背景
		qrcodePrefix: "https://zayf.yima.world/p", // 二维码前缀（扫码拦截） 
		icon: 'https://image.bolink.club/Fu-BlryhiEDyHU5mb7lBZK9Hv1sS',
		hideCompanyLabel: true,
		hiddenPark: true, // 停车模块 
		hiddenCharge: false, // 充电模块
		hiddenCommunity: true, // 社区模块
		hiddenOfficialAccount: true, // 公众号
		hiddenCoupon: true, // 优惠券
		hiddenNotice: true, // 消息通知
		hiddenOtherMenu: true, // 其他
		phone: '***********', // 客服电话
		userOfficialAccountLink: '', //我的-公众号链接
		// 我的-菜单
		userDefaultMenu: [{
				flag: null,
				icon: "https://image.bolink.club/FppRSsNvXeIN_xkYC8soTC1A4DFB",
				meta: {
					jumpType: "本程序页面",
					keyWork: "首页-点击进入充电订单",
					appId: null,
					clickType: "Button",
					jumpDesc: "充电订单"
				},
				name: "充电记录",
				order: null,
				path: "/pagesF/order/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		],
		communityMenu: [{
				flag: null,
				icon: "https://image.bolink.club/Fr5924BBGMSLIWQV3x7_CWSFSkIQ",
				meta: {
					jumpType: "本程序页面",
					keyWork: "我的-点击进入单车充电",
					appId: null,
					clickType: "Button",
					jumpDesc: "单车充电"
				},
				name: "单车充电",
				order: null,
				path: "/pagesH/index/index",
				target: "navigateTo",
				type: "getphonenumber",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
			{
				name: "",
				icon: "",
				target: "",
			},
		]
	},
	


}
export default authority
