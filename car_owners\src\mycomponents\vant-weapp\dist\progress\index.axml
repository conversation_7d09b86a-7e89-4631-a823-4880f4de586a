<view class='progress-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='getters'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-progress {{customClass}}' style="height: {{ utils.addUnit(strokeWidth) }}; {{ trackColor ? 'background: ' + trackColor : '' }}">
    <view class='van-progress__portion' style="width: {{ percentage }}%; background: {{ inactive ? '#cacaca' : color }}">
      <view a:if='{{ showPivot && getters.text(pivotText, percentage) }}' style="color: {{ textColor }}; background: {{ pivotColor ? pivotColor : inactive ? '#cacaca' : color }}" class='van-progress__pivot'>
        {{ getters.text(pivotText, percentage) }}
      </view>
    </view>
  </view>
</view>