.search-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.search-index .van-search {
    -webkit-align-items: center;
    align-items: center;
    box-sizing: border-box;
    padding: 10px 12px;
    padding: var(--search-padding, 10px 12px);
}

.search-index .van-search,
.search-index .van-search__content {
    display: -webkit-flex;
    display: flex;
}

.search-index .van-search__content {
    -webkit-flex: 1;
    flex: 1;
    padding-left: 12px;
    padding-left: var(--padding-sm, 12px);
    border-radius: 2px;
    border-radius: var(--border-radius-sm, 2px);
    background-color: #f7f8fa;
    background-color: var(--search-background-color, #f7f8fa);
}

.search-index .van-search__content--round {
    border-radius: 17px;
    border-radius: calc(var(--search-input-height, 34px) / 2);
}

.search-index .van-search__label {
    padding: 0 5px;
    padding: var(--search-label-padding, 0 5px);
    font-size: 14px;
    font-size: var(--search-label-font-size, 14px);
    line-height: 34px;
    line-height: var(--search-input-height, 34px);
    color: #323233;
    color: var(--search-label-color, #323233);
}

.search-index .van-search__field {
    -webkit-flex: 1;
    flex: 1;
}

.search-index .van-search__field__left-icon {
    color: #969799;
    color: var(--search-left-icon-color, #969799);
}

.search-index .van-search--withaction {
    padding-right: 0;
}

.search-index .van-search__action {
    padding: 0 8px;
    padding: var(--search-action-padding, 0 8px);
    font-size: 14px;
    font-size: var(--search-action-font-size, 14px);
    line-height: 34px;
    line-height: var(--search-input-height, 34px);
    color: #323233;
    color: var(--search-action-text-color, #323233);
}

.search-index .van-search__action--hover {
    background-color: #f2f3f5;
    background-color: var(--active-color, #f2f3f5);
}
