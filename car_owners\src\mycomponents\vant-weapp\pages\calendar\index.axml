<view class='page-container-classname' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <demo-block title='基础用法' ref='saveChildRef1'>
    <van-cell is-link=" " title='选择单个日期' data-type='single' data-id='selectSingle' value='{{ computed.formatFullDate(date.selectSingle) }}' onClick='show' ref='saveChildRef2'>
    </van-cell>
    <van-cell is-link=" " title='选择多个日期' data-type='multiple' data-id='selectMultiple' value='{{ computed.formatMultiple(date.selectMultiple) }}' onClick='show' ref='saveChildRef3'>
    </van-cell>
    <van-cell is-link=" " title='选择日期区间' data-type='range' data-id='selectRange' value='{{ computed.formatRange(date.selectRange) }}' onClick='show' ref='saveChildRef4'>
    </van-cell>
  </demo-block>
  <demo-block title='快捷选择' ref='saveChildRef5'>
    <van-cell is-link=" " title='选择单个日期' data-type='single' data-id='quickSelect1' value='{{ computed.formatFullDate(date.quickSelect1) }}' onClick='show' ref='saveChildRef6'>
    </van-cell>
    <van-cell is-link=" " title='选择日期区间' data-type='range' data-id='quickSelect2' value='{{ computed.formatRange(date.quickSelect2) }}' onClick='show' ref='saveChildRef7'>
    </van-cell>
  </demo-block>
  <demo-block title='自定义日历' ref='saveChildRef8'>
    <van-cell is-link=" " title='自定义颜色' data-type='range' data-id='customColor' value='{{ computed.formatRange(date.customColor) }}' onClick='show' ref='saveChildRef9'>
    </van-cell>
    <van-cell is-link=" " title='自定义日期范围' data-type='single' data-id='customRange' value='{{ computed.formatFullDate(date.customRange) }}' onClick='show' ref='saveChildRef10'>
    </van-cell>
    <van-cell is-link=" " title='自定义按钮文字' data-type='range' data-id='customConfirm' value='{{ computed.formatRange(date.customConfirm) }}' onClick='show' ref='saveChildRef11'>
    </van-cell>
    <van-cell is-link=" " title='自定义日期文案' data-type='range' data-id='customDayText' value='{{ computed.formatRange(date.customDayText) }}' onClick='show' ref='saveChildRef12'>
    </van-cell>
    <van-cell is-link=" " title='自定义弹出位置' data-type='single' data-id='customPosition' value='{{ computed.formatFullDate(date.customPosition) }}' onClick='show' ref='saveChildRef13'>
    </van-cell>
    <van-cell is-link=" " title='日期区间最大范围' data-type='range' data-id='maxRange' value='{{ computed.formatRange(date.maxRange) }}' onClick='show' ref='saveChildRef14'>
    </van-cell>
  </demo-block>
  <demo-block title='平铺展示' ref='saveChildRef15'>
    <van-calendar title='日历' poppable='{{ false }}' show-confirm='{{ false }}' min-date='{{ tiledMinDate }}' max-date='{{ tiledMaxDate }}' class='tiled-calendar' ref='saveChildRef16'>
    </van-calendar>
  </demo-block>
  <van-calendar show='{{ showCalendar }}' type='{{ type }}' color='{{ color }}' round='{{ round }}' position='{{ position }}' min-date='{{ minDate }}' max-date='{{ maxDate }}' max-range='{{ maxRange }}' onFormatter='_dayFormatter' show-confirm='{{ showConfirm }}' confirm-text='{{ confirmText }}' confirm-disabled-text='{{ confirmDisabledText }}' onConfirm='onConfirm' onSelect='onSelect' onUnselect='onUnselect' onOpen='onOpen' onOpened='onOpened' onClose='onClose' onClosed='onClosed' ref='saveChildRef18'>
  </van-calendar>
</view>