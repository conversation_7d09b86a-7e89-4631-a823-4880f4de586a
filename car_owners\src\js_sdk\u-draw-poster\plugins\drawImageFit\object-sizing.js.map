{"version": 3, "file": "object-sizing.js", "sourceRoot": "", "sources": ["../../../packages/plugins/drawImageFit/object-sizing.ts"], "names": [], "mappings": "AAcA;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,YAAY,CAC1B,KAMC,EACD,aAAmB,EACnB,aAAmB;IAWnB,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,KAAK,SAAS,CAAA;IAC/C,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAE3D,mBAAmB;IACnB,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAA;IACjE,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAA;IAEjE,oBAAoB;IACpB,IAAI,aAAa,GAAG,CAAC,CAAA;IAErB,IACE,CAAC,cAAc,GAAG,cAAc,IAAI,KAAK,CAAC,SAAS,IAAI,SAAS,CAAC;QACjE,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,CAAC,SAAS,IAAI,OAAO,CAAC;QAEhE,8BAA8B;QAC9B,mDAAmD;QACnD,8DAA8D;QAC9D,MAAM;QACN,aAAa,GAAG,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAA;SACtD,IACH,CAAC,cAAc,GAAG,cAAc,IAAI,KAAK,CAAC,SAAS,IAAI,OAAO,CAAC;QAC/D,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,CAAC,SAAS,IAAI,SAAS,CAAC;QAElE,8BAA8B;QAC9B,qDAAqD;QACrD,gEAAgE;QAChE,MAAM;QACN,aAAa,GAAG,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAA;;QACxD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;IAE7C,MAAM,iBAAiB,GAAG,aAAa,CAAC,KAAK,GAAG,aAAa,CAAA;IAC7D,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,GAAG,aAAa,CAAA;IAE/D,0BAA0B;IAC1B,MAAM,eAAe,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CACxD,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,IAAI,QAAQ,CACzC,CAAA;IACD,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,CACxD,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,IAAI,QAAQ,CACzC,CAAA;IACD,IAAI,gBAAgB,GAAG,CAAC,aAAa,CAAC,KAAK,GAAG,iBAAiB,CAAC,GAAG,eAAe,CAAA;IAClF,IAAI,eAAe,GAAG,CAAC,aAAa,CAAC,MAAM,GAAG,kBAAkB,CAAC,GAAG,eAAe,CAAA;IACnF,IAAI,SAAS,EAAE;QACb,gBAAgB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAA;QACxC,eAAe,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAA;KACxC;IACD,8DAA8D;IAC9D,mBAAmB;IACnB,kEAAkE;IAClE,0FAA0F;IAC1F,wDAAwD;IACxD,0DAA0D;IAC1D,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,KAAa,EAAE,EAAE,CAAC;QACjD,YAAY,CAAC,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG,aAAa;QACvD,YAAY,CAAC,CAAC,KAAK,GAAG,eAAe,CAAC,GAAG,aAAa;KACvD,CAAA;IAED,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAExC,+CAA+C;IAC/C,+CAA+C;IAC/C,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;IAEjF,0BAA0B;IAC1B,OAAO;QACL,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QACxB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QACvB,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC;QACrD,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC;QACtD,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACpE,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACnE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,aAAa,CAAC,KAAK,CAAC;QACpD,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,MAAM,CAAC;KACvD,CAAA;AACH,CAAC"}