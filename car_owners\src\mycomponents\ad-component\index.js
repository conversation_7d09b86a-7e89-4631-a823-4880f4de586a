Component({
  data() {
    return {
      //spaceCode: '50_2023100925000066067',
      extInfo: {},
    }
  },
  props: {
    onRenderSuccess: () => { },
    onRenderFail: () => { },
    onBeforeJump: () => true,
	spaceCode:String,
  },
  methods: {
    onRenderSuccess() {
		console.log('onRenderSuccess广告成功',this.spaceCode,333,this.props.spaceCode)
      this.props.onRenderSuccess && this.props.onRenderSuccess()
    },
    onRenderFail(err) {
		console.log('onRenderFail广告失败',err)
      this.props.onRenderFail && this.props.onRenderFail(err)
    },
    onBeforeJump(err) {
      return this.props.onBeforeJump ? this.props.onBeforeJump(err) : true
    }
  },
});