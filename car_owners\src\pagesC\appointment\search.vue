<template>
	<view  class="search-park">
		<!-- #ifndef MP-ALIPAY -->
		<view class="search-header" :style="{height:(statusBarHeight + navHeight + 27)+'px'}">
			<!-- 顶部菜单 start -->
			<view class="nav-bar-center-default" :style="{top: statusBarHeight+'px',height: navHeight+'px'}">
				<view class="title" :style="{height: navHeight+'px','line-height': navHeight+'px'}">
					<view class="flex jus alc padl-40"
						:style="{height: '100%',width: navHeight+'px','line-height': navHeight+'px'}" @click="toIndex"
						hover-class="park-hover-class">
						<van-icon name="arrow-left" size="36rpx" />
					</view>
					<view class="title--center">预约停车</view>
				</view>
			</view>
			<view class="flex100" :style="{height:(statusBarHeight + navHeight + 4)+'px'}" />
			<!-- 顶部菜单 end -->
		</view>
		<!-- #endif -->
		<!-- 车场搜索 end -->
		<view class="search-body">
		<scroll-view scroll-y="true" class="scroll-list" lower-threshold="300" @scrolltolower="scrolltolowerOrder">
				<view class="order-item flex-col" v-for="(item,index) in orderList" @click="toOrderDetail(item)"
					:key="index">
					<view class="flex-row marb-20">
						<view class="park-name of-clamp1 font-32">
							{{item.comName || '--'}}
						</view>
						<view class="order-type flex-1 jue alc">
							<view class="flex-row alc">
								<template v-if="item.reserveState == 2">
									<van-icon name="https://image.bolink.club/FpHgLoq7LVK_Dafdwp0gS5H6Vl6T"
										class="marr-10" size="26rpx"></van-icon>
									<view class="wait font-26">{{item.reserveState | getStateName}}</view>
								</template>
								<template v-else-if="item.reserveState == 3 || item.reserveState == 100">
									<van-icon name="https://image.bolink.club/FgDKBF43OhQy7Wr9W37WYeRZ1XEh"
										class="marr-10" size="26rpx"></van-icon>
									<view class="timeout font-26">{{item.reserveState | getStateName}}</view>
								</template>
								<template v-else-if="item.reserveState == 6">
									<van-icon name="https://image.bolink.club/FmLv3XPNi1yYmtIqF13qJziJb2QA"
										class="marr-10" size="26rpx"></van-icon>
									<view class="font-26">{{item.reserveState | getStateName}}</view>
								</template>
								<template v-else>
									<van-icon name="https://image.bolink.club/Fmy54K9aoxItI0vbsGWM_I0Oxlak"
										class="marr-10" size="26rpx"></van-icon>
									<view class="font-26">{{item.reserveState | getStateName}}</view>
								</template>
								<van-icon name="arrow" size="26rpx" />
							</view>
						</view>
					</view>
					<view class="flex-row">
						<view class="address of-clamp1 font-26">
							{{item.comAddress || '--'}}
						</view>
						<view class="price fw-600 font-32">
							￥{{item.amount | toFixed2}}
						</view>
					</view>
					<view class="flex-row mart-10">
						<van-icon name="https://image.bolink.club/FiYcrfjc8sPyEPLlPaW2Fntf9kPk" class="marr-10"
							size="26rpx" />
						<view class="font-26 fw-400">
							{{item.startTime | formetDate}}~{{item.endTime | formetDate}}
						</view>
					</view>
				</view>
				<view class="list-end">{{isOrderEnd ? '暂无更多~' : '加载中...'}}</view>
			</scroll-view> 
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import util from "../../common/utils/util";
	export default {
		data() {
			return {
				navHeight: 40,
				statusBarHeight: 47,
				orderList: [], // 订单列表
				isOrderEnd: false, // 是否已加载所有订单
				orderPageNum: 1,
				orderPageSize: 10, // 订单每页查询条数 
			}
		},

		onLoad(options) {
			getApp().getSystemInfo().then(res => {
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight || res.titleBarHeight;
			});

		},
		onShow() {
			this.showOrderList()
		},
		onPullDownRefresh() {
			this.pageNum = 1;
			this.parkListIsEnd = false;
			this.getParkList();
		},
		filters: {
			// 保留两位数字
			toFixed2(val) {
				return val ? Number(val).toFixed(2) : '0.00'
			},
			// 格式化时间
			formetTime(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd hh:mm:ss') : ''
			},
			formetDate(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd c') : ''
			},
			getStateName(val) {
				switch (val) {
					case 1:
						return '预约中';
					case 2:
						return '预约成功';
					case 3:
						return '预约失败';
					case 4:
						return '过期';
					case 5:
						return '已入场';
					case 6:
						return '已出场';
					case 99:
						return '待支付';
					case 100:
						return '已取消';
					default:
						return '--';
				}
			}
		},
		methods: {
			// 返回首页
			toIndex() {
				uni.switchTab({
					url: '/pages/user/index'
				})
			},
			// 展示订单列表
			showOrderList() {
				this.orderPageNum = 1;
				this.orderList = [];
				this.getOrderList();
				this.showOrder = true;
			},
			// 下拉获取订单信息
			scrolltolowerOrder() {
				if (this.isOrderEnd) {
					return false;
				}
				this.orderPageNum += 1;
				this.getOrderList();
			},
			toOrderDetail(item) {
				util.reNavigateTo(`/pagesC/appointment/detail?orderId=${item.id}`);
			},
			getOrderList() {
				let params = {
					openId: uni.getStorageSync('openId'),
					page: this.orderPageNum,
					rp: this.orderPageSize,
				}
				apis.chargingPile.getCameraReserveList(params).then((res) => {
					if (res.code === 200) {
						let orderList = res.data && res.data.voList || [];
						if (orderList.length < this.orderPageSize) {
							this.isOrderEnd = true;
						}
						this.orderList = [...this.orderList, ...orderList];
					} else if (res.code == 500) {
						this.isOrderEnd = true;
					}
				}).catch(() => {

				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.search-park {
		width: 100%;
		height: 100%;
		overflow: hidden;
		background-color: #F8F8F8;

		.search-header {
			// background-image: linear-gradient(to right, #2F5AE5, #468CFE);
			background-color: #468CFE;
			width: 100%;

		}
		
		.search-box {
			height: 88rpx;
			line-height: 88rpx;
			/* #ifdef MP-ALIPAY */
			margin: 0 40rpx;
			/* #endif */
			/* #ifndef MP-ALIPAY */
			margin: -44rpx 40rpx 0;
			/* #endif */
			border-radius: 4rpx;
			
			.search{
				background-color: #fff;
				width: 100%;
				position: relative;
				z-index: 50;
			}
		
			input {
				width: 100%;
				height: 100%;
				font-size: 32rpx;
			}
		}
		
		.search-body {
			/* #ifndef MP-ALIPAY */
			height: calc(100vh - 240rpx);
			/* #endif */
			/* #ifdef MP-ALIPAY */
			height: calc(100vh - 108rpx);
			/* #endif */

			.scroll-list {
				padding: 44rpx 24rpx;
				height: 100%;
				.park-name {
					width: 400rpx;
				}

				.order-item {
					background-color: #FFFFFF;
					border-radius: 5rpx;
					padding: 32rpx 24rpx 36rpx 32rpx;
					margin-bottom: 24rpx;

					.order-type {
						.wait {
							color: #F59A23;
						}

						.timeout {
							color: #A30014;
						}

						.complete {
							color: #5A667E;
						}
					}

					.address {
						width: 532rpx;
						color: #242E3E;
						line-height: 1.5;
					}
				}
			}


		}

	}



</style>
