<template>
	<view>
		<web-view :src="weburl"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				weburl: 'https://12123.bolink.club/miyou_web/api/bolink_openid'
			};
		},
		onLoad (options) {
			console.log('this.options', options);
			if ('url' in options) {
				this.weburl = decodeURIComponent(options.url);
			}
			console.log('this.weburl', this.weburl);
			uni.setNavigationBarTitle({
				title: "一码App"
			});
		},
		onReady () {
			getApp().pagePathRecord();
		},
	}
</script>

<style lang="less">

</style>