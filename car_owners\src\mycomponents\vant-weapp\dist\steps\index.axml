<view class='steps-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from="./index.axmlstatus.sjs" name='status'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{customClass}} {{ utils.bem('steps', [direction]) }}">
    <view class='van-step__wrapper'>
      <view a:for='{{ steps }}' a:key='{{index}}' data-index='{{ index }}' class="{{ utils.bem('step', [direction, status(index, active)]) }} van-hairline" style="{{ status(index, active) === 'inactive' ? 'color: ' + inactiveColor: '' }}" ref-numbers='{{ steps }}' onTap='antmoveAction' data-antmove-tap='onClick'>
        <view class='van-step__title' style="{{ index === active ? 'color: ' + activeColor : '' }}">
          <view>
            {{ item.text }}
          </view>
          <view class='{{descClass}}'>
            {{ item.desc }}
          </view>
        </view>
        <view class='van-step__circle-container'>
          <block a:if='{{ index !== active }}'>
            <van-icon a:if='{{ item.inactiveIcon || inactiveIcon }}' color="{{ status(index, active) === 'inactive' ? inactiveColor: activeColor }}" name='{{ item.inactiveIcon || inactiveIcon }}' custom-class='van-step__icon' ref='saveChildRef1'>
            </van-icon>
            <view a:else  class='van-step__circle' style="{{ 'background-color: ' + (index < active ? activeColor : inactiveColor) }}">
            </view>
          </block>
          <van-icon a:else  name='{{ item.activeIcon || activeIcon }}' color='{{ activeColor }}' custom-class='van-step__icon' ref='saveChildRef2'>
          </van-icon>
        </view>
        <view a:if='{{ index !== steps.length - 1 }}' class='van-step__line' style="{{ 'background-color: ' + (index < active ? activeColor : inactiveColor) }}">
        </view>
      </view>
    </view>
  </view>
</view>