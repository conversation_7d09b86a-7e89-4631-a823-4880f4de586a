<template>
	<view class="whole">
		<view class="abso-box" >
			<image src="../static/record/Group515.jpg" mode="" class="abso-box-img"  ></image>
			<view class="flex-row alc tb">
				<van-icon @click="goBack" name="arrow-left" color="black" size="36rpx" />
				<view @click="goBack" style="margin-left: 4rpx;" class="return-text ">记油耗</view>
			</view>
		</view>
		<!-- 历史油耗 -->
		<view class="whole-date"> {{addOilDate  }}</view>
		<view class="whole-item"   v-for="(item,index) in listname" :key="index">
			<view class="whole-item-one"> <view class="whole-item-text">日期</view>    <view @click="shownex">...</view>     </view>
			<view class="whole-item-one"> <view>车辆总里程</view>     <view> {{item.mileage}} </view>     </view>
			<view class="whole-item-one"> <view>单价</view>           <view>{{item.oilPrice}}</view>     </view>
			<view class="whole-item-one"> <view>加油量</view>         <view>{{item.oilAmount}}</view>     </view>
			<view class="whole-item-one"> <view>加油金额</view>       <view>{{item.oilPrice*item.oilAmount}}</view>     </view>
			<view class="whole-item-one"> <view>增加里程</view>       <view>{{item.mileage-addMileage}}</view>     </view>
			<view class="whole-item-one"> <view>是否加满</view>       <view>{{item.isFillUp >0?  "是" : "否" }}</view>     </view>
		</view>
		 	
	</view>
</template>

<script>
	import BaseConfig from '../../common/config/index.config.js';
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	import poster from '../components/poster/poster.vue'
	const app = getApp();
	
	export default {
		data() {
			return {
			   listname:[
				   {
					   
				   }
			   ] ,//获取历史油耗
			   plateNumber:uni.getStorageSync("plateNumber"),
			   addOilDate:Date.parse(new Date())/1000,
			   isFillUp:0,
			   pageNum:1,
			   pageSize:20,
			}
		},
		onReady(){
						this.oilrecodelist();

		},
		onShow(){
			getApp().isloginSuccess();
		},	
		
		methods: {
			 //获取历史油耗
			 oilrecodelist(){
			 	let params = {
			 					plateNumber: this.plateNumber,
			 					pageNum:this.pageNum,
			 					 pageSize:this. pageSize
			 				}
			 				apis.homeApis.oilrecodelist(params).then((res) => {
			 							// this.listname=res.data.rows
										this.listname=res.data
										console.log(this.listname,'!!!!!!!!!!!!!!!!!!!!!!!!!!')
			 					}) 
			 			},
	        	
			goBack () {
				uni.navigateBack({
					fail: (err)=> {
						uni.switchTab({
							url: '/pages/park/index'
						})
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	  .whole{
		  width: 100%;
	  }
     .tb{
			position: absolute;
			top: 100rpx;
			left: 20rpx;
		}
	.abso-box-img{
		width: 100%;
	}
	.whole-date{
		width: 94px;
		height: 12px;
		position: absolute;
		top: 104.5px;
		left: 17px;
		font-size: 15px;
		font-weight: 700;
		color: #ffffff;
		line-height: 24px;
		letter-spacing: 0px;
	}
	.whole-item{
		display: flex;
		flex-flow: column;
		position: absolute;
		top: 136px;
		width: 90%;
		height: 257.5px;
		margin-left: 5%;
		border-radius: 15px;
		box-shadow: 0px 6px 26px 0px rgba(0,0,0,0.08); 
		background: #FFFFFF;
	}
	.whole-item-one{
		display: flex;
		justify-content: space-between;
		margin-left: 20px;
		margin-right: 20px;
		margin-top: 10px;
	}
	.whole-item-text{
		width: 34px;
		height: 12px;
		font-size: 16px;
		font-weight: 700;
		text-align: LEFT;
		color: #0e79ff;
		line-height: 12px;
	}
</style>
