<template>
	<view class="lff-dis" :style="{height: height}">
		<view class="danmu-li" v-for="(item,index) in listData" :class="item.type" :style="[item.style]" :key="index">
			<view class="danmu-inner">
				<view class="user-box" :class="index%2==1 ? 'bg1' : 'bg2'">
					<view class="user-img">
						<view class="img-box">
							<image :src="item.avatarUrl"></image>
						</view>
					</view>
					<!-- <view class="user-text">
						{{item.nickName}}
					</view> -->
					<view class="user-status">
						{{item.item}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		props: {
			//rightToLeft leftToRight leftBottom
			type: {
				type: String,
				default: 'rightToLeft'
			},
			list: {
				type: Array,
				default () {
					return []
				}
			},
			// 弹幕区域高度
			height: {
				type: String,
				default: '440rpx'
			},
			minTime: {
				type: Number,
				default: 8
			},
			maxTime: {
				type: Number,
				default: 15
			},
			minTop: {
				type: Number,
				default: 2
			},
			maxTop: {
				type: Number,
				default: 240
			},
			hrackH: { //轨道高度
				type: Number,
				default: 40
			}
		},
		data() {
			return {
				listData: [],
				flag: 1,
				top: [2, 5, 4, 3], // 显示的行数顺序
				disBgurl: 'https://image.bolink.club/yima/activity-dis-bg.jpg'
			}
		},
		mounted() {
			//leftBottom 使用参数
			if (this.type === 'leftBottom') {
				this.hrackNum = Math.floor(this.maxTop / this.hrackH);
			}
		},
		methods: {
			add(obj) {
				// console.log('flag->', this.flag)
				// console.log('this.listData.length-->', this.listData.length);
				this.flag ++;
				// time: Math.ceil(Math.floor(Math.random() * (this.maxTime - this.minTime + 1) + this.minTime)),
				let data = {
					item: obj.item,
					nickName: obj.nickName,
					avatarUrl: obj.avatarUrl,
					id:Date.parse(new Date()),
					time: Math.ceil(Math.floor((this.maxTime - this.minTime + 1) + this.minTime)),
					type: this.type
				}
				if (this.type === 'leftBottom') {
					let objData = {
						item: data.item,
						nickName: obj.nickName,
						avatarUrl: obj.avatarUrl,
						type: 'leftBottomEnter',
						style: {
							transition: `all 0.5s`,
							animationDuration: `0.5s`,
							transform: `translateX(0%)`,
							bottom: `${this.minTop}px`
						}
					}
					let listLen = this.listData.length;
					let hrackNum = this.hrackNum;
					for (let i in this.listData) {
						if(this.listData[i].status === 'reuse'){ //重用
							this.$set(this.listData,i,objData);
						}else if(this.listData[i].status === 'reset'){ //重置
							this.listData[i].style.transition = 'none';
							this.listData[i].style.bottom = 0;
							this.listData[i].status = 'reuse';
						}else if(this.listData[i].status === 'recycle'){ //回收
							this.listData[i].type = 'leftBottomExit';
							this.listData[i].status = 'reset';
						}else{
							this.listData[i].style.bottom = parseInt(this.listData[i].style.bottom) + this.hrackH + 'px';
						}
						if(parseInt(this.listData[i].style.bottom) >= (this.maxTop - this.hrackH) && this.listData[i].status !== 'reset'){ //需要回收
							this.listData[i].status = 'recycle';
						}
					}
					if(listLen < hrackNum + 2){
						this.listData.push(objData);
					}
				} else if (this.type === 'rightToLeft') {
					let objData = {
						item: data.item,
						nickName: obj.nickName,
						avatarUrl: obj.avatarUrl,
						type: 'rightToLeft',
						style: {
							animationDuration: `${data.time}s`,
							top: `${Math.ceil(this.top[this.flag%4] * this.hrackH + this.minTop)}px`
						},
						delTime: Date.parse(new Date()) + data.time * 1500
					}
					for (let i in this.listData) {
						if (this.listData[i].delTime <= Date.parse(new Date())) {
							this.repaint(i, objData.type);
							objData.type = '';
							this.$set(this.listData, i, objData);
							return
						}
					}
					this.listData.push(objData);
				}
			},
			repaint(index, type) {
				setTimeout(() => {
					this.listData[index].type = type;
				}, 100)
			}
		}

	}
</script>
<style>

</style>
<style lang="scss">
	.lff-dis {
		overflow: hidden;
		position: relative;
		width: 100%;
		pointer-events: none;
		background-image: url("https://image.bolink.club/yima/activity-dis-bg.jpg");
		background-size: 100% 100%;
	}
	@keyframes leftBottomEnter {
		0% {
			transform: translateY(100%);
			opacity: 0;
		}

		100% {
			transform: translateY(0%);
			opacity: 1;
		}
	}

	@keyframes leftBottomExit {
		0% {
			transform: translateY(0%);
			opacity: 1;
		}
		
		100% {
			transform: translateY(-200%);
			opacity: 0;
		}
	}

	@keyframes leftToRight {
		0% {
			transform: translateX(-100%);
		}

		100% {
			transform: translateX(100%);
		}
	}

	@keyframes rightToLeft {
		0% {
			transform: translateX(100%);
		}

		100% {
			transform: translateX(-100%);
		}
	}

	.danmu-li {
		position: absolute;
		width: 100%;
		transform: translateX(100%);
		animation-timing-function: linear;

		&.leftBottomEnter {
			animation-name: leftBottomEnter;
		}
		&.leftBottomExit{
			animation-name: leftBottomExit;
			animation-fill-mode: forwards;
		}

		&.rightToLeft {
			animation-name: rightToLeft;
		}

		&.leftToRight {
			animation-name: rightToLeft;
		}

		.danmu-inner {
			display: inline-block;
			.bg1 {
				background: linear-gradient(90deg,#8523e7 0%, rgba(43,138,251,0.00) 100%);
			}
			.bg2 {
				background: linear-gradient(90deg,#40d3f3 0%, rgba(35,130,249,0.00) 100%);
			}
			.user-box {
				display: flex;
				padding: 3rpx 40rpx 3rpx 0rpx;
				border-radius: 50rpx;
				align-items: center;
				.user-img {
					.img-box {
						display: flex;
						image {
							width: 58rpx;
							height: 58rpx;
							border: 2rpx solid #ffffff;
							border-radius: 50rpx;
						}
					}
				}

				.user-status {
					margin-left: 10rpx;
					white-space: nowrap;
					font-size: 28rpx;
					font-weight: bold;
					color: rgba(255, 255, 255, 1);
				}

				.user-text {
					margin-left: 10rpx;
					// white-space: nowrap;
					font-size: 28rpx;
					font-weight: bold;
					min-width: 40rpx;
					max-width: 200rpx;
					overflow: hidden;
					white-space: nowrap; 
					text-overflow: ellipsis;
					color: rgba(255, 255, 255, 1);
				}
			}
		}
	}
</style>
