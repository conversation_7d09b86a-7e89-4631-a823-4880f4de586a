<template>
	<view class="pingan">
		
		<view style="position: relative;">
			<image style="width: 100%;height: 694rpx" src="../static/pingan_new/top-bg.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
			
			<view class="flex-row alc juc mart-30 padb-30 my-logo">
				<image src="../static/pingan_new/pingan.png" style="width: 142rpx;height: 55rpx;"></image>
				<view class="content-box-bor"></view>
				<image src="../static/pingan_new/chezhu.png" style="width: 142rpx;height: 55rpx;"></image>
			</view>
		</view>
		
		<view class="main-cotain">
			<view class="bank-info">
				<view class="flex-row">
					<image class="bank-info-logo" src="../static/pingan_new/logo.png"></image>
					<view class="flex-row flex-1 bank-info-nav">
						<view class="bank-info-title">平安银行</view>
						<view class="flex-1 jue">
							<view class="bank-info-btn">开通成功</view>
						</view>
					</view>
				</view>
				<view class="bank-info-content">
					<view class="bank-info-num">{{accountNoPage}}</view>
					<view class="mart-10 bank-info-add">开户行：{{'平安银行佛山分行'}}</view>
				</view>
			</view>
			
			<view class="input-box">
				<view style="position: relative;">
					<image src="../static/pingan_new/bg-line.png" style="width: 100%;height: 72rpx;"></image>
					<view class="top-bg-desc">支付3元，领取10元停车券</view>
				</view>
				<view class="input-box-content">
					<view class="flex-row alc pingan-input-par" style="border-bottom: none;">
						<image src="../static/pingan_new/icon3.png" style="width: 36rpx;height: 27rpx;"></image>
						<input class="pingan-input" :disabled="inputDisabled" type="number" v-model.trim="bindCardNo" placeholder="请输入银行卡号" placeholder-style="color: #ABABAB;" />
						<view v-if="!inputDisabled" class="flex-1 jue">
							<image @click="photograph" src="../static/pingan_new/icon6.png" class="pingan-icon"></image>
						</view>
					</view>
					<view class="flex-row alc pingan-input-par">
						<image src="../static/pingan_new/icon4.png" style="width: 28rpx;height: 37rpx;"></image>
						<input 
							:disabled="inputDisabled"
							class="pingan-input" 
							type="number" 
							maxlength="11" 
							v-model.trim="mobileNo" 
							placeholder="绑定卡预留的手机号"  
							placeholder-style="color: #ABABAB;"
							/>
					</view>
					<view class="flex-row alc">
						<view class="pingan-input-par" style="padding-left: 70rpx;">
							<input 
								class="pingan-input" 
								type="number" 
								v-model.trim="code" 
								maxlength="6"  
								style="width: 200rpx;"  
								placeholder="验证码" 
								placeholder-style="color: #ABABAB;"
							/>
						</view>
						<view class="flex-1 jue" style="margin-bottom: 25rpx;">
							<van-button
								customClass="my-submit-btn"
								@click="codeBtnClick" 
								size="small"
								:disabled="loadingCode"
								round 
								custom-style="width: 260rpx;height: 84rpx"
								color="#F65C27"
							>{{btnText}}</van-button>
						</view>
					</view>
					
					
					<view class="rule-checkbox">
						<view class="flex-row">
							<view>
								<van-checkbox custom-class="my-checkbox" style="block: inline" :value="true" :checked-color="checked1 ? '#FE6121': '#D7D7D7'" icon-size="30rpx" shape="square" @click.native="(e)=>{checked1=!checked1}">
								</van-checkbox>
							</view>
							<view>
								<span @click="()=>{checked1=!checked1}">我已阅读并同意</span>
								<span class="rule-item" @click.stop="ruleView(1)">《平安银行快捷支付业务服务协议》</span>
							</view>
						</view>
					</view>
					<view class="pingan-btn">
						<van-button 
							customClass="my-submit-btn"
							@click="btnClick" 
							:loading="loading"
							loading-text="加载中..."
							:disabled="disabled"
							block 
							round 
							color="#F65C27"
						>立即支付（￥3）</van-button>
					</view>
				</view>
			</view>
		</view>
		
		
		<van-dialog id="van-dialog" />
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	
	export default {
		data() {
			return {
				thirdId: '', 
				mobileNo: '', // 手机号
				customerName: '',
				cardId: '',
				bindCardNo: '',
				signId: '', // 签约号
				otpValue: '', // 短信单号
				businessNo: '', // 业务号
				otpOrderNo: '', 
				accountNo: '', // 卡号
				inputDisabled: true, // 默认禁止输入，签约失败时，换卡号时允许输入
				accountNoPage: '', // 页面显示的卡号
				codeSuccess: false, // 是否成功获取验证码
				code: '',
				timer: null,
				payTimer: null,
				loadingCode: false,
				loading: false,
				loadingText: 60,
				btnText: '获取验证码',
				btnTimber: '获取验证码',
				checked1: false,
				subscribeFlag: false,
				payCount: 0,
				rechargeId: null,
				status: 1,
			}
		},
		onLoad (options) {
			this.getPinganUserInfo();
		},
		onUnload () {
			clearInterval(this.timer);
			clearInterval(this.payTimer);
		},
		computed: {
			disabled () {
				let flag = true;
				if (this.codeSuccess && this.checked1) {
					flag = false;
				}
				return flag;
			}
		},
		methods: {
			getPinganUserInfo () {
				apis.homeApis.getPinganUserInfo().then((res)=> {
					if (res.status === 200 && res.data) {
						this.thirdId = res.data.thirdId; // 第三方id
						this.mobileNo = res.data.mobileNo; // 手机号
						this.customerName = res.data.customerName; // 真实姓名
						this.cardId = res.data.cardId; // 身份证号
						this.bindCardNo = res.data.bindCardNo; // 绑定卡号
						this.accountNo = res.data.accountNo; // 三类户卡号
						this.accountNoPage = res.data.accountNo ? res.data.accountNo.replace(/(.{4})/g, "$1 ") : '----'; // 页面显示的卡号
					} else {
						uni.showToast({
							title: res.msg || '系统繁忙，请稍候再试',
							icon: 'none',
							duration: 5000
						});
					}
				}).catch((err) => {
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				})
			},
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			ruleView (type) {
				uni.showLoading({
					title: '加载中...'
				})
				console.log('ruleView')
				let url = 'https://mweb.bolink.club/pingan/'
				if (type === 1) {
					url += 'pingan_fu.pdf';
				}
				wx.downloadFile({
				  url: url,
				  success: (res) => {
					  uni.hideLoading();
				    const filePath = res.tempFilePath
				    wx.openDocument({
				      filePath: filePath,
				      success: (res) => {
						this.checked1 = true;
				      }
				    })
				  },
				  fail: () => {
					  uni.showToast({
					  	title: '打开失败，请稍候再试',
					  	icon: 'none'
					  });
				  }
				})
			},
			codeBtnClick () {
				if (this.inputDisabled) {
					this.getcheckcode();
				} else {
					this.applysign();
				}
			},
			// 代扣获取验证码
			getcheckcode () {
				this.loadingCode = true;
				let params = {
					thirdId: this.thirdId,
					cardId: this.cardId,
					customerName: this.customerName,
					bindCardNo: this.bindCardNo,
					mobileNo: this.mobileNo,
					scene: 'TI001'
				};
				apis.homeApis.getcheckcode(params).then((res)=> {
					if (res.status === 200) {
						this.otpOrderNo = res.data.otpOrderNo;
						this.businessNo = res.data.businessNo;
						this.thirdId = res.data.thirdId;
						this.codeSuccess = true;
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						});
						this.timer = setInterval(() => {
							this.loadingText = this.loadingText - 1;
							this.btnText = '倒计时' + this.loadingText;
							if (this.loadingText === 0) {
								this.btnText = this.btnTimber;
								this.loadingCode = false;
								this.loadingText = 60;
								clearInterval(this.timer)
							}
						}, 1000)
					} else {
						this.loadingCode = false;
						uni.showToast({
							title: res.msg || '系统繁忙，请稍候再试',
							icon: 'none'
						});
					}
				}).catch(() => {
					this.loadingCode = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				})
			},
			clearBtn () {
				this.btnText = this.btnTimber;
				this.loadingCode = false;
				this.loadingText = 60;
				clearInterval(this.timer)
			},
			// 签约获取验证码
			applysign () {
				if (!this.bindCardNo) {
					uni.showToast({
						title: '请先输入银行卡号',
						icon: "none"
					})
					return;
				} else if (!this.mobileNo) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				}
				this.loadingCode = true;
				let params = {
					thirdId: this.thirdId,
					mobileNo: this.mobileNo,
					customerName: this.customerName,
					cardId: this.cardId,
					bindCardNo: this.bindCardNo,
				};
				apis.homeApis.applysign(params).then((res)=> {
					if (res.status === 200) {
						this.signId = res.data.signId;
						this.businessNo = res.data.businessNo;
						this.codeSuccess = true;
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						});
						this.timer = setInterval(() => {
							this.loadingText = this.loadingText - 1;
							this.btnText = '倒计时' + this.loadingText;
							if (this.loadingText === 0) {
								this.clearBtn();
							}
						}, 1000)
					} else {
						this.loadingCode = false;
						uni.showToast({
							title: res.msg || '系统繁忙，请稍候再试',
							icon: 'none',
							duration: 5000
						});
					}
				}).catch(() => {
					this.loadingCode = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				})
			},
			// 点击支付按钮
			btnClick () {
				if (!this.bindCardNo) {
					uni.showToast({
						title: '请先输入银行卡号',
						icon: "none"
					})
					return;
				} else if (!this.mobileNo) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				} else if (!this.codeSuccess) {
					uni.showToast({
						title: '请先获取验证码',
						icon: "none"
					})
					return;
				} else if (!this.code) {
					uni.showToast({
						title: '请先输入验证码',
						icon: "none"
					})
					return;
				} else if (!this.checked1) {
					uni.showToast({
						title: '请先勾选同意',
						icon: "none"
					})
					return;
				}
				let tmplId = getApp().globalData.tmplIds[3];
				// 就首次支付弹出订阅
				if (!this.subscribeFlag && getApp().globalData.myTmplIds.indexOf(tmplId) == -1) {
					uni.requestSubscribeMessage({
						tmplIds: [tmplId],
						success: (res) => {
							let status = 2;
							if (res[tmplId] == "accept") { // 字段就是tmplIds模板id
								status = 1;
							}
							let params = [{
								priTmplId: tmplId,
								openid: uni.getStorageSync('openId'),
								status: status
							}];
							apis.homeApis.subscribeSta(params).then((res) => {
							})
						},
						fail: (err) => {
							console.log('requestSubscribeMessage-err-->',err)
						},
						complete: () => {
							this.subscribeFlag = true;
							if (this.inputDisabled) { // 正常情况下，直接发起代扣
								this.commonTransferIn();
							} else {  // 签约的情况下,需要验证
								this.presigncontract();
							}
						},
					})
				} else {
					if (this.inputDisabled) { // 正常情况下，直接发起代扣
						this.commonTransferIn();
					} else {  // 签约的情况下
						this.presigncontract();
					}
				}
			},
			// 签约验证接口
			presigncontract () {
				this.loading = true;
				let params = {
					thirdId: this.thirdId,
					businessNo: this.businessNo,
					signId: this.signId,
					mobileNo: this.mobileNo,
					customerName: this.customerName,
					cardId: this.cardId,
					bindCardNo: this.bindCardNo,
					otpValue: this.code,
				}
				apis.homeApis.presigncontract(params).then((res) => {
					if (res.status === 200) {
						this.getPinganUserInfo();
						this.inputDisabled = true; // 签约成功，启用正常的发送验证码
						this.loading = false;
						this.code = '';
						this.codeSuccess = false;
						this.clearBtn();
						uni.showToast({
							title: '操作成功，您可以重新发起支付了',
							icon: 'none',
							duration: 5000
						});
					} else {
						uni.showToast({
							title: res.msg || '系统繁忙，请稍候再试',
							icon: 'none',
							duration: 5000
						});
					}
				}).catch(()=>{
					this.loading = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none',
						duration: 5000
					});
				});
			},
			// 代扣接口
			commonTransferIn () {
				let params = {
					thirdId: this.thirdId,
					businessNo: this.businessNo,
					otpOrderNo: this.otpOrderNo,
					otpValue: this.code,
				};
				apis.homeApis.commonTransferIn(params).then((res) => {
					if (res.status === 200) {
						this.payCount = 0;
						this.payTimer = setInterval(() => {
							this.queryStatus(params.thirdId, params.businessNo);
						}, 800)
					} else {
						this.loading = false;
						let msg = res.msg && JSON.parse(res.msg)
						if (msg.responseCode == '61000002') {
							uni.showToast({
								title: '验证码错误',
								icon: 'none',
								duration: 5000
							});
						} else {
							uni.showToast({
								title: msg.responseMsg || '系统繁忙，请稍候再试',
								icon: 'none',
								duration: 5000
							});
						}
					}
				}).catch(()=>{
					this.loading = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none',
						duration: 5000
					});
				});
			},
			// 查询支付的状态
			queryStatus (thirdId, businessNo) {
				uni.showLoading({
					title: '支付中,请稍候...'
				})
				let params = {
					thirdId: thirdId,
					businessNo: businessNo,
				};
				apis.homeApis.transferInResultQuery(params).then((res) => {
					this.payCount += 1;
					if (res.status === 200) {
						this.status = res.data.status || 1;
						if (res.data.status == 3) {
							clearInterval(this.payTimer);
							uni.hideLoading();
							if (res.data.replace == 1) { // 扣款失败，换签约号的情况
								this.inputDisabled = false;
								this.clearBtn();
								this.codeSuccess = false;
								this.bindCardNo = '';
								this.code = '';
								this.loading = false;
								uni.showToast({
									title: '卡余额不足，请更换绑定的银行卡',
									icon: 'none',
									duration: 6000
								});
							} else {
								uni.showToast({
									title: res.msg || '系统繁忙，请稍候再试',
									icon: 'none',
									duration: 5000
								});
							}
						} else if (res.data.status == 4) { // 支付成功的情况
							this.rechargeId = res.data.rechargeId;
							clearInterval(this.payTimer);
							uni.hideLoading();
							uni.reLaunch({
								url: `/pagesA/pingan_new/paySuccess?thirdId=${this.thirdId}&businessNo=${this.businessNo}&rechargeId=${this.rechargeId}&status=${this.status}`,
								complete: (res) => {
									this.loading = false;
								}
							})
						}
					}
					if (this.payCount > 120) {
						uni.showToast({
							title: '系统繁忙，请稍候再试',
							icon: 'none'
						});
						this.loading = false;
						clearInterval(this.payTimer);
					}
				}).catch(res => {
					this.payCount += 1;
				})
			},
			// 调用相机
			photograph () {
				uni.chooseImage({
				    count: 1, 
				    sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
				    sourceType: ['camera', 'album'], // album 从相册选图，camera 使用相机，默认二者都有
				    success: (res) => {
						// console.log(res.tempFilePaths);
						// this.src = res.tempFilePaths[0]
						if(res.tempFiles[0].size <= 1000000){  
							this.imagePath = res.tempFilePaths[0]
							this.imageUpload(res.tempFilePaths[0]);
						} else {
							uni.showToast({
								title:'上传图片不能大于1M!',
								icon:'none'
							})
						}
				    },
					fail: (res) => {
						if (res.errMsg != 'chooseImage:fail cancel') {
							uni.showToast({
								title: '选择图片出错了，请重新尝试',
								icon: 'none'
							});
						}
					}
				});
			},
			// 上传图片识别
			imageUpload (src) {
				apis.homeApis.bankpic(src).then((res)=> {
					console.log(res);
					if (res.data.bank_card_number) {
						this.bank = res.data.bank_card_number;
					}
				}).catch(res=>{
					uni.hideLoading();
					uni.showToast({
						title: '图片上传失败，请稍候再试',
						icon: 'none'
					});
				})
			},
			
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		background-color: #fff;
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 300rpx;
	}
	.my-logo {
		position: absolute;
		top: 200rpx;
		left: calc(50% - 164rpx)
	}
	
	.bank-info {
		height: 246rpx;
		border-radius: 16rpx;
		background-color: #fff;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
		padding: 30rpx 40rpx;
		margin: 30rpx;
		font-size: 26rpx;
		color: #A0A0A0;
	}
	.bank-info-nav {
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #eee;
	}
	.bank-info-logo {
		width: 86rpx;
		height: 50rpx;
		margin-right: 10rpx;
	}
	.bank-info-title {
		margin-left: 10rpx;
		font-size: 36rpx;
		color: #666;
	}
	.bank-info-btn {
		margin-left: 16rpx;
		font-size: 24rpx;
		text-align: center;
		color: #fff;
		width: 167rpx;
		height: 44rpx;
		line-height: 44rpx;
		background: linear-gradient(135deg,#FED47A 0%, #FFBA49);
		border-radius: 20rpx 0px 20rpx 0rpx;
	}
	.bank-info-content {
		padding-top: 20rpx;
		font-size: 28rpx;
		color: #999;
	}
	.bank-info-num {
		color: #333;
		font-size: 44rpx;
		font-weight: 500;
		letter-spacing: 2rpx;
	}
	.bank-info-add {
		color: #999;
		font-size: 24rpx;
	}
	
	.input-box {
		margin: 40rpx 30rpx 40rpx 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
	}
	.top-bg-desc {
		font-size: 30rpx;
		font-weight: 800;
		color: #D14B22;
		line-height: 72rpx;
		width: 100%;
		position: absolute;
		top: 0rpx;
		text-align: center;
	}
	.input-box-content {
		padding: 40rpx 60rpx 60rpx 60rpx;
	}
	.pingan-btn-title {
		padding: 10rpx 10rpx 24rpx 10rpx;
		color: #333;
		font-size: 40rpx;
		margin-bottom: 10rpx;
	}
	
	.top-redbag {
		/* position: absolute;
		bottom: 150rpx;
		left: 100rpx; */
		/* background-color: #fae6cd; */
		border-radius: 12rpx;
	}
	.top-redbag-content {
		padding: 20rpx;
	}
	.top-redbag-item {
		background-color: #F5D39E;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 24rpx;
		color: #A07137;
	}
	.top-redbag-price {
		font-size: 30rpx;
	}
	.top-redbag-num {
		font-size: 52rpx;
	}
</style>
<style>
	.my-checkbox {
		display: inline-block!important;
		vertical-align: middle;
		width: 50rpx;
	}
</style>
