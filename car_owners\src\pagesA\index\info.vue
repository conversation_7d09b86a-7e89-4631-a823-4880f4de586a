<template>
	<view class="info">
		
		<van-tabs :active="active" @change="onChange" color="#2385FF" title-active-color="#2385FF">
		  <!-- <van-tab title="全部"></van-tab> -->
		  <van-tab v-for="(item, index) in typeData" :name="index" :key="item.id" :title="item.typeName"></van-tab>
		</van-tabs>
		
		<view class="info-list" @touchstart='touchStart' @touchend="touchEnd">
			<template v-if="infoData.length > 0">
				<infolist :infoData="infoData" :isInfoList="true" @praiseClick="praiseClick" />
			</template>
			
			<empty v-else />
		</view>
		
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import infolist from "../../components/infolist/infolist.vue";
	import empty from '../../components/empty/empty.vue';
	let app = getApp();
	
	export default {
		components: {
			infolist,
			empty
		},
		data() {
			return {
				active: 0, // tabs选择列
				typeData: [],
				max: 0,
				pageNum: 1,
				pageSize: 20,
				type: 1,
				infoData: []
			};
		},
		onLoad () {
			uni.showLoading({
				title: '请稍候...'
			})
			app.login().then(res => {
				this.initData();
			})
		},
		onPullDownRefresh () {
			this.pageNum = 1;
			this.infoData = [];
			this.getInfo();
		},
		onReachBottom () {
			console.log("---到底了");
			this.pageNum = this.pageNum + 1;
			
			this.getInfo();
			uni.showLoading({
				title: "请稍候",
				mask: true
			})
		},
		onReady () {
			getApp().pagePathRecord();
		},
		methods: {
			// 资讯点赞
			praiseClick (item, index) {
				console.log('item-->', item);
				let state = item.praise;
				let praiseNum = this.infoData[index].praiseNum || 0;
				let praise = state == 1 ? 0 : 1;
				state == 1 ? --praiseNum : ++praiseNum;
				if (praiseNum<0) praiseNum = 0;
				
				this.$set(this.infoData[index], 'praise', praise);
				this.$set(this.infoData[index], 'praiseNum', praiseNum);
				
				let params = {
					articalId: item.id,
					state: state,
				}
				apis.homeApis.praiseArtical(params).then((res) => {
					
				})
			},
			initData () {
				apis.homeApis.getinformationtype().then((res) => {
					console.log(res)
					this.typeData = res.data.sort(this.compare('sortlevel'));
					// this.typeData.unshift({ id: 0, typeName: "全部"});
					this.max = res.data.length;
					this.type = this.typeData[0].id; 
					this.getInfo();
				})
			},
			// 获取资讯列表
			getInfo () {
				let params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					type: this.type == 0 ? -1 : this.type
				}
				apis.homeApis.getInfomationByType(params).then((res) => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					this.infoData = this.infoData.concat(res.data.rows);
					if (res.data.total > 0 && res.data.rows.length == 0) {
						uni.showToast({
							title: "亲，到底了。",
							icon: "none"
						})
					}
				})
			},
			// 数组比较大小
			compare(property){
			    return function(a,b){
			        var value1 = a[property];
			        var value2 = b[property];
			        return value1 - value2;
			    }
			},
			//
			onChange (e) {
				console.log(e.detail);
				uni.showLoading({
					title: '请稍候...'
				})
				this.infoData = [];
				this.pageNum = 1;
				this.type = this.typeData[e.detail.name].id; 
				this.getInfo();
			},
			touchStart(e) {
			  this.startX =  e.changedTouches[0].clientX;
			  this.startY =  e.changedTouches[0].clientY;
			},
			touchEnd(e) {
			  let x = e.changedTouches[0].clientX;
			  let y = e.changedTouches[0].clientY;
			  let turn = this.getTouchData(x,y,this.startX,this.startY);
			  console.log("turn", turn);
			  // console.log("active", this.active);
			  // console.log("max", this.max);
			  if (turn == "left"){
				  if (this.active != this.max) {
					  this.active = this.active + 1;
				  }
			  } else if (turn == "right") {
				  if (this.active != 0) {
					  this.active = this.active - 1;
				  }
			  }
			  // console.log("active-----", this.active);
			  // console.log("max--------", this.max);
			},
			// 判断左滑还是右滑
			getTouchData (endX, endY, startX, startY) {
			  let turn = "";
			  if (endX - startX > 50 && Math.abs(endY - startY) < 50) { //右滑
			    turn = "right";
			  } else if (endX - startX < -50 && Math.abs(endY - startY) < 50) { //左滑
			    turn = "left";
			  }
			  return turn;
			}
		}
	}
</script>

<style lang="less">
	.info {
		width: 100%;
	}
	.info-list {
		background-color: #fff;
		padding: 20rpx 0;
		color: #666;
	}
</style>
