

const CONFIG = {
  // 开发环境配置
  development: {
    loginTitleTxt: '一码App', // 登录页标题
    copyright: '3.1.1', // 版本信息
    baseUrl: "https://mt.bolink.club", // 测试环境
		cloudUrl:"https://sztest1.bolink.club/",//充电桩测试环境
		cloudUrl1:"https://ts.bolink.club/",//充电桩测试环境
	  zldServer: "https://ts.bolink.club/",
    cappUrl: 'https://mt.bolink.club/', // 测试环境
	  advertisementUrl:'https://beta.bolink.club',//h5广告测试环境
    AMapKey: '713843fc2512cbb48eeb284addba5209', // 高德地理服务key
    longitude: 116.398419, // 默认经度
    latitude: 39.909729, // 默认纬度
	  // 毫秒值要大于提交审核时间 六个小时
	  releaseTime: 1675749000000, // 毫秒值 - 每次发行前需要更改 为应对审核失败，无保险服务类目
    signKey: 'L8WD3M51',
    platId: 10000002,
    paySuccessUrl:'https://43.136.214.63:30449/#',//支付成功后跳转的地址  //智慧社区 https://tsweb5.bolink.club/h5/index.html#/pagesF/order/payorder    //充电 https://43.136.214.63:30449/#/pagesF/charge/Recharge
    parkAppid: 'wx962fe9d5c0e2a2c7', // 微信停车关联公众号appid   支付宝需要在  有判断支付宝h5环境parkAppid  改为2017080408033352
    h5Type: 'park', // 打包的h5类型
  },
  // 生产环境配置
  production: {
    loginTitleTxt: '一码App', // 登录页标题
    copyright: '4.4.59', // 版本
	  cloudUrl:  "https://yun.bolink.club/", //充电桩正式环境
	  cloudUrl1:  "https://yun.bolink.club/", //充电桩正式环境
    cappUrl: 'https://m.bolink.club/', // 正式环境
    baseUrl: 'https://m.bolink.club', // 正式环境
    advertisementUrl:'https://s.bolink.club',//h5广告正式环境
    AMapKey: '713843fc2512cbb48eeb284addba5209', // 高德地理服务key
    longitude: 116.398419, // 默认经度
    latitude: 39.909729, // 默认纬度
	  // 毫秒值要大于提交审核时间 六个小时
	  releaseTime: 1686730956000, // 毫秒值 - 每次发行前需要更改 为应对审核失败，无保险服务类目
    signKey: 'PA0X5C70IUQ0NAA4',
    platId: 10000001,
    paySuccessUrl:'https://ele.bolinkpaas.com/#',//支付成功后跳转的地址
    parkAppid: 'wxc4eced1c73751e0b', // 停车关联公众号appid 零碳智慧空间
    h5Type: 'park', // 打包的h5类型
 
  }
}
export default CONFIG[process.env.VUE_APP_MOD || process.env.NODE_ENV]
