<template>
	<view class="search">
		<!-- <view class="custom-back-nav padl"
						:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}">
						<view class="flex-row alc">
							<van-icon @click="goBack" name="arrow-left" color="#000000" size="36rpx" />
							<view @click="goBack" style="margin-left: 4rpx;color: #000000;">搜索</view>
						</view>
					</view> -->
		<view class="bgFFF"></view>
		<view class="search">
			<!-- #ifdef MP-ALIPAY -->
			<van-search :value="searchVal" placeholder="搜索目的地或电站" show-action :clearable="false" shape="round"
				onCancel="cancel" onChange="changeVal" />
			<!-- #endif -->
			<!-- #ifdef MP-WEIXIN || H5 || APP-PLUS -->
			<van-search :value="searchVal" placeholder="搜索目的地或电站" show-action :clearable="false" shape="round" @cancel="cancel"
				@change="changeVal" />
			<!-- #endif -->
		</view>
		<view class="title-type" v-show="searchVal">
			<view :class="activeTitle == 1 ? 'title title-active' : 'title'" @click="handleTitle(1)">目的地（{{ showList ? (
				total || 0) : (searchList.length || 0) }}）</view>
			<view :class="activeTitle == 2 ? 'title title-active' : 'title'" @click="handleTitle(2)">电站（{{ nearbyStationList ? (
				nearbyStationCount || 0) : 0 }}）</view>
		</view>
		<view v-if="searchList.length > 0 || nearbyStationList.length > 0">
				<view :class="{ searchList: true, charge: activeName === 'charge' }" v-show="!showList && activeTitle == 1">
					<view :class="{ searchItemParking: activeName === 'parking', searchItemCharge: activeName === 'charge' }"
						v-for="(item, index) in searchList" :key="index" @click="jumpPage(item)">
						<view :class="{ left: true, chargeLeft: activeName === 'charge' }">
							<view class="flex-row" style="justify-content: space-between;">
								<view class="name limit">
									<van-icon v-if="activeName == 'charge'" class="marr-10"
										name="https://image.bolink.club/Fn_Mnoge7JN7Dn8ypAdyKc9tzPLC" color="#8E8D90" size="24rpx" />
									<span v-for="(text, textindex) in item.title.split('')" :key="textindex"
										:style="{ color: searchVal.indexOf(text) > -1 ? '#3F7CF6' : '' }">
										{{ text }}</span>
								</view>
								<span v-if="activeName == 'charge'" class="padl-40 name blue">附近有{{ item.powerStationCount }}个电站</span>
							</view>
							<view class="address-container " v-if="activeName === 'charge'">
								<view class="address">{{ item.address || '-' }}</view>
								<view class="address-distance">距离{{item.distance}}km</view>
							</view>
						</view>
					</view>
				</view>
				<template v-if="activeTitle == 2 && searchVal">
					<scroll-view :scroll-y="true" @scrolltolower="scrolltolower">
						<view class="chargeContent" v-for="(item, index) in nearbyStationList" @click="jumpCharge(item)">
							<view class="top">
									<view class="parkName-container">
										<view class="parkName">{{item.name}}</view>							
										<view v-if="item.releaseStatus==1" class="station-await">
										可预约
										</view>
									</view>
								<view class="distance">
									<image src="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" mode="aspectFit"></image>
									{{ item.distance + 'km' }}
								</view>
							</view>
							<view v-if="item.companyName || item.busineHours" class="flex-row font-26 fw-400">
								<view class="grey name">
									{{ item.companyName || '' }}
								</view>
								<!-- <view class="station-time-type">
									{{ item | formetBusineHours }}
								</view> -->
<!-- 		<view v-if="item.releaseStatus == 1" class="station-await">
									可预约
								</view> -->
							</view>
							<view class="middle">
								<view class="all">
									<view class="slow" v-if="item.slowAllCount">
									<view class="slow-text"></view>	空{{item.slowSpaceCount || 0}} <view class="grey">/{{item.slowAllCount || 0}}</view>
									</view>
									<view class="fast" v-if="item.quickyAllCount">
										<view class="fast-text"></view>	空{{item.quickySpaceCount || 0}} <view class="grey">/{{item.quickyAllCount || 0}}</view>
									</view>
								</view>
								<view class="priceall">
									<view class="text">￥</view>
									<view class="price">{{ item.minFee }}</view>
									<view class="text">起</view>
								</view>
							</view>
							<view class="flex-row bottom">
								<van-icon name="https://image.bolink.club/Fn_Mnoge7JN7Dn8ypAdyKc9tzPLC" color="#8E8D90" size="24rpx" />
								<view class="address">
									{{ (item.address || '暂无') }}
								</view>
							</view>
						</view>
						<!-- <view v-if="!showAll" class="loadCharge" @click="loadAllCharge">查看全部{{total}}个充电站 ></view> -->
					</scroll-view>
					<view v-show="activeName === 'charge'">
						<view class="list-end">
							<view v-if="nearbyStationList.length > 0">{{ isNearbyEnd ? '亲，到底了' : '上滑自动加载' }}</view>
							<view v-else>暂无相关电站</view>
						</view>
					</view>
				</template>
			<view v-if="searchList.length < 0 || nearbyStationList.length < 0 && !showList" class="emptyData">{{
				activeName === 'charge' && activeTitle === 1 ? '暂无相关目的地' : activeName === 'charge' && activeTitle === 2 ? '暂无相关电站'
				: '暂无搜索结果' }}</view>
		</view>
		<view class="history" v-else-if="!showList">
			<view class="title">
				<view class="name">历史记录</view>
				<image src="../static/history-del.png" mode="" @click="delHistoryList"></image>
			</view>
			<view class="list padb-20">
				<view class="content" v-for="(item, index) in historyList" :key="index" @click="clickHistory(item)">
					{{ item }}
				</view>
			</view>
		</view>

		<!-- <view v-if="activeName==='charge' && !searchVal &&(!searchList || searchList.length==0)">
						<view class="font-32 fw-500 marl-40 mart-20 marb-20">
							附近电站
						</view>
						<view class="search-list" v-if="addressList.length > 0">
							<view class="search-item-charge flex-row" v-for="(item,index) in addressList" :key="index"
								@click="jumpPage(item)">
								<view class="flex-col jus left">
									<view class="flex-row">
										<view class="flex-row name limit fw-500 font-28">
											<span v-for="(text,index) in item.title.split('')" :key="index"
												:style="{color:addressList.indexOf(text)>-1?'#3F7CF6':''}">
												{{text}}</span>
										</view>
										<span class="padl-40 name fw-400 font-28">附近的电站</span>
									</view>
									<view class="address font-26 grey">{{item.address || '-'}}</view>
								</view>
								<view class="flex jue right">
									<van-icon name="https://image.bolink.club/FiaqtbyF7klN2KzZx2mCSesyeraw" color="#8E8D90"
										size="48rpx" />
								</view>
							</view>
						</view>
					</view> -->
		<view v-else-if="activeName === 'charge' && (!searchList || searchList.length === 0)">
			<view style="height: 20rpx; background-color: #E9ECF1; width: 100%;">
			</view>
			<view>
				<view class="font-32 fw-500 marl-30 mart-30">相关目的地</view>
				<view class="searchItem-charge" v-if="addressList.length > 0 && searchList.length == 0">
					<view class="card mart-20" v-for="(item, index) in addressList" :key="index" @click="jumpPage(item)">
						<view class="left">
							<view class="flex-row">
								<view class="name limit flex jus font-32 fw-500">
									<span v-for="(text, index) in item.title.split('')" :key="index"
										:style="{ color: addressList.indexOf(text) > -1 ? '#3F7CF6' : '' }">
										{{ text }}</span>
								</view>

								<span class="flex-row border jue">
									<van-icon name="https://image.bolink.club/FqoJavhkI3D8OoTiW4WXcL-LC_TB" color="#8E8D90" size="24rpx" />
									<view class="name padl-10 font-26 fw-500">
										附近电站
									</view>
								</span>
							</view>
							<view class="flex-row">
								<view class="address limit flex jus font-24">{{ item.address || '-' }}</view>
								<view class="flex-row flex jue distance">
									<view class="flex-row marl-40">
										<van-icon name="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" color="#8E8D90" size="30rpx" />
										<view class="address marl-10 font-32 fw-500">
											{{ item._distance + 'km' }}
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="activeName === 'charge' && showList && activeTitle === 1">
			<view class="titleTop font-32 fw-500">{{ searchVal }}
				<span class="padl-20">
					附近相关电站
				</span>
			</view>
			<scroll-view :scroll-y="true" @scrolltolower="scrolltolower">
				<view class="chargeContent" v-for="(item, index) in chargeList" @click="jumpCharge(item)">
					<view class="top">
						<view class="parkName-container">
								<view class="parkName">{{item.name}}</view>							
								<view v-if="item.releaseStatus==1" class="station-await">
								可预约
								</view>
							</view>
						<view class="distance">
							<image src="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" mode="aspectFit"></image>
							{{ item.distance + 'km' }}
						</view>
					</view>
					<view v-if="item.companyName || item.busineHours" class="flex-row font-26 fw-400">
						<view class="grey name">
							{{ item.companyName || '' }}
						</view>
						<!-- <view class="station-time-type">
							{{ item | formetBusineHours }}
						</view> -->
	<!-- 					<view v-if="item.releaseStatus == 1" class="station-await">
							可预约
						</view> -->
					</view>
					<view class="middle">
						<view class="all">
							<view class="slow" v-if="item.slowAllCount">
									<view class="slow-text"></view>	空{{item.slowSpaceCount || 0}} <view class="grey">/{{item.slowAllCount || 0}}</view>
									</view>
									<view class="fast" v-if="item.quickyAllCount">
										<view class="fast-text"></view>	空{{item.quickySpaceCount || 0}} <view class="grey">/{{item.quickyAllCount || 0}}</view>
									</view>
						</view>
						<view class="priceall">
							<view class="text">￥</view>
							<view class="price">{{ item.minFee }}</view>
							<view class="text">起</view>
						</view>
					</view>
					<view class="flex-row bottom">
						<van-icon name="https://image.bolink.club/Fn_Mnoge7JN7Dn8ypAdyKc9tzPLC" color="#8E8D90" size="24rpx" />
						<view class="address">
							{{ (item.address || '暂无') }}
						</view>
					</view>
				</view>
				<!-- <view v-if="!showAll" class="loadCharge" @click="loadAllCharge">查看全部{{total}}个充电站 ></view> -->
			</scroll-view>
			<view v-show="activeName === 'charge'">
				<view class="list-end">
					<view v-if="chargeList.length > 0">{{ isEnd ? '亲，到底了' : '上滑自动加载' }}</view>
					<view v-else>暂无相关电站</view>

				</view>
			</view>
		</view>

</view>
</template>

<script>
import apis from "../../common/apis/index";
const app = getApp();

export default {
	data() {
		return {
			total: 0,
			nearbyStationCount:0,
			showAll: false,
			chargeList: [],
			nearbyStationList: [],//搜索电站
			showList: false,
			searchList: [],
			activeName: 'parking',
			searchVal: '',
			pageCharge: 1,//电站查询当前页
			activeTitle: 1,
			windowHeight: 0,
			statusBarHeight: 0,
			navHeight: 0,
			historyList: [],
			page_index: 1,
			page_size: 10,
			mobile: uni.getStorageSync('mobile') || '', // 用户手机号
			longitude: '',
			latitude: '',
			addressList: [],
			pageNum: '',
			currentTime: "",
			chargeSearchData: {
				rp: 10,
				longitude: 113.947629,
				latitude: 22.49597,
				distance: 5000,//单位km
				page: 1
			},
			isEnd: false,
			isNearbyEnd: false,//电站列表提示
			deferTimer: null,//搜索框防抖定时器标识符
		}
	},
	onLoad(options) {
		console.log('options-->', options);
		this.activeName = options.activeName
		getApp().getSystemInfo().then(res => {
			this.windowHeight = res.windowHeight;
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight || 40;
		});
		getApp().login().then(res => {

		})
		this.init()
		// #ifdef MP-ALIPAY
		this.$scope.changeVal = this.changeVal.bind(this)
		this.$scope.cancel = this.cancel.bind(this)
		// #endif
	},
	onShow() {
		// 判断是否有token和session_key
		getApp().isloginSuccess();
		if (this.activeName === 'parking') {
			this.historyList = uni.getStorageSync('parkingHistory') ? JSON.parse(uni.getStorageSync(
				'parkingHistory')) : []
		} else {
			this.historyList = uni.getStorageSync('chargeHistory') ? JSON.parse(uni.getStorageSync('chargeHistory')) :
				[]
		}

	},
	onReachBottom() {
		if (!this.searchVal) {
			return false
		}
		if (this.activeName === 'charge' && this.activeTitle == 1) {
			this.chargeSearchData.page += 1
			// 重新执行

			this.queryPowerStationList(this.chargeSearchData)
		} else if (this.activeName === 'charge' && this.activeTitle == 2) {
			this.pageCharge += 1
			this.queryStation(this.searchVal)
		}
	},
	// onPullDownRefresh() {
	// 	if(this.activeName === 'charge'){
	// 		console.log('重新拉取数据')
	// 		let params = {
	// 			longitude: this.longitude, 
	// 			latitude: this.latitude,
	// 			page:1
	// 		}
	// 		this.queryPowerStationList(params)
	// 	}
	// },
	filters: {
		formetBusineHours(item) {
			let { openingTime, closingTime } = item
			return ((openingTime && closingTime) ? openingTime + '~' + closingTime : "24小时") + '营业'
		}
	},
	components: {

	},
	onUnload() {
		clearTimeout(this.deferTimer)

	},
	methods: {
		handleTitle(val) {
			if (this.activeTitle === val) return
			this.activeTitle = val
			if(val===2){
				this.longitude=''
				this.latitude=''
			}

		},
		init() {
			this.pageNum = 1
			if (this.activeName === 'charge') {
				let longitude = uni.getStorageSync("longitude"),
					latitude = uni.getStorageSync("latitude");
				this.showList = false
				let data = {
					location: latitude + ',' + longitude,
					get_poi: 1,
					token: uni.getStorageSync('token'),
					path: '/place/geocoder',
					// #ifdef H5
					isH5Request: 1,
					phone: uni.getStorageSync('mobile'),
					// #endif
				}
				apis.homeApis.mapService(data).then(res => {
					this.addressList = res.result && res.result.pois || []
				}).catch(err => {
					this.addressList = []
				})
			}
		},
		scrolltolower() {
			if (this.showAll) {
				this.page_index = this.page_index + 1;
				this.loadAllCharge()
			}
		},
		changeVal(e) {
			console.log('changeVal', e);
			this.searchVal = e.detail
			// let currentTime = Date.parse(new Date())
			// if (this.currentTime) {
			// 	if (currentTime - this.currentTime <= 1000) return
			// } else {
			// 	this.currentTime = currentTime
			// }
			if (this.searchVal === '') {
				this.activeTitle = 1
				return false
			}

			clearTimeout(this.deferTimer)
			this.deferTimer = setTimeout(() => {
				if (this.activeName === 'charge') {
					this.queryAddress(e.detail)
					this.queryStation(e.detail)
				} else {
					let data = {
						keyWord: e.detail,
						region: 1
					}
					apis.homeApis.suggestion(data).then(res => {
						this.searchList = res.data
					})
				}

			}, 400)

		},
		clickHistory(item) {
			this.searchVal = item
			this.changeVal({
				detail: item
			})
		},
		// loadAllCharge() {
		// 	this.showAll = true
		// 	let data = {
		// 		longitude: this.longitude,
		// 		latitude: this.latitude,
		// 		distance: 5000,
		// 		rp: this.page_size,
		// 		page: this.page_index,
		// 	}
		// 	apis.homeApis.queryPowerStationList(data).then(res => {
		// 		console.log(res);
		//     if (!res.data.voList || res.data.voList.length == 0) {
		// 			this.isEnd = true
		// 			return false
		// 		}
		// 		if (this.page_index === 1) {
		// 			this.chargeList = res.data.voList;
		// 		} else {
		// 			this.chargeList = this.chargeList.concat(res.data.voList);
		// 		}

		// 	})
		// },
		jumpCharge(item) {
			uni.navigateTo({
				url: `/pagesB/map/chargeMessage?item=${encodeURIComponent(JSON.stringify(item))}&longitude=${this.longitude}&latitude=${this.latitude}`
			})
		},
		jumpPage(item) {
			this.searchVal = item.title
			// this.changeVal({
			// 	detail: item.title
			// })
			this.queryStation(item.title)
			if (this.historyList.length > 10) {
				this.historyList.pop()
			}


			// 去重，重排
			let obj = {},
				tempArr = [];
			this.historyList.map(i => {
				if (!obj[i]) {
					obj[i] = true;
					tempArr.push(i)
				}
			})
			let index = tempArr.findIndex(i => i == item.title)
			if (index !== -1) {
				tempArr.splice(index, 1)
			}
			tempArr.unshift(item.title)
			this.historyList = tempArr


			if (this.activeName === 'parking') {
				uni.setStorageSync('parkingHistory', JSON.stringify(tempArr))
				uni.navigateTo({
					url: `/pagesC/park/nearpark?item=${JSON.stringify(item)}`
				})
			} else {
				uni.setStorageSync('chargeHistory', JSON.stringify(tempArr))
				let {
					lat,
					lng
				} = item.location
				this.longitude = lng
				this.latitude = lat
				this.chargeSearchData = {
					rp: 10,
					longitude: lng,
					latitude: lat,
					distance: 10,
					page: 1,
					queryAll: 1
				}
				apis.homeApis.queryPowerStationList(this.chargeSearchData).then(res => {
					console.log(res, '电站列表')
					let list = res.data.voList
					list.forEach((item)=>{
						item.label=item.label ? JSON.parse(item.label) : []

					})
					if (!list || list.length < 10) {
						this.isEnd = true
					} else {
						this.isEnd = false
					}
					this.showList = true
					this.showAll = false
					this.total = res.data.total
					this.chargeList = list
				}).catch((err) => {
					this.chargeList = []
				})
			}

		},
		delHistoryList() {
			if (this.activeName === 'parking') {
				uni.removeStorageSync('parkingHistory')
			} else {
				uni.removeStorageSync('chargeHistory')
			}
			this.historyList = []
		},
		cancel() {
			this.searchVal = ''
			this.activeTitle = 1
			this.searchList = []
			this.nearbyStationList = []
			this.chargeList = []
			this.showList = false
		},
		goBack() {
			uni.navigateBack({
				delta: 1,
			});
		},
		queryPowerStationList(data) {
			apis.homeApis.queryPowerStationList(data).then(res => {
				let list = res.data.voList
				if (!list || list.length < 10) {
					this.isEnd = true
				} else {
					this.isEnd = false
				}
				if (!list || list.length == 0) {
					this.$emit('isEndCharge', true)
					return false
				}

				if (data.page === 1) {
					this.chargeList = list ? list : [];
				} else {
					this.chargeList = this.chargeList.concat(list);
				}
			}).catch((err) => {
				this.chargeList = []
			})
		},
		// 查询充电目的地电站
		queryAddress(detail) {
			this.showList = false
			let data = {
				keyword: detail,
				token: uni.getStorageSync('token'),
				path: '/place/suggestion',
				// #ifdef H5
				isH5Request: 1,
				phone: uni.getStorageSync('mobile'),
				// #endif
			}
			let longitude = uni.getStorageSync("longitude"),
					latitude = uni.getStorageSync("latitude");
			if(this.activeName == 'charge'){
				data.latitude=latitude
				data.longitude=longitude
			}
			apis.homeApis.mapService(data).then(res => {
				let searchList = res.data || []
				if (searchList.length > 5) {
					searchList = searchList.slice(0, 5)
				}
				this.searchList = searchList;
				
			}).catch((err) => {
				this.searchList = []
			})
		},
		//查询电站
		queryStation(detail) {
			let longitude = uni.getStorageSync("longitude"),
				latitude = uni.getStorageSync("latitude");
			let data = {
				keyword: detail,
				longitude: longitude,
				latitude: latitude,
				distance: 10000,
				rp: 10,
				page: this.pageCharge || 1,
				isSupportAppointment: 0,
				queryAll:1
			}
			if (!detail) {
				return false
			}

			apis.homeApis.queryPowerStationList(data).then(res => {

				let list = res.data.voList || []
				list.forEach(item => {
					item.label = item.label ? JSON.parse(item.label) : []
				})
				// if (!res.data.voList || res.data.voList.length == 0) {
				// 	this.isNearbyEnd = true
				// 	this.nearbyStationList = []
				// 	return false
				// }
				if (!list || list.length < 10) {
					this.isNearbyEnd = true
				} else {
					this.isNearbyEnd = false
				}



				if (data.page === 1) {
					this.nearbyStationList = list ? list : [];
					this.nearbyStationCount=res.data.total
				} else {
					this.nearbyStationList = this.nearbyStationList.concat(list);
				}
			}).catch((err) => {
				this.nearbyStationList = []

			})

		},

	}
}
</script>

<style lang="less">
// #ifdef MP-ALIPAY
.padl {
	padding-left: 20px;
}

// #endif
.loadCharge {
	width: 100%;
	font-size: 32rpx;
	text-decoration: underline;
	font-weight: 400;
	color: #B0B6C1;
	line-height: 24px;
	text-align: center;
}

.emptyData {
	text-align: center;
	font-size: 26rpx;
	color: #242E3E;
	height: 50px;
	line-height: 50px;
}

.titleTop {
	margin-top: 10px;
	margin-left: 7%;
}

.chargeContent {
	padding: 0 12px 20px;
	margin: 10px 5%;
	border-radius: 30rpx;
	padding: 0 24rpx 20rpx;
	box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);
	background-color: #fff;

}

.search {
	width: 100%;
}

.bgFFF {
	width: 100%;
	height: 2vh;
	// background-color: #fff;
}

.title-type {
	height: 70rpx;
	line-height: 70rpx;
	width: 100%;
	display: flex;
	justify-content: space-around;
	background-color: #FFF;
	font-size: 26rpx;
	color: #242E3E;

	// padding:0 17%;
	.title {
		width: 170rpx;
		height: 70rpx;
		line-height: 70rpx;
		text-align: center;

	}

	.title-active {
		border-bottom: 6rpx solid #4380FD;
		color: #4380FD;
	}

}

.searchList {
	padding: 20rpx 40rpx;
	margin: 20rpx 30rpx;
	background-color: #FFF;

	&.charge {
		border-radius: 30rpx;
		box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);

		.left {
			padding-top: 24rpx;
		}

		padding: 0 40rpx;


	}

	.searchItemParking {
		height: 64rpx;
		line-height: 64rpx;
		font-size: 26rpx;
		font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
		font-weight: 400;
		color: #242E3E;
	}

	.searchItemCharge {
		font-size: 26rpx;
		font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
		font-weight: 400;
		color: #242E3E;
		display: flex;
		justify-content: space-between;

		.chargeLeft {
			border-bottom: 1rpx solid rgba(130, 142, 166, 0.2);
		}

		&:last-child .chargeLeft {
			border: none;
		}

		.distance {
			line-height: 106rpx;
			font-size: 28rpx;
			font-family: DINAlternate-Bold, DINAlternate;
			font-weight: bold;
			color: #828EA6;

			image {
				width: 24rpx;
				height: 24rpx;
				margin-right: 10rpx;
			}
		}

		.name {
			&.limit {
				max-width: calc(100vw - 310rpx);
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
			}
		}


		.address-container {
			width: 600rpx;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #828EA6;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			word-break: break-all;
			padding: 20rpx 0 20rpx 35rpx;
			display: flex;
			justify-content: space-between;

			.address {
				width: 70%;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #828EA6;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
			}

			.address-distance {
				font-size: 22rpx;
			}

		}
	}
}

.search-list {
	margin: 20rpx 30rpx;

	.search-item-charge {
		background-color: #FFF;
		box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);
		margin: 0 0 15rpx;
		border-radius: 30rpx;
		padding: 20rpx 24rpx 4rpx;

		.left {
			width: calc(100vw - 100rpx);
		}

		.right {
			width: 48rpx;
			margin-bottom: 10rpx;
		}

		.name {
			&.limit {
				max-width: calc(100vw - 350rpx);
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
			}
		}

		.address {
			padding: 20rpx 0;
		}

	}
}



.history {
	padding: 40rpx 32rpx 0 40rpx;

	.title {
		display: flex;
		justify-content: space-between;

		.name {
			font-family: PingFangSC-Medium, PingFang SC;
			color: #242E3E;
			line-height: 48px;
		}

		image {
			width: 48px;
			height: 48px;
		}
	}

	.list {
		.content {
			display: inline-block;
			height: 64rpx;
			padding: 0 20rpx;
			margin: 20rpx 20rpx 0 0;
			border-radius: 32rpx;
			background: #FFFFFF;
			line-height: 64rpx;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #828EA6;
		}
	}
}

/deep/.van-search__content {
	border: 1px solid #A1C1FF !important;
}

/deep/.van-search {
	background: #FBFBFB !important;
}

/deep/.van-button {
	width: 48rpx !important;
	height: 48rpx !important;
	border: none !important;
	background: transparent !important;
	padding: 0 !important;
}

.searchItem-charge {
	margin: 20rpx 30rpx;

	.card {
		border-radius: 30rpx;
		padding: 30rpx 24rpx 17rpx;
		box-shadow: 0px 2px 2rpx 0px rgba(0, 0, 0, 0.04);
		background-color: #FFFFFF;
	}

	.border {
		width: 160rpx;
		margin-left: 20rpx;
		border-radius: 50rpx;
		background-color: #4380FD;
		color: #FFFFFF;
		display: flex;
		justify-content: center;
	}


	.name {
		height: 50%;
		line-height: 53rpx;

		&.limit {
			max-width: calc(100vw - 280rpx);
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			word-break: break-all;
		}
	}

	.address {
		height: 50%;
		line-height: 53rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #828EA6;

		&.limit {
			max-width: calc(100vw - 300rpx);
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			word-break: break-all;
		}
	}

	.distance {
		width: 200rpx;
		padding: 10rpx 0;
	}

}

.blue {
	color: #4380FD;
	font-size: 22rpx;
}
</style>
