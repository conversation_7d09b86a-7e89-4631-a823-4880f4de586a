<template>
	<view class="agreement-container">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<custom-nav-bar title="登录"></custom-nav-bar>
		<!-- #endif -->
		<view class="agreement">
			<view class="yima-login">
				<image :src="icon" class="login"></image>
			</view>
			<template v-if="isWXMobile">
				<view class="text-content">
					<view class="title">
						小程序将获取以下授权
					</view>
					<view class="text">
						将获取您的{{phonetip}}注册手机号以便快速登录，您也可以使用账号登录。我方提供的服务涉及大量线下场景，需要您的手机号以方便联系处理各种问题。
					</view>

				</view>

				<view>
					<view class="login-footer">
						<view class="text-read">
							<van-checkbox :value="checked" @change="checkedColor"
								class="charge-checkbox"></van-checkbox>
							<text>阅读并同意</text>
							<text class="orange" @click="toDetail(0)">《用户协议》</text>及<text class="orange"
								@click="toDetail(1)">《隐私政策》</text><text>并使用</text>
								<text class="text-wx-login">手机号快捷登录</text>
						</view>
						<button class="wx-button" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
							v-if="checked">
							<view class="bl-button--primary">手机号快捷登录</view>
						</button>
						<view class="bl-button bl-button--primary" @click="wxLogin" v-else>手机号快捷登录</view>
						<view class="bl-button btn" @click="isWXMobile=false">输入账号登录</view>
					</view>

				</view>
			</template>
			<template v-else>
				<view class="text-content">
					<view class="title">
						手机验证码登录
					</view>
					<view class="text" style="text-align: center;">
						本机号码未注册将自动创建新账号
					</view>

				</view>
				<view class="phone-code-container">
					<view class="phone">
						<van-cell-group>
							<van-field :value="phone" required clearable label="" placeholder="请输入手机号"
								placeholder-style="font-size: 26rpx;padding:0 0 0 15rpx;color:#9ba6c6"  maxlength="11"
								@change="phoneChange" class="phone" />
						</van-cell-group>
						<image class="img" src="http://image.bolink.club/FmWF0LMVKUDoLbmEN8Pxe_kCsK_N"></image>
					</view>
					<view class="phone code">
						<van-cell-group>
							<van-field :value="code" required clearable label="" placeholder="请输入验证码" maxlength="4"
								placeholder-style="font-size: 26rpx;padding:0 0 0 15rpx;color:#9ba6c6"
								@change="codeChange" class="phone code" />
							<image class="img" src="http://image.bolink.club/FheC-QTkNM93SSjKw-0RUkKzA7kJ"></image>
							<button class="code-tips" :disabled="codeContent !== '获取验证码'" :loading="codeLoading"
								@click="getCode">
								{{ codeContent }}
							</button>
						</van-cell-group>
					</view>
				</view>
				<view>
					<view class="login-footer-phone">
						<view class="text-read">
							<van-checkbox :value="checked" @change="checkedColor"
								class="charge-checkbox"></van-checkbox>
							<text>阅读并同意</text>
							<text class="orange" @click="toDetail(0)">《用户协议》</text>及<text class="orange"
								@click="toDetail(1)">《隐私政策》</text><text>并使用</text>
								<text class="text-wx-login">手机号快捷登录</text>
						</view>
						<view class="bl-button bl-button--primary" @click="phoneLogin" v-if="checked">立即登录</view>
						<view class="bl-button bl-button--primary" @click="wxLogin" v-else>立即登录</view>
					</view>

				</view>

			</template>
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index"
	import util from '../../common/utils/util'
	import BaseConfig from '../../common/config/index.config'
	let app = getApp();
	export default {
		data() {
			return {
				mobile: '',
				checked: false,
				loginType: '', //登录类型是否是单车扫码进来的
				bicycleUrl: '',
				jumpType: 1, //上一个页面过来除了授权手机，1还要跳转到别的菜单页面 2搜索框到搜索页面，3跳转到别的页面 4获取手机不用跳转 5扫码进来的
				activeName: '', //pages/index/index 选中停车，充电，社区标识
				BaseConfig,
				jumpRow: {}, //授权手机后跳转的页面
				jumpUrl: '', //jumpType=3 跳转的路径
				hasChargingOrder: false,
				isEmpower: false, //是否要提示授权手机登录
				isWXMobile: true,
				checked: false,
				phone: '',
				code: '',
				codeContent: '获取验证码',
				phonetip: '微信',
				icon: 'http://image.bolink.club/Fm5vHRmYqyGIC7MLl_SzQlo8VnqV'
			}
		},
		async onLoad(options) {
			// #ifdef MP-WEIXIN
			this.phonetip = '微信'
			let appletConfiguration = util.getAppletConfiguration();
			if(appletConfiguration){
				this.icon = appletConfiguration.icon ? appletConfiguration.icon : this.icon;
			}
			// #endif
			// #ifdef MP-ALIPAY
			this.phonetip = '支付宝'
			// #endif
			if (options && options.loginType == 'bicycle') {
				this.loginType = options.loginType
				let bicycleUrl = uni.getStorageSync('userLoginUrl')
				this.bicycleUrl = bicycleUrl
			}
			if (options.jumpType) {
				this.jumpType = Number(options.jumpType)
				this.activeName = options.activeName ? options.activeName : ''
				this.hasChargingOrder = options.hasChargingOrder ? JSON.parse(options.hasChargingOrder) : false
				this.jumpUrl = options.jumpUrl ? options.jumpUrl : ''

			}
			this.isEmpower = options.isEmpower ? JSON.parse(options.isEmpower) : false
			let jumpRow = uni.getStorageSync("jumpRow")
			this.jumpRow = jumpRow ? JSON.parse(jumpRow) : {}
			await util.initData()
		},

		methods: {
			// 拿到手机号码后需要执行的操作
			async getPhoneJump() {
				var pages = getCurrentPages(); //获取页面
				let beforePage = pages[pages.length - 2]; //上个页面
				let route = beforePage.route
				let activeName = this.activeName
				let row = this.jumpRow
				this.removeMobile(this.mobile)
				this.queryMyWallet()
				if (this.loginType === 'bicycle') {
					let result = await util.chargeUserManagerAdd(this.mobile)
					if (result) {
						uni.reLaunch({
							url: this.bicycleUrl + '&res=1&type=userLogin'
						})
					}

				} else if (this.jumpType === 1) {
					// 跳转icon菜单
					if (route == 'pages/user/index') {
						uni.$emit('updateMobile', this.mobile)
						beforePage.$vm.handleNavItemClick(row);
					} else {
						this.handleNavItemClick(row)
					}
				} else if (this.jumpType === 2) {
					// 首页搜索框跳转到搜索页面
					if (activeName == 'parking') {
						uni.reLaunch({
							url: '/pagesC/park/nearpark?type=search'
						})
					} else if (activeName == 'charge') {
						uni.reLaunch({
							url: '/pagesB/charge/search?activeName=' + activeName
						})
					} else {
						uni.navigateBack({
							delta: 1
						})
						setTimeout(() => {
							beforePage.$vm.clickControlSelect()
						}, 500)

					}

				} else if (this.jumpType === 3) {
					// 跳转到汽车预约充电车位页面需要 携带参数
					if (this.jumpUrl === '/pagesF/charge/parklotList') {
						let parkingChargeInfo = uni.getStorageSync('parkingChargeInfo')
						this.jumpUrl =
							`/pagesF/charge/parklotList?parkingSpace=${parkingChargeInfo.parkingSpace}&chargeInfo=${parkingChargeInfo.chargeInfo}`
						uni.removeStorageSync("parkingChargeInfo")

					} else if (this.jumpUrl === '/pagesC/appointment/choice') {
						let parkInfo = uni.getStorageSync('parkInfo')
						this.jumpUrl =
							`/pagesC/appointment/choice?info=${parkInfo.info}&parkWaitInfo=${parkInfo.parkWaitInfo}`
						uni.removeStorageSync("parkInfo")

					} else if (this.jumpUrl === '/pagesH/index/index') {
						let activeIndex = uni.getStorageSync('activeIndex')
						this.jumpUrl = `/pagesH/index/index?activeIndex=${activeIndex}`
						uni.removeStorageSync("activeIndex")

					}
					uni.redirectTo({
						url: this.jumpUrl,
					})

				} else if (this.jumpType === 5) {
					let code = uni.getStorageSync('dealQrcode')
					util.dealQrcode(code).then(result => {

					}).catch(err => {
						uni.showToast({
							title: err,
							duration: 3000,
							icon: 'none'
						})
						uni.hideLoading();
					})

					uni.removeStorageSync("dealQrcode")
				} else {
					uni.navigateBack({
						delta: 1
					})
				}
			},
			// 手机登录
			async phoneLogin() {
				if (!this.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}
				if (!this.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					})
					return
				}

				let updateMobileData = {
					openId: uni.getStorageSync('openId'),
					mobile: this.phone,
					type: 'custom',
					verifyCode: this.code
				}
				let result = await this.updateMobile(updateMobileData)
				if (result.flag === 0) {
					return
				}
				this.mobile = this.phone
				this.getPhoneJump()

			},
			codeChange(e) {
				this.code = e.detail
			},
			phoneChange(e) {
				this.phone = e.detail
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			countdownFn(n) {
				if (n <= 0) {
					this.codeContent = '获取验证码'
					this.codeLoading = false
				} else {
					this.timer = setTimeout(() => {
						n--
						this.codeContent = `${n}秒后重发`
						this.countdownFn(n)
					}, 1000)
				}
			},
			/* 获取验证码 */
			getCode() {
				if (!this.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}
				let pattern = /^1[3456789]\d{9}$/;
				if (!pattern.test(this.phone)) {
					uni.showToast({
						title: '手机号不正确',
						icon: 'none',
						duration: 3000
					});
					return
				}
				let data = {
					mobile: this.phone
				}
				this.codeLoading = true
				this.countdownFn(60)
				apis.homeApis.verifyCode(data).then(res => {
					if (res.status == 200) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'none',
							duration: 3000
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 3000
						});
					}
					this.codeLoading = false
				}).catch((err) => {
					this.codeLoading = false
				})

			},
			/* 获取钱包数据 */
			async queryMyWallet() {
				try {
					let data = await util.getWallet()
				} catch (error) {
					uni.showToast({
						title: '钱包查询异常',
						icon: 'none'
					})
				}
			},
			updateMobile(updateMobileData) {
				return new Promise((resove, reject) => {
					apis.homeApis.updateMobile(updateMobileData).then(async res => {
						if (res.status == 200) {
							uni.showToast({
								title: '登录成功',
								icon: 'none',
								duration: 3000
							});

							let uid=uni.getStorageSync('uid')
							if(!uid || uid=='undefined'){
									await util.initData()
								}

							return resove({
								flag: 1
							})
						} else if (res.status == 223) {
							Dialog.confirm({
									title: '提示',
									message: '当手机号跟保存的不一致',
									cancelButtonText: '取消',
									confirmButtonText: '确定',
									confirmButtonColor: '#1677FF'
								})
								.then(() => {
									let updateData = {
										openId: uni.getStorageSync('openId'),
										mobile: this.mobile,
										isUpdate: 1
									}
									if (!isWXMobile) {
										updateData.type = 'custom'
										updateData.verifyCode = this.code
									}
									this.updateMobile(updateData)

								})
								.catch(() => {

								});

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 3000
							});
							return resove({
								flag: 0
							})
						}

					}).catch((err) => {
						return resove({
							flag: 0
						})
					})
				})


			},
			getPhoneNumber(e, name) {
				// 别的页面要获取手机
				var pages = getCurrentPages(); //获取页面
				let beforePage = pages[pages.length - 2]; //上个页面
				let route = beforePage.route
				let activeName = this.activeName
				let evt = e
				let row = this.jumpRow
				if (evt.detail && evt.detail.errMsg === "getPhoneNumber:fail user deny") {
					uni.showToast({
						title: '请先获取手机号授权',
						icon: 'none',
						duration: 3000
					});
					return false;
				}
				if (this.isEmpower && this.jumpType === 4 && route === 'pages/user/index') {
					beforePage.$vm.getPhoneNumber(e, name)
					uni.navigateBack({
						delta: 1
					})

				} else if (this.isEmpower) {

					// #ifdef MP-ALIPAY
					my.getPhoneNumber({
						success: (req) => {
							app.getPhoneNumber(evt, this, req).then(async res => {
								if (res) {
									this.mobile = res.mobile
									let updateMobileData = {
										openId: uni.getStorageSync('openId'),
										mobile: res.mobile
									}
									let result = await this.updateMobile(updateMobileData)
									if (result.flag === 0) {
										return
									}
									this.getPhoneJump()
								}
							}).catch(err => {
								console.log("获取失败", err)
							})
						},
						fail: (err) => {
							console.log('授权失败', err)
						}
					})
					// #endif
					// #ifdef MP-WEIXIN
					app.getPhoneNumber(evt, this).then(async res => {
						if (res) {
							this.mobile = res.mobile
							let updateMobileData = {
								openId: uni.getStorageSync('openId'),
								mobile: res.mobile
							}
							let result = await this.updateMobile(updateMobileData)
							if (result.flag === 0) {
								return
							}
							this.getPhoneJump()
						}			

					}).catch(err => {
						console.log("获取失败", err)
					})
					// #endif
					return false;

				} else {
					beforePage.$vm.getPhoneNumber(e, name)
				}

			},
			handleNavItemClick(row) {
				row = this.jumpRow
				let that = this
				let itemClick = {
					'charge'() {
						let jumpPath = row.path;
						let startTime = new Date().getTime();
						// 页内跳转
						if (row.target === 'tab') {
							uni.switchTab({
								url: jumpPath
							});
							return
						} else if (row.flag == "chargingOrder") {
							if (that.hasChargingOrder) {
								jumpPath = '/pagesF/charge/state'
							}
						}
						let meta = row.meta;
						uni.navigateTo({
							url: jumpPath,
							complete: (res) => {
								getApp().eventRecord({
									keyWord: meta.keyWord,
									clickType: meta.clickType,
									jumpType: meta.jumpType,
									jumpDesc: meta.jumpDesc,
									result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
									startTime: startTime,
									endTime: new Date().getTime()
								});
							}
						})
					},
					'parking'() {
						// #ifdef MP-ALIPAY
						if (row.name == '违章查询' || row.name == '优惠加油') {
							uni.showToast({
								title: '功能暂未开通，敬请期待',
								icon: 'none',
								duration: 3000
							});
							return false
						}
						// #endif

						let startTime = new Date().getTime();
						if (row.flag === 'promotion') {
							return this.getOilAdPath(row);
						}
						if (row.icon === '') {
							return false
						}
						if (row.path === undefined || row.path === '') {
							return this.hintModel()
						}

						// 重新组装 path 数据
						let jumpPath = row.path;
						if (row.name == "附近车场") {
							jumpPath += "?type=auto"
						}
						// 如果标识是"ladderControl"，并且有梯控的项目id,说明不是第一次使用
						if (row.flag === 'ladderControl') {
							let projectId = uni.getStorageSync('bl_community_ladder_id') || '';
							let projectName = uni.getStorageSync('bl_community_name') || '';
							if (projectId === '') {
								uni.navigateTo({
									url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
									complete: (res) => {

									}
								})
								return false;
							}

							jumpPath += `?id=${projectId}&name=${projectName}`;
						}

						// 如果标识是"openDoor",并且有项目id,说明不是第一次使用
						if (row.flag === 'openDoor' || row.flag === 'multiply') {
							let bl_community_id = uni.getStorageSync('bl_community_id') || '';
							if (bl_community_id === '') {
								uni.navigateTo({
									url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
									complete: (res) => {

									}
								})
								return false;
							}
						}

						// 页内跳转
						if (row.target === 'tab') {
							uni.switchTab({
								url: jumpPath
							});
						} else if (row.target === 'url') {
							if (row.path === 'placement_information') {
								if (Math.round(new Date()) > BaseConfig.releaseTime) {
									getApp().jumpWeiBao('weibao-车险服务导航');
								} else {
									uni.showToast({
										title: '功能暂未开通，敬请期待',
										icon: 'none',
										duration: 3000
									});
								}
								return false;
							}
							getApp().jumpAd({
								url: jumpPath,
								keyWord: row.meta.keyWord
							});
						} else if (row.target === 'mini_program') {
							let meta = row.meta;
							uni.navigateToMiniProgram({
								appId: meta.appId,
								path: jumpPath,
								complete: (res) => {
									getApp().eventRecord({
										keyWord: meta.keyWord,
										clickType: 'Button',
										jumpType: meta.jumpType,
										jumpDesc: meta.jumpDesc,
										result: res.errMsg == 'navigateToMiniProgram:ok' ?
											'成功' : '失败',
										startTime: startTime,
										endTime: new Date().getTime()
									});
								}
							})
						} else {
							let meta = row.meta;
							uni.navigateTo({
								url: jumpPath,
								complete: (res) => {
									getApp().eventRecord({
										keyWord: meta.keyWord,
										clickType: meta.clickType,
										jumpType: meta.jumpType,
										jumpDesc: meta.jumpDesc,
										result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
										startTime: startTime,
										endTime: new Date().getTime()
									});
								},
								fail: (err) => {


								}
							})
						}
					},
					'control'() {
						let startTime = new Date().getTime();
						if (row.flag === 'promotion') {
							return this.getOilAdPath(row);
						}
						if (row.icon === '') {
							return false
						}
						if (row.path === undefined || row.path === '') {
							return this.hintModel()
						}
						// 重新组装 path 数据
						let jumpPath = row.path;
						// 如果标识是"ladderControl"，并且有梯控的项目id,说明不是第一次使用
						if (row.flag === 'ladderControl') {
							let projectId = uni.getStorageSync('bl_community_ladder_id') || '';
							let projectName = uni.getStorageSync('bl_community_name') || '';
							if (projectId === '') {
								uni.navigateTo({
									url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
									complete: (res) => {

									}
								})
								return false;
							}

							jumpPath += `?id=${projectId}&name=${projectName}`;
						}


						// 如果标识是"openDoor",并且有项目id,说明不是第一次使用
						if (row.flag === 'openDoor' || row.flag === 'multiply' || row.flag === 'notice' || row
							.flag ===
							'phone' || row.flag === 'report') {

							let bl_community_id = uni.getStorageSync('bl_community_id') || '';
							if (bl_community_id === '') {
								uni.navigateTo({
									url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
									complete: (res) => {

									},

								})
								return false;
							}
						}
						// 页内跳转
						if (row.target === 'tab') {
							uni.switchTab({
								url: jumpPath
							});
						} else if (row.target === 'url') {
							if (row.path === 'placement_information') {
								if (Math.round(new Date()) > BaseConfig.releaseTime) {
									getApp().jumpWeiBao('weibao-车险服务导航');
								} else {
									uni.showToast({
										title: '功能暂未开通，敬请期待',
										icon: 'none',
										duration: 3000
									});
								}
								return false;
							}
							getApp().jumpAd({
								url: jumpPath,
								keyWord: row.meta.keyWord
							});
						} else if (row.target === 'mini_program') {
							let meta = row.meta;
							uni.navigateToMiniProgram({
								appId: meta.appId,
								path: jumpPath,
								complete: (res) => {
									getApp().eventRecord({
										keyWord: meta.keyWord,
										clickType: 'Button',
										jumpType: meta.jumpType,
										jumpDesc: meta.jumpDesc,
										result: res.errMsg == 'navigateToMiniProgram:ok' ?
											'成功' : '失败',
										startTime: startTime,
										endTime: new Date().getTime()
									});
								}
							})
						} else {
							let meta = row.meta;
							uni.navigateTo({
								url: jumpPath,
								complete: (res) => {
									getApp().eventRecord({
										keyWord: meta.keyWord,
										clickType: meta.clickType,
										jumpType: meta.jumpType,
										jumpDesc: meta.jumpDesc,
										result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
										startTime: startTime,
										endTime: new Date().getTime()
									});
								}
							})
						}

					}
				}
				itemClick[this.activeName] && itemClick[this.activeName]()
			},
			hintModel(text) {
				uni.showModal({
					title: '',
					showCancel: false,
					confirmText: '好的',
					content: text || '暂无此功能，敬请期待',
					success: function(res) {

					}
				});
			},
			getOilAdPath(row) {
				apis.homeApis.getOilAdPath({
					mobile: uni.getStorageSync('mobile'),
					openid: uni.getStorageSync('openId')
				}).then(res => {
					if (res.status === 200 && res.data) {
						let data = res.data;
						getApp().jumpAd({
							url: data.redirectUrl,
							keyWord: row.meta.keyWord
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 3000
					});
				})
			},

			//新获取的手机和缓存手机号不一样删除原来缓存的数据
			removeMobile(mobile) {
				if (mobile != uni.getStorageSync("mobile")) {
					uni.removeStorageSync("bl_door_access_group")
					uni.removeStorageSync("bl_user_info")
					uni.removeStorageSync("bl_door_control_array")
					uni.removeStorageSync("bl_door_control_group")
					uni.removeStorageSync("bl_community_id")
					uni.removeStorageSync("bl_community_ladder_id")
					uni.removeStorageSync("bl_community_name")
					uni.setStorageSync("mobile", mobile)
				}

			},
			wxLogin() {
				uni.showToast({
					title: '请阅读并同意用户协议和隐私政策',
					icon: 'none',
					duration: 2000,
				})

			},
			checkedColor() {
				this.checked = !this.checked
			},

			toDetail(num) {
				let url =
					num == 0 ? "/pagesA/agreement/user" : "/pagesA/agreement/privacy"
				let startTime = new Date().getTime()
				uni.navigateTo({
					url: url,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: `
												协议列表`,
							clickType: "Button",
							jumpType: "本程序页面",
							jumpDesc: "查看协议明细",
							result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
							startTime: startTime,
							endTime: new Date().getTime(),
						})
					},
				})
			},
		},
	}
</script>

<style lang="less" scoped>
	page {
		background-color: #f0f2f5;
	}

	.agreement-container {
		width: 100%;
		//#ifdef MP-WEIXIN || APP-PLUS
		margin: 180rpx 40rpx 0 40rpx;
		// #endif
		// #ifdef MP-ALIPAY
		margin: 80rpx 40rpx 0 40rpx;
		// #endif

		.yima-login {
			text-align: center;

			.login {
				width: 200rpx;
				height: 200rpx;
				border-radius: 50%;
			}

		}

		.text-content {
			font-size: 28rpx;
			color: #242e3e;

			.title {
				text-align: center;
				font-weight: bold;
				margin: 40rpx 0;
			}
		}

		.login-footer {
			position: fixed;
			bottom: 50rpx;
			font-weight: bold;
			width: 100%;
			left: 0;
			padding: 0 40rpx;
			font-size: 28rpx;

			.btn {
				color: #242e3e;
				text-align: center !important;
				font-size: 28rpx;
				border: 1rpx solid #d8d8d8;
				width: 100%;
				margin: 50rpx auto 0 auto;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 20rpx;
			}

		}

	}

	.text-read {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: flex-start;
		font-weight: bold;
		margin: 0 0 30rpx 0;
	}

	.orange {
		color: #fc954c;
	}

	.text-wx-login {
		text-indent: 1.3em;
	}


	.bl-button--primary {
		width: 100%;
		margin: 0 auto;
		background: #24be7d;
		text-align: center !important;
		height: 80rpx;
		color: #ffffff;
		line-height: 80rpx;
		border-radius: 20rpx;
		font-size: 28rpx;
	}

	::v-deep .van-checkbox__icon {
		font-size: 28rpx !important;
		margin-right: 10rpx;

	}

	.phone-code-container {
		margin-top: 100rpx;

		.phone {
			margin: 20rpx 0;
			position: relative;

			.img {
				position: absolute;
				left: 10rpx;
				top: 10rpx;
				width: 50rpx;
				height: 50rpx;
			}

		}

		.code {
			.img {
				top: 15rpx;
				width: 40rpx;
				height: 40rpx;
			}

			.code-tips {
				font-size: 26rpx;
				position: absolute;
				right: 50rpx;
				top: 0;
				color: #329639;
				font-weight: bold;
				background-color: #f1f2f6;
				height: 60rpx;
				z-index: 99;
				// #ifdef MP-ALIPAY
				line-height: 80rpx;
				// #endif
			}
		}
	}

	::v-deep .van-cell {
		background-color: #f1f2f6;
		height: 70rpx;
		padding: 10rpx 16rpx 10rpx 80rpx;
		line-height: 70rpx;
	}

	::v-deep .van-checkbox__icon {
		font-size: 28rpx !important;
		margin-right: 10rpx;

	}

	::v-deep .van-cell--required:before {
		display: none;
	}

	::v-deep .van-cell__title {
		display: none;
	}
	// #ifdef MP-ALIPAY
	::v-deep .phone .icon-index {
		transform: translateX(80px);
	}
	::v-deep .code .icon-index {
		margin-top:6rpx;
	}
	// #endif
	.login-footer-phone {
		margin-top: 80rpx;
		font-weight: bold;
		width: 100%;
		left: 0;
		font-size: 28rpx;
	}
</style>