{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../packages/plugins/fillWarpText/index.ts"], "names": [], "mappings": "AAsBA,MAAM,MAAM,GAAqB;IAC/B,IAAI,EAAE,sBAAsB;IAC5B,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;QACnB,GAAG,CAAC,YAAY,GAAG,CAAC,MAAM,EAAE,EAAE;YAC5B,MAAM,OAAO,GAAG,CAAC,MAAM,GAAG;gBACxB,QAAQ,EAAE,GAAG;gBACb,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC5C,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG;gBACzC,SAAS,EAAE,EAAE;gBACb,IAAI,EAAE,IAAI;gBACV,GAAG,MAAM;aACV,CAAC,CAAA;YACF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAA;YAC/E,gBAAgB;YAChB,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAA;aAC5D;YACD,YAAY;YACZ,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACpC,aAAa;YACb,IAAI,GAAG,GAAa,EAAE,CAAA;YACtB,QAAQ;YACR,IAAI,IAAI,GAAG,EAAE,CAAA;YACb,IAAI,SAAS,EAAE;gBACb,GAAG,GAAG,GAAG,CAAA;aACV;iBAAM;gBACL,iBAAiB;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnC,yBAAyB;oBACzB,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE;wBACtB,MAAK;qBACN;oBACD,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,QAAQ,EAAE;wBAC1C,mBAAmB;wBACnB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAA;qBACf;yBAAM;wBACL,sBAAsB;wBACtB,CAAC,EAAE,CAAA;wBACH,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACd,IAAI,GAAG,EAAE,CAAA;qBACV;iBACF;gBACD,oBAAoB;gBACpB,IAAI,IAAI,EAAE;oBACR,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACf;gBACD,eAAe;gBACf,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE;oBACtB,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;oBACzB,QAAQ;oBACR,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,CAAA;oBACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACxC,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAA;wBAC5D,IAAI,YAAY,GAAG,QAAQ,EAAE;4BAC3B,6BAA6B;4BAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;4BAClC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;yBACrC;6BAAM;4BACL,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;4BACjB,MAAK;yBACN;qBACF;iBACF;aACF;YACD,YAAY;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAC7B,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,UAAU,EAAE,CAAC,EAAE,CAAA;gBACzD,WAAW;gBACX,IAAI,IAAI;oBAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;gBACjD,OAAO,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;IACH,CAAC;CACF,CAAA;AAED,eAAe,GAAG,EAAE,CAAC,MAAM,CAAA"}