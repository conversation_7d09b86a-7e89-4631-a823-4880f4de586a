
// Regular Expressions for parsing tags and attributes
var startTag = /^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/;
var endTag = /^<\/([-A-Za-z0-9_]+)[^>]*>/;
var attr = /([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g; // Empty Elements - HTML 5

var empty = makeMap('area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr'); // Block Elements - HTML 5
// fixed by xxx 将 ins 标签从块级名单中移除

var block = makeMap('a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video'); // Inline Elements - HTML 5

var inline = makeMap('abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var'); // Elements that you can, intentionally, leave open
// (and which close themselves)

var closeSelf = makeMap('colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr'); // Attributes that have their values filled in disabled="disabled"

var fillAttrs = makeMap('checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected'); // Special Elements (can contain anything)

var special = makeMap('script,style');
function HTMLParser(html, handler) {
  var index;
  var chars;
  var match;
  var stack = [];
  var last = html;

  stack.last = function () {
    return this[this.length - 1];
  };

  while (html) {
    chars = true; // Make sure we're not in a script or style element

    if (!stack.last() || !special[stack.last()]) {
      // Comment
      if (html.indexOf('<!--') == 0) {
        index = html.indexOf('-->');

        if (index >= 0) {
          if (handler.comment) {
            handler.comment(html.substring(4, index));
          }

          html = html.substring(index + 3);
          chars = false;
        } // end tag

      } else if (html.indexOf('</') == 0) {
        match = html.match(endTag);

        if (match) {
          html = html.substring(match[0].length);
          match[0].replace(endTag, parseEndTag);
          chars = false;
        } // start tag

      } else if (html.indexOf('<') == 0) {
        match = html.match(startTag);

        if (match) {
          html = html.substring(match[0].length);
          match[0].replace(startTag, parseStartTag);
          chars = false;
        }
      }

      if (chars) {
        index = html.indexOf('<');
        var text = index < 0 ? html : html.substring(0, index);
        html = index < 0 ? '' : html.substring(index);

        if (handler.chars) {
          handler.chars(text);
        }
      }
    } else {
      html = html.replace(new RegExp('([\\s\\S]*?)<\/' + stack.last() + '[^>]*>'), function (all, text) {
        text = text.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g, '$1$2');

        if (handler.chars) {
          handler.chars(text);
        }

        return '';
      });
      parseEndTag('', stack.last());
    }

    if (html == last) {
      throw 'Parse Error: ' + html;
    }

    last = html;
  } // Clean up any remaining tags


  parseEndTag();

  function parseStartTag(tag, tagName, rest, unary) {
    tagName = tagName.toLowerCase();

    if (block[tagName]) {
      while (stack.last() && inline[stack.last()]) {
        parseEndTag('', stack.last());
      }
    }

    if (closeSelf[tagName] && stack.last() == tagName) {
      parseEndTag('', tagName);
    }

    unary = empty[tagName] || !!unary;

    if (!unary) {
      stack.push(tagName);
    }

    if (handler.start) {
      var attrs = [];
      rest.replace(attr, function (match, name) {
        var value = arguments[2] ? arguments[2] : arguments[3] ? arguments[3] : arguments[4] ? arguments[4] : fillAttrs[name] ? name : '';
        attrs.push({
          name: name,
          value: value,
          escaped: value.replace(/(^|[^\\])"/g, '$1\\\"') // "

        });
      });

      if (handler.start) {
        handler.start(tagName, attrs, unary);
      }
    }
  }

  function parseEndTag(tag, tagName) {
    // If no tag name is provided, clean shop
    if (!tagName) {
      var pos = 0;
    } // Find the closest opened tag of the same type
    else {
        for (var pos = stack.length - 1; pos >= 0; pos--) {
          if (stack[pos] == tagName) {
            break;
          }
        }
      }

    if (pos >= 0) {
      // Close all the open elements, up the stack
      for (var i = stack.length - 1; i >= pos; i--) {
        if (handler.end) {
          handler.end(stack[i]);
        }
      } // Remove the open elements from the stack


      stack.length = pos;
    }
  }
}

function makeMap(str) {
  var obj = {};
  var items = str.split(',');

  for (var i = 0; i < items.length; i++) {
    obj[items[i]] = true;
  }

  return obj;
}

function removeDOCTYPE(html) {
  return html.replace(/<\?xml.*\?>\n/, '').replace(/<!doctype.*>\n/, '').replace(/<!DOCTYPE.*>\n/, '');
}

function parseAttrs(attrs) {
  return attrs.reduce(function (pre, attr) {
    var value = attr.value;
    var name = attr.name;

    if (pre[name]) {
			pre[name] = pre[name] + " " + value;
    } else {
			pre[name] = value;
    }

    return pre;
  }, {});
}

function parseHtml(html) {
  html = removeDOCTYPE(html);
  var stacks = [];
  var results = {
    node: 'root',
    children: []
  };
  HTMLParser(html, {
    start: function start(tag, attrs, unary) {
      var node = {
        name: tag
      };

      if (attrs.length !== 0) {
        node.attrs = parseAttrs(attrs);
      }

      if (unary) {
        var parent = stacks[0] || results;

        if (!parent.children) {
          parent.children = [];
        }

        parent.children.push(node);
      } else {
        stacks.unshift(node);
      }
    },
    end: function end(tag) {
      var node = stacks.shift();
      if (node.name !== tag) console.error('invalid state: mismatch end tag');

      if (stacks.length === 0) {
        results.children.push(node);
      } else {
        var parent = stacks[0];

        if (!parent.children) {
          parent.children = [];
        }

        parent.children.push(node);
      }
    },
    chars: function chars(text) {
      var node = {
        type: 'text',
        text: text
      };

      if (stacks.length === 0) {
        results.children.push(node);
      } else {
        var parent = stacks[0];

        if (!parent.children) {
          parent.children = [];
        }

        parent.children.push(node);
      }
    },
    comment: function comment(text) {
      var node = {
        node: 'comment',
        text: text
      };
      var parent = stacks[0];

      if (!parent.children) {
        parent.children = [];
      }

      parent.children.push(node);
    }
  });
  return results.children;
}

// 富文本处理
function richText(content) {
  let res
  res = content.replace(/<img[^>]*>/gi, function(match, capture) {
    var match = match.replace(/style=\"(.*)\"/gi, '')
    return match
  })
  res = res.replace(/\<img/gi, '<img style="width:100%;height:auto;"')
  return res
}

// 校验-车辆识别代号
function checkVin(vin) {
  let flag = true
  const vinWei = [8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2]
  const vinVal = { 0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9,
    A: 1, B: 2, C: 3, D: 4, E: 5, F: 6, G: 7, H: 8,
    J: 1, K: 2, L: 3, M: 4, N: 5, P: 7, R: 9, S: 2,
    T: 3, U: 4, V: 5, W: 6, X: 7, Y: 8, Z: 9 }
  if (!vin || vin == ' ' || vin.indexOf('O') >= 0 || vin.indexOf('I') >= 0 || vin.indexOf('Q') >= 0) {
    flag = false
  } else {
    // 长度为17
    if (vin.length == 17) {
      let amount = 0
      const vinArr = vin.split('')
      for (let i = 0; i < vinArr.length; i++) {
        // VIN码从从第一位开始，码数字的对应值×该位的加权值，计算全部17位的乘积值相加
        amount += vinVal[vinArr[i]] * vinWei[i]
      }
      // 乘积值相加除以11、若余数为10，即为字母Ｘ
      if (amount % 11 == 10) {
        if (vinArr[8] == 'X') {
          flag = true
        } else {
          flag = false
        }
      } else {
        // VIN码从从第一位开始，码数字的对应值×该位的加权值，
        // 计算全部17位的乘积值相加除以11，所得的余数，即为第九位校验值
        if (amount % 11 != vinVal[vinArr[8]]) {
          flag = false
        } else {
          flag = true
        }
      }
    }
    // 长度不为17
    if (vin.length != 17) {
      flag = false
    }
  }
  return flag
}

// 年检时间计算
function yearCheck(registrationTime, carType, carApplication) {
  // console.log('registrationTime---->', registrationTime, carType, carApplication)
  if (!registrationTime || registrationTime == ' ') {
    return '- - '
  }
  // registrationTime = '2018-11-06';
  const s1 = new Date(registrationTime.trim().replace(/-/g, '/'))
  // let s2 = new Date('2020-11-06');
  const s2 = new Date()
  const s1DateObj = { year: s1.getFullYear(), month: s1.getMonth() + 1, day: s1.getDate() }
  const s2DateObj = { year: s2.getFullYear(), month: s2.getMonth() + 1, day: s2.getDate() }
  // console.log('s1DateObj--s2DateObj-->', s1DateObj, s2DateObj);

  const difTime = parseInt((s2.getTime() - s1.getTime()))
  const difYear = s2DateObj.year - s1DateObj.year // 相差年份
  const difMonth = s2DateObj.month - s1DateObj.month // 相差月份
  const difDate = s2DateObj.day - s1DateObj.day // 相差天
  const difDay = Math.floor(difTime / 86400000) // 相差的总天数

  // console.log('difYear--difMonth--difDay-->', difYear, difMonth, difDate);

  let temp = 0; let nextYear; let dyear = 0; let dmonth = 0

  if (carType == 1 || carType == 2 || carType == 6 || carType == 7) {
    // 小型、微型非营运载客汽车10年内，每2年检测一次，10年-15年的每年检一次，超过15年的，每6个月检验1次。
    if (difYear < 10 || (difYear == 10 && difMonth < 0) || (difYear == 10 && difMonth == 0 && difDate <= 0)) {
      dyear = difYear % 2
      dmonth = s1DateObj.month
      if (difYear % 2 == 0) { // 整两年，往下比较月份
        if (difMonth > 0) { // 月份比注册的大，加2年进入下次年检
          dyear = difYear % 2 + 2
        } else if (difMonth == 0) { // 月份一样，往下比较日期
          if (difDate > 0) {
            dyear = difYear % 2 + 2
          }
        }
      }
    } else if (difYear < 15 || (difYear == 15 && difMonth < 0) || (difYear == 15 && difMonth == 0 && difDate <= 0)) {
      dyear = 0
      dmonth = s1DateObj.month
      if (difMonth > 0) {
        dyear = 1
      } else if (difMonth == 0) { // 月份一样，往下比较日期
        if (difDate > 0) {
          dyear = 1
        }
      }
    } else { // 超过15年的,每6个月检验1次
      dyear = 0
      dmonth = (s1DateObj.month + 6) % 12
      if (difMonth < 6) {
      } else if (difMonth == 6) {
        if (difDate > 0) {
          dyear = 1
          dmonth = (s1DateObj.month + 12) % 12
        }
      } else if (difMonth > 6) {
        dyear = 1
        dmonth = (s1DateObj.month + 12) % 12
      }
      console.log('dmonth---->', dmonth)
    }
  } else if (carType == 3 || carType == 4 || carType == 5) {
    // 载货汽车和大型、中型非营运载客汽车10年以内每年检验1次，超过10年的，每6个月检验1次。
    if (difYear <= 10) {
      temp = 365 - difDay % 365
    } else {
      temp = 182 - difDay % 182
    }
  } else {
    temp = '- - '
  }
  if (carApplication == 2 && (carType == 1 || carType == 2)) {
    // 营运载客汽车5年以内每年检验1次，超过5年的，每6个月检验1次。
    if (difYear <= 5) {
      temp = 365 - difDay % 365
    } else {
      temp = 182 - difDay % 182
    }
  }
  // ios端的时间格式必须为 /连接； 2020/10/10
  nextYear = (s2DateObj.year + dyear) + '/' + dmonth + '/' + s1DateObj.day
  temp = Math.floor((new Date(nextYear).getTime() - s2.getTime()) / 86400000)
  // console.log('nextYear-temp---->', nextYear, temp);
  return temp < 0 ? 0 : temp
}

// 车牌号校验
function isLicensePlate(licensePlate) {
  let result = false
  const express = /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领临无A-Z0-9]{1}[a-zA-Z](([a-kA-K]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[a-kA-K]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/
  result = express.test(licensePlate)
  return result
}

// 时间戳转时间 默认type="dateTime" 返回-年月日时分秒，type="date" 返回-年月日
function timestampToTime(timestamp, type = 'dateTime') { // 支持毫秒级时间戳
  let tt = timestamp * 1000
  if (tt.toString().length > 13) {
    tt = parseInt(timestamp)
  }
  const date = new Date(tt)

  const Y = date.getFullYear() + '.'
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '.'
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  const h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':'
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes() + ':'
  const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  const res = type == 'dateTime' ? Y + M + D + ' ' + h + m + s : Y + M + D
  return res
}
// 时间戳转时间 默认type="dateTime" 返回-年月日时分秒，type="date" 返回-年月日 不展示秒
function timestampToTimeSeconds(timestamp, type = 'dateTime') { // 支持毫秒级时间戳
  let tt = timestamp * 1000
  if (tt.toString().length > 13) {
    tt = parseInt(timestamp)
  }
  const date = new Date(tt)

  const Y = date.getFullYear() + '.'
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '.'
  const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  const h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':'
  const m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes()
  const res = type == 'dateTime' ? Y + M + D + ' ' + h + m : Y + M + D
  return res
}

// 分钟 转 年月日时分
function getDateByminute(minute) { // 一个月默认30天
  let residue = minute
  const model = {}
  let dateStr = ''
  const hourMinute = 60
  const dayMinute = hourMinute * 24
  const monthMinute = dayMinute * 30// 一月默认是30天
  const yearMinute = dayMinute * 365

  if (residue >= yearMinute) { // 年
    const year = parseInt(residue / yearMinute)
    // ppo.settings.sumDuration.year = year;
    model.year = year
    dateStr += year + '年'
    residue = residue % yearMinute// 剩分钟
  }
  if (residue >= monthMinute) { // 月
    const month = parseInt(residue / monthMinute)
    model.month = month
    dateStr += month + '月'
    residue = residue % monthMinute// 剩分钟
  }
  if (residue >= dayMinute) { // 日
    const day = parseInt(residue / dayMinute)
    model.day = day
    dateStr += day + '天'
    residue = residue % dayMinute// 剩分钟
  }
  if (residue >= hourMinute) { // 时
    const hour = parseInt(residue / hourMinute)
    model.hour = hour
    dateStr += hour + '小时'
    residue = residue % hourMinute// 剩分钟
  }
  if (residue < hourMinute) { // 分
    model.minute = residue
    dateStr += residue + '分钟'
    return dateStr
  }
}

/**
   * 将时间戳转换为时长格式
   * @param {number} timestamp - 时间戳（以毫秒为单位）
   * @returns {string} - 转换后的时长格式
   */
function formatDuration(timestamp) {
  const totalSeconds = Math.floor(timestamp / 1000);
  const days = Math.floor(totalSeconds / (3600*24));
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  let duration = '';
  if (days > 0) {
    duration += `${days}天`;
  }
  if (hours > 0) {
    duration += `${hours}小时`;
  }
  if (minutes > 0) {
    duration += `${minutes}分钟`;
  }
  if (seconds > 0) {
    duration += `${seconds}秒`;
  }

  return duration || '0秒';
}

// 表单校验
// 固定编号
function testNumber(data, length) {
  const reg = new RegExp('^\\d{1,' + length + '}$', 'g')
  return reg.test(data)
}

// 校验中文字符
function testCEText(data, length) {
  const reg = new RegExp('^[\\u4e00-\\u9fa5a-zA-Z]{0,}$', 'g')
  return reg.test(data)
}

// 校验英文、数字、符号
function testEntext(data, length) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{1,1}$/
  return reg.test(data)
}
// 校验英文和数字
function testEnNumtext(str) {
  const reg = /^[a-zA-Z0-9]+$/
  return reg.test(str)
}

// 校验手机号
function testPhoneNumber(num) {
  const reg = /^1[3456789]\d{9}$/
  return reg.test(num)
}
// 检验身份证
function testIdCard(num) {
  const reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X|x)$/
  return reg.test(num)
}

// 校验价格（整数或保留两位小数）
function testPrice(price) {
  const priceReg = /(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/
  return priceReg.test(price)
}

// 重新封装　trim
function tTrim(str) {
  str += ''
  return str.replace(/^\s+|\s+$/gm, '')
}

// 过滤表情
function filterEmoji(name) {
  var str = name.replace(/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig, '')
  return str
}

// 表情处理
function utf16toEntities(str) {
  // console.log('utf16toEntities--11111->', str)
  var patt = /[\ud800-\udbff][\udc00-\udfff]/g // 检测utf16字符正则
  str = str.replace(patt, function(char) {
    var H, L, code
    if (char.length === 2) {
      H = char.charCodeAt(0) // 取出高位
      L = char.charCodeAt(1) // 取出低位
      code = (H - 0xD800) * 0x400 + 0x10000 + L - 0xDC00 // 转换算法
      return '&#' + code + ';'
    } else {
      return char
    }
  })
  // console.log('utf16toEntities---22222->', str)
  return str
}

// 字符串转表情
function uncodeUtf16(str) {
  // console.log('uncodeUtf16--11111->', str)
  var reg = /\&#.*?;/g
  if (str) {
    var result = str.replace(reg, function(char) {
      var H, L, code
      if (char.length == 9) {
        code = parseInt(char.match(/[0-9]+/g))
        H = Math.floor((code - 0x10000) / 0x400) + 0xD800
        L = (code - 0x10000) % 0x400 + 0xDC00
        return unescape('%u' + H.toString(16) + '%u' + L.toString(16))
      } else {
        return char
      }
    })
    // console.log('uncodeUtf16--22222->', result)
    return result
  }
}

/**
 *
 * @param s
 * @returns {string}
 */
function stripscript(s) {
  var pattern = new RegExp("[`~!@#$^&*()=|{}':%'\\[\\].<>/?~～！@#￥……&*¥（）——|{}【】‘：”“'。、？]")
  var rs = ''
  for (var i = 0; i < s.length; i++) {
    rs = rs + s.substr(i, 1).replace(pattern, '')
  }
  return rs
}

/**
 * 开始结束日期判断
 * <AUTHOR>
 * @param {String} startDate = [1970-01-01]
 * @param {String} startTime = [12:30]
 * @param {String} endDate = [1970-01-01]
 * @param {String} endTime = [12:30]
 * @return {boolean} true/false
 */
function legalTimeStartEnd(startDate, startTime, endDate, endTime) {
  const data = {
    start: `${startDate} ${startTime}`,
    end: `${endDate} ${endTime}`
  }
  const startTimestamp = new Date(data.start)
  const endTimestamp = new Date(data.end)
  console.log(startTimestamp, endTimestamp)
  console.log(startTimestamp - endTimestamp)
  return !(startTimestamp - endTimestamp >= 0)
}

/**
 * 日期时间是否大于当前日期时间判断
 * <AUTHOR>
 * @param {String} startDate = [1970-01-01]
 * @param {String} startTime = [12:30]
 * @return {boolean} true/falseyearCheck
 */
function legalTime(startDate, startTime) {
  var date = `${startDate} ${startTime}`
  date = new Date(date)
  const now = new Date()
  return !(now - date >= 0)
}

export default {
  richText,
  checkVin,
  yearCheck,
  isLicensePlate,
  timestampToTime,
	timestampToTimeSeconds,
  getDateByminute,
  formatDuration,
  testNumber,
  testCEText,
  testEnNumtext,
  testPhoneNumber,
  testIdCard,
  testPrice,
  tTrim,
  utf16toEntities,
  uncodeUtf16,
  stripscript,
  filterEmoji,
  legalTimeStartEnd,
  legalTime,
  testEntext,
	parseHtml
}
