<template>
	<view class="page-fail">
		<view class="fail-desc">
			<view>很抱歉，登录出现问题了</view>
			<view class="mart-10">请点击按钮重新登录</view>
		</view>
		<view class="fail-btn">
			<van-button type="info" round @click="getUserProfile">立即登录</van-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: null,
			};
		},
		onLoad () {
			
		},
		methods: {
			// 获取用户信息的回调
			getUserProfile (e) {
				getApp().updataUserInfo().then(()=>{
					this.userInfo = getApp().globalData.userInfo;
					uni.switchTab({
						url: '/pages/index/index'
					})
				});
			},
		}
	}
</script>

<style lang="less">
	.page-fail {
		width: 100%;
		background-color: #fff;
	}
	.fail-desc {
		margin-top: 20%;
		text-align: center;
	}
	.fail-btn {
		margin-top: 40rpx;
		text-align: center;
	}
</style>
