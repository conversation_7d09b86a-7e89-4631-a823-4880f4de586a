<view class='dialog-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <van-popup show='{{ show }}' z-index='{{ zIndex }}' overlay='{{ overlay }}' transition='{{ transition }}' custom-class='van-dialog van-dialog--{{ theme }} {{ className }}' custom-style='width: {{ utils.addUnit(width) }};{{ customStyle }}' overlay-style='{{ overlayStyle }}' close-on-click-overlay='{{ closeOnClickOverlay }}' onClose='onClickOverlay' ref='saveChildRef1'>
    <view a:if='{{ title || useTitleSlot  }}' class="{{ utils.bem('dialog__header', { isolated: !(message || useSlot) }) }}">
      <slot a:if='{{ useTitleSlot }}' name='title'>
      </slot>
      <block a:elif='{{ title }}'>
        {{ title }}
      </block>
    </view>
    <slot a:if='{{ useSlot }}'>
    </slot>
    <view a:elif='{{ message }}' class="{{ utils.bem('dialog__message', [theme, messageAlign, { hasTitle: title }]) }}">
      <text class='van-dialog__message-text'>
        {{ message }}      </text>
    </view>    <van-goods-action a:if="{{ theme === 'round-button' }}" custom-class='van-dialog__footer--round-button' ref='saveChildRef2'>
      <van-goods-action-button a:if='{{ showCancelButton }}' size='large' loading='{{ loading.cancel }}' class='van-dialog__button van-hairline--right' custom-class='van-dialog__cancel' custom-style='color: {{ cancelButtonColor }}' onClick='onCancel' ref='saveChildRef3'>
        {{ cancelButtonText }}
      </van-goods-action-button>
      <van-goods-action-button a:if='{{ showConfirmButton }}' size='large' class='van-dialog__button' loading='{{ loading.confirm }}' custom-class='van-dialog__confirm' custom-style='color: {{ confirmButtonColor }}' open-type='{{ confirmButtonOpenType }}' lang='{{ lang }}' business-id='{{ businessId }}' session-from='{{ sessionFrom }}' send-message-title='{{ sendMessageTitle }}' send-message-path='{{ sendMessagePath }}' send-message-img='{{ sendMessageImg }}' show-message-card='{{ showMessageCard }}' app-parameter='{{ appParameter }}' onClick='onConfirm' onGetuserinfo='bindGetUserInfo' onContact='bindContact' onGetphonenumber='bindGetPhoneNumber' onError='bindError' onLaunchapp='bindLaunchApp' onOpensetting='bindOpenSetting' ref='saveChildRef4'>
        {{ confirmButtonText }}
      </van-goods-action-button>
    </van-goods-action>
    <view a:else  class='van-hairline--top van-dialog__footer'>
      <van-button a:if='{{ showCancelButton }}' size='large' loading='{{ loading.cancel }}' class='van-dialog__button van-hairline--right' custom-class='van-dialog__cancel' custom-style='color: {{ cancelButtonColor }}' onClick='onCancel' ref='saveChildRef5'>
        {{ cancelButtonText }}
      </van-button>
      <van-button a:if='{{ showConfirmButton }}' size='large' class='van-dialog__button' loading='{{ loading.confirm }}' custom-class='van-dialog__confirm' custom-style='color: {{ confirmButtonColor }}' open-type='{{ confirmButtonOpenType }}' lang='{{ lang }}' business-id='{{ businessId }}' session-from='{{ sessionFrom }}' send-message-title='{{ sendMessageTitle }}' send-message-path='{{ sendMessagePath }}' send-message-img='{{ sendMessageImg }}' show-message-card='{{ showMessageCard }}' app-parameter='{{ appParameter }}' onClick='onConfirm' onGetuserinfo='bindGetUserInfo' onContact='bindContact' onGetphonenumber='bindGetPhoneNumber' onError='bindError' onLaunchapp='bindLaunchApp' onOpensetting='bindOpenSetting' ref='saveChildRef6'>
        {{ confirmButtonText }}
      </van-button>
    </view>
  </van-popup>
</view>