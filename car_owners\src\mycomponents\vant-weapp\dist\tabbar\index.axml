<view class='tabbar-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{ border ? 'van-hairline--top-bottom' : '' }} {{ utils.bem('tabbar', { fixed, safe: safeAreaInsetBottom }) }} {{customClass}}" style="{{ zIndex ? 'z-index: ' + zIndex : '' }}">
    <slot>
    </slot>
  </view>
  <view a:if='{{ fixed && placeholder }}' style='height: {{ height }}px;'>
  </view>
</view>