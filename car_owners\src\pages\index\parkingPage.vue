<template>
	<view class="">
		<view class="front-page-nav--panel">
			<view class="nav-panel-item" v-for="(item,index) in navList" :key="index" v-show="item.name">
				<template v-if="item.type ==='getphonenumber' && verifyPhone">
					<button style="line-height: 100%;" 
						@click="handleJump($event,item)">
						<image :src="item.icon" class="nav-panel-item__icon"></image>
						<view class="nav-panel-item__name">
							<text v-text="item.name"></text>
						</view>
					</button>
				</template>
				<template v-else>
					<view @click="handleNavItemClick(item)">
						<image :src="item.icon" class="nav-panel-item__icon"></image>
						<view class="nav-panel-item__name">
							<text v-text="item.name"></text>
						</view>
					</view>
				</template>
			</view>
			<!-- #ifdef MP-ALIPAY -->
			<!-- <view  class="nav-panel-item">
				<view @click="toInpark(1)">
					<image src="http://image.bolink.club/FiFHt3GXeJqQ9mWmmg8xcpNgwm3-" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						无牌车
					</view>
				</view>
			</view>
			<view  class="nav-panel-item">
				<view @click="toInpark(2)">
					<image src="http://image.bolink.club/FiFHt3GXeJqQ9mWmmg8xcpNgwm3-" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						预付
					</view>
				</view>
			</view>
			<view  class="nav-panel-item">
				<view @click="toInpark(3)">
					<image src="http://image.bolink.club/FiFHt3GXeJqQ9mWmmg8xcpNgwm3-" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						出场
					</view>
				</view>
			</view> -->
			<!-- #endif -->
		</view>
		<!-- #ifdef MP-ALIPAY -->
		<view class="ali-ad-img"></view>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<swiper v-if="isAllowShow&&hotAreaList.length>0" class="bannerList" indicator-color="#ccc"
			indicator-active-color="#fff" :indicator-dots="true" :autoplay="true" :circular="true" :interval="3000"
			:duration="1000">
			<swiper-item v-for="(item,index) in hotAreaList">
				<image :src="item.pic" mode="heightFix" @click="handleBannerClick(item)"></image>
			</swiper-item>
		</swiper>
		<!-- #endif -->
		<!-- 附近停车 预约停车 -->
		<view class="chargeList">
			<view style="display: flex;justify-content:flex-start;padding: 7rpx;">
			</view>

			<view :class="{'flex-row':true,'scroll-top-fixed':isTop}" :style="{top: navHeight+statusBarHeight+'px'}">
				<view @click="chargeChooseChange(0)" :class="{'title':true,'active':chargeChoose==0}">
					距离最近
					<view class="selected" v-show="chargeChoose===0"></view>
				</view>
				<!-- #ifndef MP-ALIPAY -->
				<view @click="chargeChooseChange(1)" :class="{'title':true,'active':chargeChoose==1}">
					预约停车
					<view class="selected" v-show="chargeChoose==1"></view>
				</view>
				<!--  #endif -->
				<view @click="chargeChooseChange(2)" :class="{'title':true,'active':chargeChoose==2}">
					收藏车场
					<view class="selected" v-show="chargeChoose==2"></view>
				</view>
			</view>

			<view :class="{'parkingContent parkingContent-top':isTop,'parkingContent':!isTop,'parkingContent-min-height':parkListDataLength}">

				<view style="padding-bottom: 24rpx" v-for="(item,index) in parkListData">
					<view class="charge-item" @click="toDetail(item)">
						<view class="top">
							<view class="parkName-container">
								<view class="parkName">{{item.name}}</view>
								<!-- 										<view v-if="item.releaseStatus==1" class="station-await">
											可预约
										</view> -->
							</view>

							<view class="distance">
								<image src="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" mode="aspectFit">
								</image>
								{{item.dis.toFixed(2)}}km
							</view>
						</view>
						<view class="flex-row font-24">
							<view class="grey name">
								{{item.address}}
							</view>
						</view>
						<view class="all">
							<image class="pIcon" src='https://image.bolink.club/yima/nearParkStopCar-lv.png' v-if="(item.totalPlot - item.emptyPlot) <= 10"></image>
							<image class="pIcon" src='https://image.bolink.club/Fm7CVgsXPJZTYPzuXggSDAs3wtI7' v-else-if="item.emptyPlot <= 10"></image>
							<image class="pIcon" src='http://image.bolink.club/Fq8ilVQL2XMJVi74wdObLOrY9LRu' v-else></image>
							<view class="carNum">
								<view class="spaceRemain" v-if="(item.totalPlot - item.emptyPlot) <= 10">余位充裕</view>
								<view class="tenseRemain" v-else-if="item.emptyPlot <= 10">余位紧张</view>
								<view class="emptyPlot" v-else>空{{item.emptyPlot||0}}</view>
								/{{item.totalPlot||0}}
							</view>
						</view>
<!-- 						<view class="middle">
							<view class="all">
								<image class="pIcon" src='http://image.bolink.club/Fup96AiAOQQHADy1fuGpD4UXyEV3'>
								</image>
								<view class="carNum">
									空闲 {{item.emptyPlot||0}}
								</view>
							</view>
									<view class="priceall">
										<view class="text">￥</view>
										<view class="price">{{item.minFee}}</view>
										<view class="text">起</view>
									</view> 
						</view> -->
					</view>
				</view>
				<view class="list-end" v-if="chargeChoose!==2">{{isEndCharge? '暂无更多~' : '加载中...'}}</view>



			</view>

		</view>



		<!-- 活动弹框 -->
		<van-overlay z-index="1000" :show="showActivity">
			<view class="activity-view">
				<view class="flex-col alc marr-30 marl-30 marb-20 mart-20">
					<swiper :indicator-dots="modalAdList && modalAdList.length>1" style="width: 100%;height: 657rpx;"
						autoplay>
						<template v-for="(item,index) in modalAdList">
							<swiper-item v-if="item.jumpType==1" @click="jumpH5Activity(item)" :key="index">
								<image :src="item.pic" style="width: 500rpx;height: 100%;" />
							</swiper-item>
							<swiper-item v-if="item.jumpType==2" @click="jumpMiniProgram(item)" :key="index">
								<image :src="item.pic" style="width: 500rpx;height: 100%;" />
							</swiper-item>
						</template>
					</swiper>
					<view class="mart-20" style="color: #fff">将在{{second}}s后自动关闭</view>
					<view class="mart-30">
						<van-icon name="close" color="#fff" size="60rpx" @click="onCloseActivity" />
					</view>
				</view>
			</view>
		</van-overlay>
		<!-- <view v-if="showOrderTips" class="orderTips">
			<view class="con">
				<view @click="toOrderList" class="orderTipsText">你有未处理的，待支付订单</view>
				<image @click="closeOrderTips" class="orderTipsClose" src="https://image.bolink.club/yima/order_list_index_close_icon.png"></image>
			</view>
		</view> -->
		<van-dialog id="van-dialog"></van-dialog>
		<!-- <van-dialog id="my-dialog"></van-dialog> -->
		<!-- #ifdef MP-ALIPAY -->
		<!-- 出入停车场消息授权插件 -->
		<parking-message onHandle="onHandle" />
		<subscribe-msg></subscribe-msg>
		<!-- #endif -->
	</view>
</template>

<script>
	// #ifdef MP-ALIPAY
	const {
		requestSubscribeMessage
	} = requirePlugin('subscribeMsg');
	const parkingPlugin = requirePlugin("parkingMsg");
	// #endif
	// // #ifdef MP-ALIPAY
	// import Dialog from '../../mycomponents/vant-weapp/dist/dialog/index';
	// // #endif
	// #ifdef MP-WEIXIN
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	// #endif
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	import util from '../../common/utils/util.js'

	let app = getApp();
	export default {
		name:'ParkingPage',
		props: {
			isAllowShow: {
				type: Boolean,
				default: false,
			},
			longitude: {
				type: Number,
				default: uni.getStorageSync('longitude'),
			},
			latitude: {
				type: Number,
				default: uni.getStorageSync('latitude'),
			},
			isEnd: {
				type: Boolean,
				default: false,
			},
			isScrollTop: {
				type: Boolean,
				default: false,
			},
			navHeight: {
				type: Number,
				default: 40,
			},
			statusBarHeight: {
				type: Number,
				default: 47,
			},
			activeName: {
				type: String,
			},
			mobile:{
				type:String
			}
		},
		data() {
			return {
				childName:'parking',//首页子组件标识，用于下一页调用跳转方法
				parkListData: [],
				pageSize: 10,
				dis: 5000,
				pageCharge: 1,
				chargeChoose: 0,
				isEndCharge: false,
				showOrderTips: false, //展示待支付订单提示
				form: form,
				util: util,
				carList: [], // 车牌信息
				swiperIndex: 0,
				hotAreaList: [],
				modalAdList: null,
				second: 5,
				showActivity: false, // 弹窗广告
				userInfo: app.globalData.userInfo, // 用户信息
				navList: [
					{
						name: '附近车场',
						icon: 'https://image.bolink.club/front_page_nearby%402x.png',
						target: 'navigateTo',
						path: '/pagesC/park/nearpark',
						meta: {
							keyWord: '发现页-点击附近车场',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '附近的停车场',
						}
					},
					{
						name: '月卡续费',
						icon: 'https://image.bolink.club/front_page_monthly_card.png',
						target: 'navigateTo',
						path: '/pagesD/car/carSubscribe',
						type: 'getphonenumber',
						checkUserInfo: true,
						meta: {
							keyWord: '发现页-点击附近车场',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '附近的停车场',
						}
					},
					{
						name: '优惠加油',
						icon: 'https://image.bolink.club/front_page_discount%402x.png',
						target: 'url', // 跳转方式 url 第三方h5页面
						path: 'https://life-api.cdd.shengxintech.com/gasoline/home?channel_id=18546',
						type: 'getphonenumber', // 容器类型 - 需要获取手机号
						flag: 'promotion', // 功能标记，一般和type配合使用
						meta: {
							keyWord: '发现页-点击进入优惠加油',
							clickType: 'Button',
							jumpType: 'H5',
							jumpDesc: '第三方优惠加油',
						}
					},

					{
						name: '今日油价',
						icon: 'https://image.bolink.club/front_page_now_oil_price%402x.png',
						target: 'navigateTo',
						path: '/pagesB/integral/oilPrice',
						meta: {
							keyWord: '发现页-点击今日油价',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '今日油价页面',
						}
					},
				],
				parkListIsEnd: false, // 车场列表是否全部查询
				showOrder: false, // 是否展示订单
				orderType: 1,
				orderList: [], // 订单列表
				parkList: [], // 预约车场列表
				getLocationFail: 0, // 定位失败车数
				pageNum: 1,
				orderPageNum: 1, // 订单当前查询页
				orderPageSize: 5, // 订单每页查询条数 
				isOrderEnd: false, // 是否已加载所有订单
				showData: false, // 是否展示数据
				isRefresher: false,

			}
		},
		computed: {
			verifyPhone() {
				let boo = true;
				try {
					let mobile = this.mobile;
					if (mobile && String(mobile).length === 11) {
						boo = false;
					} else {
						boo = true;
					}
				} catch (e) {
					boo = true;
				}
				return boo;
			},
			isTop() {
				return this.isScrollTop ? true : false
			},
			parkListDataLength(){
				let {parkListData}=this				
				let res=this.isScrollTop||parkListData.length>=4?true:false				
				return res
			}
			
		},
		filters: {
			// 保留两位数字
			toFixed2(val) {
				return val ? Number(val).toFixed(2) : '0.00'
			},
			// 格式化时间
			formetTime(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd hh:mm:ss') : ''
			},
			formetDate(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd c') : ''
			},
		},
		methods: {
			// 预约停车
			getParkList() {
				const that = this;
				let params = {
					lat: this.latitude || uni.getStorageSync('latitude'),
					lon: this.longitude || uni.getStorageInfoSync('longitude'),
					pageNum: this.pageCharge,
					pageSize: 10,
				};
				apis.park.queryParkList(params).then((res) => {
					this.showData = true;
					if (res.status === 200) {
						let data = res.data || [];
						this.showSwiper = true;
						if (!data || data.length < 10) {
							this.isEndCharge = true
						} else {
							this.isEndCharge = false
						}
						if (data.length > 0) {
							if (this.pageCharge === 1) {
								this.parkListData = data;
							} else {
								this.parkListData = [...this.parkListData, ...data];
							}

						}
					}
				}).catch(() => {
					setTimeout(() => {
						that.isRefresher = false;
					}, 1000)
					uni.hideLoading()
					uni.stopPullDownRefresh();
					that.showData = true;
					uni.showToast({
						title: '网络超时，请稍后重试!',
						icon: 'none',
					});
				})
			},
			toNavigate(item) {
				util.reNavigateTo(
					`/pagesC/park/nearparkDetail?item=${JSON.stringify(item)}&location=${JSON.stringify({longitude:this.longitude,latitude:this.latitude})}`
				);
			},

			// 预约
			toAppointment(info) {
				util.reNavigateTo(`/pagesC/appointment/appointment?info=${JSON.stringify(info)}`);
			},
			toDetail(item) {
				if(this.chargeChoose === 0){
					uni.navigateTo({
						url: '/pagesC/park/nearparkDetail?item=' + JSON.stringify(item) + '&location=' + JSON
							.stringify({
								longitude: this.longitude,
								latitude: this.latitude
							})
					})
					
				}else if(this.chargeChoose === 1){
					this.toAppointment(item)
				}

			},
			// 通过经纬度获取停车场信息
			getnearpark(name = '') {
				let that = this
				let params = {
					lat: this._props.latitude,
					lon: this._props.longitude,
					dis: this.dis,
					pageNum: this.pageCharge,
					pageSize: this.pageSize
				};
				apis.homeApis.getnearpark(params).then((res) => {
					if (res.status === 200) {
						let data = res.data || [];

						if (!data || data.length < 10) {
							this.isEndCharge = true
						} else {
							this.isEndCharge = false
						}
						if (data.length > 0) {
							if (params.pageNum === 1) {
								that.parkListData = data
							} else {
								that.parkListData = [...that.parkListData, ...data]
							}

						} else {

							uni.showToast({
								title: '附近未找到车场信息',
								icon: 'none'
							})
						}
					} else {
						that.pageCharge = that.pageCharge > 1 ? that.pageCharge - 1 : 1

					}
				}).catch((err) => {
					 console.log(err);
				})
			},
			//滚动底部触发
			scrolltolower() {
				this.pageCharge += 1
				if (this.isEndCharge) {
					this.pageCharge = 1
					return false
				}
				if (this.chargeChoose === 0) {
					this.getnearpark()

				} else if (this.chargeChoose === 1) {
					this.getParkList()
				} else {

				}

			},
			chargeChooseChange(e) {
				this.chargeChoose = e
				this.isEndCharge = false
				this.pageCharge = 0
				this.parkListData = []
				this.scrolltolower()


				// this.$emit('changeSupportAppointment',e)
			},
			toOrderList() {
				uni.navigateTo({
					url: '/pagesC/pay/carOrderList'
				})
			},
			//关闭待支付订单提示
			closeOrderTips() {
				this.showOrderTips = false
			},
			//获取待支付订单
			getOrderList() {
				this.showOrderTips = true
			},
			// 用户进行开启或者关闭出入停车场消息操作时
			onHandle(event) {
				const {
					action,
					result,
					carNumber
				} = event;
				// action指操作状态，open开启，closed关闭
				// result指操作的结果，success成功，fail失败
				// carNumber指的是车牌号
			},
			getElementIcon() {
				apis.homeApis.getElementIcon(['0']).then(res => {
					if (res.data.length > 0) {
						let navList = res.data[0].icons
						let length = res.data[0].icons.length
						for (let i = length; i < 5; i++) {
							navList.push({
								name: '',
								icon: '',
								target: ''
							}, )
						}
						this.navList = navList
						// 支付宝不显示车险报价
						// #ifdef MP-ALIPAY
						this.navList=this.navList.filter((item)=>{
							return item.name!=='车险报价'
							
						}
						)
						
						// #endif
						apis.homeApis.getcarinfo().then((res) => {
							let carList = res.data;
							if (!carList || carList.length === 0) {
								return;
							}
							this.navList.forEach(item => {
								if (item.name == '月卡续费') {
									item.path =
										`/pagesD/car/carSubscribe?item=${JSON.stringify(carList[0])}`
								}

							})
							
						});

					}
				})
			},
			// 热门区域点击 跳转
			handleBannerClick(item) {
				// #ifdef MP-ALIPAY
				this.hintModel();
				return false
				// #endif
				uni.request({
					url: item.clickUrl
				})
				if (item.url && item.jumpType === 1) {
					getApp().jumpAd({
						url: item.url,
						keyWord: '发现页-热门区域'
					});
				} else if (item.url && item.jumpType === 2) {
					uni.navigateToMiniProgram({
						appId: item.wxId,
						path: item.url || '',
						complete: (res) => {}
					});
				} else if (item.url && item.jumpType === 3) {
					let startTime = new Date().getTime();
					uni.navigateTo({
						url: item.url,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: '首页-页内跳转',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '添加车辆页面',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				} else {
					this.hintModel();
				}
			},
			// 获取热门区域的信息
			getHotArea() {
				let data = {
					areaCode: uni.getStorageSync('areaCode') || '',
					// #ifdef MP-WEIXIN
					adPosId: 19,
					// #endif
					// #ifdef MP-ALIPAY
					adPosId: 24,
					// #endif
					carType: 1
				}
				apis.homeApis.getad(data).then(res => {
					this.hotAreaList = res.data;
				}).catch(err => {

				})
				let data1 = {
					areaCode: uni.getStorageSync('areaCode') || '',
					// #ifdef MP-WEIXIN
					adPosId: 20,
					// #endif
					// #ifdef MP-ALIPAY
					adPosId: 25,
					// #endif
					carType: 1
				}
				apis.homeApis.getad(data1).then(res => {
					this.modalAdList = res.data;
					if (this.modalAdList.length > 0) {
						this.showActivity = uni.getStorageSync('showActivity');
						let timer = setInterval(() => {
							this.second -= 1;
							if (this.second == 0) {
								clearInterval(timer);
								this.showActivity = false;
								uni.setStorageSync('showActivity', false);
							}
						}, 1000)
					}
				}).catch(err => {

				})
			},
			onCloseActivity() {
				uni.setStorageSync('showActivity', false);
				this.showActivity = false;
			},
			// 广告H5跳转
			jumpH5Activity(item) {
				uni.setStorageSync('showActivity', false);
				getApp().jumpH5Activity(item, '停车缴费页-广告轮播图');
			},
			// 广告小程序跳转
			jumpMiniProgram(item) {
				uni.setStorageSync('showActivity', false);
				if (item.wxId == 'wxfc2f68263c3dea9d') {
					// let startTime = new Date().getTime();
					uni.navigateTo({
						url: item.url,
						complete: (res) => {
							getApp().adClickRecord(item.clickUrl);
							getApp().collectADClick({
								keyWord: '停车缴费页-弹窗广告',
								clickType: '图片',
								jumpType: '小程序',
								jumpDesc: '平安活动页',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime(),
								state: res.errMsg == 'navigateTo:ok' ? 1 : 0,
								adInfoId: item.id
							});
						}
					})
				} else {
					getApp().jumpMiniProgram(item, '停车缴费页-广告轮播图');
				}
			},
			getMobile() {
				let boo = true;
				try {
					let mobile = uni.getStorageSync('mobile');
					if (mobile && String(mobile).length === 11) {
						boo = false;
					} else {
						boo = true;
					}
				} catch (e) {
					boo = true;
				}

				return boo;
			},
			// 获取用户信息的回调
			getUserProfile(e) {
				app.updataUserInfo().then(() => {
					this.userInfo = app.globalData.userInfo;
					this.existorder();
				});
			},
			// 停车信息
			existorder(type) {
				apis.homeApis.existorder().then((res) => {
					if (res.data && res.data.length > 0 && this.mobile) {
						this.parkInfo = res.data;
						this.showPark = true;
					} else {
						this.showPark = false;
					}
				});
				if (type) {
					this.activityClick_c(type);
				}
			},
			// 获取手机号
			getPhoneNumber(e, name) {
				// #ifdef MP-ALIPAY
				my.getPhoneNumber({
					success: (req) => {
						app.getPhoneNumber(e, this, req).then(res => {
							if (res) {
								this.mobile = res.mobile
								if (name === 'clickNum') {
									if (this.showMyLicense) {
										this.bindCarNumberTips();
									}
								} else if (name == 'payList') {
									this.existorder(3);
								} else if (name = 'stopPay') {
									this.costQuery()
								}
							}
						}).catch(err => {
							 console.log("获取失败", err)
						})
					},
					fail: (err) => {
						 console.log('授权失败', err)
					}
				})
				// #endif
				// #ifdef MP-WEIXIN
				app.getPhoneNumber(e, this).then(res => {
					if (res) {
						this.mobile = res.mobile
						if (name === 'clickNum') {
							if (this.showMyLicense) {
								this.bindCarNumberTips();
							}
						} else if (name == 'payList') {
							this.existorder(3);
						} else if (name = 'stopPay') {
							this.costQuery()
						}
					}
				})
				// #endif

			},
			// 处理获取手机号后的跳转 
			async handleJump(evt, row) {
				let res=await util.getMobile()
				if(res.flag===0){
					row=JSON.stringify(row)
					uni.setStorageSync("jumpRow",row)
					uni.navigateTo({
						url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=1&activeName=control`,
					})
				}else{
					this.handleNavItemClick(row);
				}
			},
			/**
			 * @description 优惠加油获取路径
			 * @param {Object} row
			 */
			getOilAdPath(row) {
				apis.homeApis.getOilAdPath({
					mobile: uni.getStorageSync('mobile'),
					openid: uni.getStorageSync('openId')
				}).then(res => {
					if (res.status === 200 && res.data) {
						let data = res.data;
						getApp().jumpAd({
							url: data.redirectUrl,
							keyWord: row.meta.keyWord
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 3000
					});
				})
			},
			/**
			 * @desc model提示
			 * @param { String } text
			 */
			hintModel(text) {
				uni.showModal({
					title: '',
					showCancel: false,
					confirmText: '好的',
					content: text || '暂无此功能，敬请期待',
					success: function(res) {

					}
				});
			},
			// 点击导航栏子元素 
			handleNavItemClick(row) {
				// #ifdef MP-ALIPAY
				if (row.name == '违章查询' || row.name == '优惠加油') {
					uni.showToast({
						title: '功能暂未开通，敬请期待',
						icon: 'none',
						duration: 3000
					});
					return false
				}
				// #endif

				let startTime = new Date().getTime();
				if (row.flag === 'promotion') {
					return this.getOilAdPath(row);
				}
				if (row.icon === '') {
					return false
				}
				if (row.path === undefined || row.path === '') {
					return this.hintModel()
				}

				// 重新组装 path 数据
				let jumpPath = row.path;
				if (row.name == "附近车场") {
					jumpPath += "?type=auto"
				}
				// 如果标识是"ladderControl"，并且有梯控的项目id,说明不是第一次使用
				if (row.flag === 'ladderControl') {
					let projectId = uni.getStorageSync('bl_community_ladder_id') || '';
					let projectName = uni.getStorageSync('bl_community_name') || '';
					if (projectId === '') {
						uni.navigateTo({
							url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
							complete: (res) => {

							}
						})
						return false;
					}

					jumpPath += `?id=${projectId}&name=${projectName}`;
				}

				// 如果标识是"openDoor",并且有项目id,说明不是第一次使用
				if (row.flag === 'openDoor' || row.flag === 'multiply') {
					let bl_community_id = uni.getStorageSync('bl_community_id') || '';
					if (bl_community_id === '') {
						uni.navigateTo({
							url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
							complete: (res) => {

							}
						})
						return false;
					}
				}

				// 页内跳转
				if (row.target === 'tab') {
					uni.switchTab({
						url: jumpPath
					});
				} else if (row.target === 'url') {
					if (row.path === 'placement_information') {
						if (Math.round(new Date()) > BaseConfig.releaseTime) {
							getApp().jumpWeiBao('weibao-车险服务导航');
						} else {
							uni.showToast({
								title: '功能暂未开通，敬请期待',
								icon: 'none',
								duration: 3000
							});
						}
						return false;
					}
					getApp().jumpAd({
						url: jumpPath,
						keyWord: row.meta.keyWord
					});
				} else if (row.target === 'mini_program') {
					let meta = row.meta;
					uni.navigateToMiniProgram({
						appId: meta.appId,
						path: jumpPath,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: meta.keyWord,
								clickType: 'Button',
								jumpType: meta.jumpType,
								jumpDesc: meta.jumpDesc,
								result: res.errMsg == 'navigateToMiniProgram:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				} else {
					let meta = row.meta;
					uni.navigateTo({
						url: jumpPath,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: meta.keyWord,
								clickType: meta.clickType,
								jumpType: meta.jumpType,
								jumpDesc: meta.jumpDesc,
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						},
						fail: (err) => {


						}
					})
				}
			},
			// 绑定车牌提醒
			bindCarNumberTips(text, callback) {
				let message = text || '绑定车牌，查询停车费更便捷哦！'
				// #ifdef MP-ALIPAY
				uni.showModal({
					title: '提示',
					content: message,
					confirmText: '去绑定',
					success: function(res) {
						if (res.confirm) {
							let startTime = new Date().getTime();
							let url = `/pagesD/car/carSubscribe?fromTo=park`;
							uni.navigateTo({
								url: url,
								complete: (res) => {
									app.eventRecord({
										keyWord: '我的-顶部-车辆滚动卡片',
										clickType: 'Button',
										jumpType: '本程序页面',
										jumpDesc: '编辑车辆信息页',
										result: res.errMsg == 'navigateTo:ok' ? '成功' :
											'失败',
										startTime: startTime,
										endTime: new Date().getTime()
									});
								}
							})
						} else if (res.cancel) {
							if (callback) callback();
						}
					}
				});
				// #endif
				// #ifdef MP-WEIXIN
				Dialog.confirm({
						title: '提示',
						message: message,
						cancelButtonText: '取消',
						confirmButtonText: '去绑定',
						confirmButtonColor: '#1677FF',
					})
					.then(() => {
						let startTime = new Date().getTime();
						let url = `/pagesD/car/carSubscribe?fromTo=park`;
						uni.navigateTo({
							url: url,
							complete: (res) => {
								app.eventRecord({
									keyWord: '我的-顶部-车辆滚动卡片',
									clickType: 'Button',
									jumpType: '本程序页面',
									jumpDesc: '编辑车辆信息页',
									result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
									startTime: startTime,
									endTime: new Date().getTime()
								});
							}
						})
					})
					.catch(() => {
						if (callback) callback();
					});
				// #endif
			},
			toInpark(type){
				if(type==1){
					util.dealQrcode('https://beta.bolink.club/s?id=124297')
				}else if(type == 2){
					util.dealQrcode('https://beta.bolink.club/s?id=124623')
				}else if(type == 3){
					// util.dealQrcode('https://beta.bolink.club/s?id=124622')
					util.dealQrcode('https://beta.bolink.club/s?id=124130')
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.orderTips {
		position: fixed;
		left: 50%;
		bottom: 40rpx;

		.con {
			display: flex;
			align-items: center;
			width: 436rpx;
			height: 80rpx;
			padding: 0 40rpx;
			margin-left: -218rpx;
			background: #222E3F;
			border-radius: 40rpx;

			.orderTipsText {
				margin-right: 20rpx;
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
			}

			.orderTipsClose {
				width: 24rpx;
				height: 24rpx;
			}
		}
	}

	// #ifdef MP-ALIPAY
	.zfbWidth {
		width: 50rpx;
	}

	.zfbWidth55 {
		width: 55rpx;
	}

	// #endif
	.bannerList {
		margin: 0 26rpx 20rpx 26rpx;
		height: 204rpx;

		swiper-item {
			border-radius: 30rpx;
		}

		image {
			width: 100%;
			height: 100%;
		}
	}
	
	

	.park-popup {
		height: 320rpx;
		margin: 4rpx;
		font-size: 26rpx;
		color: #fff;
		background-color: #308EFC;
		border-radius: 20rpx;
	}

	.park-popup-bg0 {
		background-image: url('https://image.bolink.club/yima/parking-bg1.jpg');
		background-size: 100% 100%;
	}

	.park-popup-bg1 {
		background-image: url('https://image.bolink.club/yima/parking-bg2.jpg');
		background-size: 100% 100%;
	}

	.park-popup-bg2 {
		background-image: url('https://image.bolink.club/yima/parking-bg3.jpg');
		background-size: 100% 100%;
	}

	.park-popup-bg3 {
		background-image: url('https://image.bolink.club/yima/parking-bg4.jpg');
		background-size: 100% 100%;
	}

	.activity-view {
		width: 100%;
		text-align: center;
		position: relative;
		top: 25%;
	}



	.chargeList {
		//height: auto;
		height: 100%;

		.title {
			margin: 0 0 24rpx 40rpx;
			height: 56rpx;
			font-size: 32rpx;
			font-weight: 600;
			color: #B0B6C1;
			padding-top: 8rpx;
			line-height: 36rpx;
		}

		.selected {
			margin: 12rpx auto 0 auto;
			width: 88rpx;
			border-bottom: 4rpx solid #0da062;

		}

		.active {
			color: #242E3E;
			font-size: 36rpx;
		}

		.parkingContent {
			width: calc(100vw - 52rpx);
			height: 100%;
			//height: calc(100vh - 720rpx);
			margin: 0 26rpx 20rpx 26rpx;
			overflow-y: auto;
			padding-bottom: 130rpx;

			// background-color: #FCF9FC;
			.charge-item {
				border-radius: 30rpx;
				padding: 24rpx 24rpx;
				box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);
				background-color: #fff;

			}



			.top {
				display: flex;
				justify-content: space-between;
				padding: 0 0 10rpx;
				align-items: center;

				image {
					width: 34rpx;
					height: 34rpx;
					margin-right: 20rpx;
					transform: translateY(7rpx);
				}

				.parkName-container {
					display: flex;
					align-items: center;

					.station-await {
						margin-left: 20rpx;
						padding: 0rpx 10rpx;
						font-size: 18rpx;
						color: #468cfe;
						border-radius: 20rpx 0 20rpx 0;
						width: 80rpx;
						height: 32rpx;
						display: flex;
						justify-content: center;
						border: 1rpx solid #468cfe;
						align-items: center;
					}
				}

				.parkName {
					font-size: 32rpx;
					font-weight: bold;
					color: #242E3E;
					width:55vw;
					overflow:hidden; //超出的文本隐藏
					text-overflow:ellipsis; //溢出用省略号显示
					white-space:nowrap; //溢出不换行
		
				}

				.distance {
					font-size: 28rpx;
					// font-weight: bold;
					color: #2c2c2c;
				}
			}

			.middle {
				display: flex;
				justify-content: space-between;

				.priceall {
					position: relative;
					display: flex;

					.price {
						margin: 0;
						padding: 0;
						font-size: 50rpx;
						font-weight: bold;
						color: #EF635F;
					}

					.text {
						padding-top: 20rpx;
					}
				}

			}

			.all {
				display: flex;
				align-items: center;
				width: 290rpx;
				text-align: center;
				font-size: 24rpx;
				height: 54rpx;
				line-height: 54rpx;

				.pIcon {
					width: 40rpx;
					height: 40rpx;
				}

				.carNum {
					display: flex;
					margin-left: 16rpx;
					color:#c0c5d2;
					.emptyPlot{
						color:#468cfe;
					}
					.spaceRemain{
						color: #1BCA7F;
						font-weight: bold;
					}
					.tenseRemain{
						color: #9307E6;
						font-weight: bold;
					}
				}




			}

			.grey {
				color: #c0c5d2;
			}


			.bottom {
				background: #F2F5FC;
				border-radius: 4rpx;
				opacity: 0.39;
				padding-left: 5rpx;

				.address {
					padding: 8rpx;
					font-size: 26rpx;
					font-weight: 400;
					color: #828EA6;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
				}
			}
		}

		.parkingContent-top {
			min-height:100vh;
		}
		.parkingContent-min-height{
			min-height: 2000rpx;
		}
	}

	.scroll-top-fixed {
		width: 100%;
		position: fixed;
		top: 165rpx;
		background-color: #fafafa;
		z-index: 99;
		height: 90rpx;
		line-height: 90rpx;
		padding-top: 10rpx;
	}
</style>