<template>
	<view class="agreement-container">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<custom-nav-bar :title="step===1?'手机号':'更换手机号'"></custom-nav-bar>
		<!-- #endif -->
		<view class="agreement">
			<template v-if="step===1">
				<view class="text-content">
					<view class="text" style="text-align: center;">
						当前手机号
					</view>
					<view class="title">
						{{encryptionMobile}}
					</view>
				</view>
			<view>
				<view class="login-footer">
					<view class="bl-button bl-button--primary" @click="next">
						{{step===1?'更换手机号':step===2?'下一步':'提交'}}
					</view>
				</view>
			</view>
			</template>
			<template v-else-if="step===2">
				<view class="text-content">
					<view class="text">
						请输入新的手机号
					</view>
				</view>
				<view class="phone-code-container">
					<view class="phone">
						<van-cell-group>
							<van-field :value="phone" required clearable label="" placeholder="请输入手机号"
								placeholder-style="font-size: 26rpx;color:#9ba6c6;" maxlength="11" @change="phoneChange"
								class="phone" />
						</van-cell-group>
					</view>
				</view>
				<view>
					<view class="login-footer">
						<view class="bl-button bl-button--primary" @click="next">
							{{step===1?'更换手机号':step===2?'下一步':'提交'}}
						</view>
					</view>
				</view>
			</template>
			<template v-else-if="step===3||step===4||step===5">
				<view class="text-content">
					<view class="text">
						输入验证码
					</view>
					<view class="text grey" style="margin-top:50rpx">
						{{tips}}
					</view>
				</view>
				<view class="phone-code-container">
					<view class="phone code">
						<van-cell-group>
							<van-field :value="code" required clearable label="" placeholder="验证码" maxlength="4"
								placeholder-style="font-size: 26rpx;color:#9ba6c6;" @change="codeChange"
								class="phone code" />
						</van-cell-group>
					</view>
				</view>
				<view :class="codeContent==='获取验证码'||codeContent==='重新获取'?'text-code blue':'text-code grey'"
					@click="getCode">
					{{ codeContent }}
				</view>
				<template v-if="codeContent!=='获取验证码'">
					<view>
						<view class="login-footer">
							<view class="bl-button bl-button--primary" @click="next">
								{{step===1?'更换手机号':step===2?'下一步':'提交'}}
							</view>
						</view>
					</view>
				</template>
			</template>
			<template v-else-if="step===6">
				<view class="text-content">
					<view class="text">
						更换成功
					</view>
					<view class="text grey" style="margin-top:50rpx">
						{{tips}}
					</view>
				</view>
			</template>
		</view>
		<van-popup :show="showPopup" @close="showPopup=false" round :close-on-click-overlay="false">
			<view class="popup-container">
				<view class="tip">该手机号与当前绑定手机号相同</view>
				<view class="footer">
					<view class="cancel btn" @click="showPopup=false">取消</view>
					<view class="submit btn" @click="showPopup=false">确定</view>
				</view>
			</view>
		</van-popup>
	</view>
</template>

<script>
	import apis from "../../common/apis/index"
	import util from '../../common/utils/util'
	import BaseConfig from '../../common/config/index.config'
	let app = getApp();
	export default {
		data() {
			return {
				step: 1,
				codeContent: '获取验证码',
				encryptionMobile: '',
				phone: '',
				code: '',
				tips: '',
				showPopup: false,
				timer:null,//验证码定时器
			}
		},
		computed: {
			isShowButton() {
				let {
					step
				} = this
				let boo = true
				if (step === 3 || step === 6) {
					boo = false
				} else {
					boo = true
				}
				// #ifdef MP-ALIPAY
				if(step===1){
					my.setNavigationBar({
					  title: '手机号'
					})
				}else{
					my.setNavigationBar({
					  title: '更换手机号'
					})
				}
				// #endif
				return boo
			}
		},
		async onLoad(options) {
			this.encryptionMobile = options.encryptionMobile

		},
		onUnload() {
			clearTimeout(this.timer)
		
		},
		methods: {
			// 加密手机号
			encryption(value) {
				if (!value) return
				var reg = /^(\d{3})\d{4}(\d{4})$/
				return value.replace(reg, "$1****$2")
			},
			submit() {
				this.tips = `手机号${this.phone}`
				this.step += 1
				this.showPopup = false
			},
			next() {
				if (this.step === 2) {
					if (!this.phone) {
						uni.showToast({
							title: '请输入手机号',
							icon: 'none'
						})
						return
					}
					let pattern = /^1[3456789]\d{9}$/;
					if (!pattern.test(this.phone)) {
						uni.showToast({
							title: '手机号不正确',
							icon: 'none',
							duration: 3000
						});
						return
					}
					let mobile = uni.getStorageSync('mobile')
					if (this.phone == mobile) {
						this.showPopup = true
						return
					}
					this.tips = `手机号${this.phone}`
				} else if (this.step == 4 || this.step == 5) {
					this.updateMobile()
					return
				}

				this.step += 1

			},
			codeChange(e) {
				this.code = e.detail
			},
			phoneChange(e) {
				this.phone = e.detail
			},
			countdownFn(n) {
				if (n <= 0) {
					this.codeContent = '重新获取'
					this.step = 5
				} else {
					this.timer = setTimeout(() => {
						n--
						this.codeContent = `${n}秒后重新获取`
						this.countdownFn(n)
					}, 1000)
				}
			},
			/* 获取验证码 */
			getCode() {
				let data = {
					mobile: this.phone
				}
				this.countdownFn(60)
				apis.homeApis.verifyCode(data).then(res => {
					if (res.status == 200) {
						this.tips = `短信已发送至${this.phone}`
						this.step =4
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}).catch((err) => {

				})

			},
			updateMobile() {
				let updateMobileData = {
					openId: uni.getStorageSync('openId'),
					mobile: this.phone,
					type: 'custom',
					verifyCode: this.code
				}
				return new Promise((resove, reject) => {
					apis.homeApis.updateMobile(updateMobileData).then(res => {
						if (res.status == 200) {
							uni.setStorageSync('mobile', this.phone)
							this.tips = `手机号${this.phone}`
							this.step =6
							clearTimeout(this.timer)
							return resove({
								flag: 1
							})
						} else if (res.status == 223) {
							let updateData = {
								openId: uni.getStorageSync('openId'),
								mobile: this.mobile,
								isUpdate: 1,
								type: 'custom',
								verifyCode: this.code
							}
							this.updateMobile(updateData)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 3000
							});
							return resove({
								flag: 0
							})
						}

					}).catch((err) => {
						return resove({
							flag: 0
						})
					})
				})


			},

		},
	}
</script>

<style lang="less" scoped>
	page {}

	.agreement-container {
		width: 100%;
		//#ifdef MP-WEIXIN || APP-PLUS
		padding: 220rpx 40rpx 0 40rpx;
		// #endif
		// #ifdef MP-ALIPAY
		padding: 80rpx 40rpx 0 40rpx;
		// #endif
		background-color: #f1f2f6;

		.text-content {
			font-size: 28rpx;
			color: #242e3e;

			.title {
				text-align: center;
				font-weight: bold;
				margin: 40rpx 0;
			}

			.text {
				text-align: left;
			}
		}

		.login-footer {
			position: fixed;
			bottom: 550rpx;
			font-weight: bold;
			width: 100%;
			left: 0;
			padding: 0 40rpx;
			font-size: 28rpx;

			.bl-button--primary {
				width: 100%;
				margin: 0 auto;
				background: #ffffff;
				text-align: center !important;
				height: 90rpx;
				color: #242e3e;
				line-height: 90rpx;
				border-radius: 20rpx;
				font-size: 28rpx;
			}

		}

	}

	.text-wx-login {
		text-indent: 1.3em;
	}

	.text-code {
		font-size: 28rpx;
		text-align: center;
		margin-top: 80rpx;
	}

	.blue {
		color: #23a6fd;
	}

	.phone-code-container {
		margin-top: 50rpx;

		.phone {
			margin: 20rpx 0;
			position: relative;
		}
	}

	::v-deep .van-cell {
		background-color: #ffffff;
		height: 90rpx;
		padding: 10rpx 16rpx 10rpx 10rpx;
		line-height: 90rpx;
		border-radius: 20rpx;
	}

	::v-deep .van-cell--required:before {
		display: none;
	}
	::v-deep .van-field__input{
		height:100% !important;
	}
	::v-deep .van-field__body{
		height:100% !important;
	}

	::v-deep .van-cell__title {
		display: none;
	}

	.grey {
		color: #8c8c8c;
	}

	.encryption-mobile {
		margin-top: 50rpx;
		font-size: 40rpx;
		text-align: center;
		font-weight: bold;
	}

	.popup-container {
		padding: 20rpx;
		width: 90vw;
		height: 270rpx;
		text-align: center;

		.tip {
			font-size: 28rpx;
			color: #242e3e;
			margin-top: 50rpx;
		}

		.footer {
			height: 120rpx;
			line-height: 120rpx;
			display: flex;
			justify-content: space-between;
			padding: 45rpx 40rpx 20rpx 40rpx;
			margin-bottom: 20rpx;

			.btn {
				width: 200rpx;
				height: 86rpx;
				line-height: 86rpx;
				font-size: 28rpx;
				border-radius: 50rpx;
			}

			.cancel {
				color: #242e3e;
			}

			.submit {
				color: #23a6fd;
			}
		}
	}
	// #ifdef MP-ALIPAY
	::v-deep .phone .icon-index {
		transform: translateX(220rpx);
	}
	::v-deep .code .icon-index {
		margin-top:6rpx;
	}
	// #endif
</style>