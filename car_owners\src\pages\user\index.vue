<template>
	<view class="user">
		<!-- #ifdef MP-WEIXIN  -->
		<!-- <view class="custom-back-nav" :style="{ 'line-height': navHeight + 'px' }">
		</view> -->
		<!-- #endif -->

		<view class="userTop">
			<view class="background" :style="{height: statusBarHeight + 490 + 'rpx'}"></view>
			<view class="container" :style="{top: statusBarHeight + 40 + 'px'}">
			<template v-if="verifyPhone">
				<view class="login-container">
					<view class="login-btn" @click="getPhoneAgreement">登录</view>
				</view>
			</template>
			<template v-else>
			<view class="user-info">
				<view class="user-information">
					<!-- #ifndef APP-PLUS -->
					<button class="avatar" @click="uniGetUserProfile" v-if="mobile">
						<image :src="avatarUrl"></image>
					</button>
					<button class="avatar"  @click="getPhoneAgreement" v-else>
						<image :src="avatarUrl"></image>
					</button>
					<view class="user-name-container">
						<button class="user-name" @click="uniGetUserProfile" v-if="mobile">
							<view class="user-name">{{ nickname }}</view>
						</button>
						<button class="user-name"  @click="getPhoneAgreement" v-else>
							<view class="user-name">{{ nickname }}</view>
						</button>
						<view class="setup-container" @click="JumpSettings('/pagesA/agreement/agreement',2)">
							<image src="http://image.bolink.club/Fj5wDAEXePSPPhThbSL6mNv-3_Wi" class="user-setup"></image>
							账户设置
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="avatar">
						<image src="https://image.bolink.club/FvZ_bonlHZROUZWAoePIpf5Rcuex"></image>
					</view>
					<view class="marl-30 mart-20">
						<view v-if="encryptionMobile" class="font-46 fw-700 white">
							<view class="">
								{{ encryptionMobile }}
							</view>
							<view class="font-24" @click="JumpSettings('/pagesA/agreement/agreement',2)">
								账户设置
							</view>
						</view>
						<view v-else @click="toLogin" class="font-46 fw-700 white">用户昵称</view>
					</view>
					<!-- #endif -->
				</view>
				<!-- #ifdef MP-WEIXIN -->
				<view class="news-container" v-if="!appletConfiguration.hiddenNotice" @click="JumpSettings('/pagesD/car/noticeSubscribe',2)">
					<image src="http://image.bolink.club/FlfjSFdJp7G_RocdzVk4MSnRYmvP" class="user-news"></image>
					消息设置
				</view>
				<!-- #endif -->
			</view>
			<view class="user-menu">
				<view class="menu-content" @click="JumpSettings('/pagesF/order/mywallet',2)">
					<view style="font-size:40rpx;font-weight: bold;">{{ amountTotal || 0 }}</view>
					<view>钱包</view>
				</view>
				<view class="menu-content" v-if="!appletConfiguration.hiddenCoupon&&verifyPhone" @click="JumpSettings('/pagesE/user/mycoupon',2)" >
					<view style="font-size:40rpx;font-weight: bold;">{{ cardTotal || 0 }}</view>
					<view>卡券</view>
				</view>
				<view class="menu-content" @click="getPhoneNumberToMycoupon($event)" v-if="!appletConfiguration.hiddenCoupon">
					<view style="font-size:40rpx;font-weight: bold;">{{ cardTotal || 0 }}</view>
					<view>卡券</view>
				</view>
				<view class="menu-content" @click="JumpSettings('/pagesB/map/collection', 2)">
					<image src="http://image.bolink.club/Fj5Iuj-XfYgC8syp5quZnwpPGZ06" class="menu-img"></image>
					<view>收藏</view>
				</view>
				<view class="menu-content" v-if="!appletConfiguration.hiddenOfficialAccount" @click="JumpSettings('/pagesA/agreement/webView')">
					<image src="http://image.bolink.club/FtBnCZywBBvx7uhgADPAJUAK50tY" class="menu-img"></image>
					<!-- #ifdef MP-WEIXIN -->
					<view>关注公众号</view>
					<!-- #endif -->
					<!-- #ifdef MP-ALIPAY -->
					<view>关注生活号</view>
					<!-- #endif -->
				</view>				
			</view>
			</template>
			<!-- 有车牌 -->
			<view class="car-container" v-if="carListLen > 0">
				<swiper class="swiper" v-if="showSwiper" :indicator-dots="true" indicator-color="#EAEAEA"
					indicator-active-color="#318FFF" style="height:152rpx">
					<template v-if="carListLen > 0">
						<swiper-item v-for="item in carList">
							<view class="car-content">
								<view>
									<view class="add-car" @click="jumpCarEditor(item)">
										{{ item.plateNumber }}
									</view>
								</view>
								<image src="http://image.bolink.club/Fnl8yJEiPW6igtu4I1oZ-OLM9UbP" class="car-img"
									@click="jumpCarEditor(item)"></image>
							</view>
						</swiper-item>
					</template>
					<swiper-item>
						<view class="car-content">
							<view>
								<view class="add-car" @click="jumpCarEditor">
									添加爱车享专属服务
								</view>
							</view>
							<view class="add-car-btn" @click="jumpCarEditor('')">添加爱车</view>
						</view>
					</swiper-item>
				</swiper>

			</view>
			<!-- 没车牌 -->
			<view class="car-container" v-else>
				<view class="car-content">
					<view>
						<view class="add-car" v-if="verifyPhone"  @click="JumpSettings('/pagesD/car/carSubscribe',2)">
							添加爱车享专属服务
						</view>
						<view class="add-car" @click="jumpCarEditor" v-else>
							添加爱车享专属服务
						</view>
					</view>
					<view class="add-car-btn" @click="JumpSettings('/pagesD/car/carSubscribe',2)"  v-if="verifyPhone">添加爱车</view>
					<view class="add-car-btn" @click="jumpCarEditor('')"  v-else>添加爱车</view>
				</view>
			</view>

			<view class="charge-menu-container">
				<view class="charge-menu-content">
					<view v-for="(item, index) in mySeverList">
						<button class="content" v-if="item.type === 'getphonenumber' && verifyPhone" 
							@click="handleJump($event, item)">
							<image :src="item.icon" class="menu-img"></image>
							<view class="menu"> {{ item.name }}</view>
						</button>

						<button class="content" @click="handleNavItemClick(item)" v-else>
							<image :src="item.icon" class="menu-img"></image>
							<view class="menu"> {{ item.name }}</view>
						</button>
					</view>
				</view>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<view class="community-service-container">
				<view class="community-service-content">
					<view class="community-service">
						<view>社区服务</view>
						<button class="community" v-if="!appletConfiguration.hiddenCommunity&&verifyPhone" 
							@click="JumpSettings('/pagesI/index/index', 2)">{{ userAddress }}
							<image src="http://image.bolink.club/FpSWEdRu4M4x_xjqBuVRjp6-Jtid" class="community-service-arrow">
							</image>
						</button>
						<button class="community" v-if="!appletConfiguration.hiddenCommunity&&!verifyPhone"
							@click="!verifyPhone ? handleHouseToggle() : ''">{{ userAddress }}
							<image src="http://image.bolink.club/FpSWEdRu4M4x_xjqBuVRjp6-Jtid" class="community-service-arrow">
							</image>
						</button>
					</view>
					<view class="community-service-menu">
						<view v-for="(item, index) in communityList">
							<button class="content"  @click="handleJump($event, item)"
								v-if="item.type === 'getphonenumber' && verifyPhone">
								<image :src="item.icon" class="menu-img"></image>
								<view class="menu"> {{ item.name }}</view>
							</button>

							<button class="content" v-else @click="handleNavItemClick(item, '社区服务')">
								<image :src="item.icon" class="menu-img"></image>
								<view class="menu">{{ item.name }}</view>
							</button>
						</view>


					</view>
				</view>
			</view>
			<view class="flex marb-20 padt-20" v-if="!appletConfiguration.hiddenOtherMenu && otherList.length > 0 && otherList[0].name!=''">
				<view class="user-card my-server">
					<view class="card-title">
						其他
					</view>
					<view class="flex">
						<view class="flex100-25" v-for="(item,index) in otherList">
							<button class="flex-col icon-box" v-if="item.type==='getphonenumber' && verifyPhone"							
							 @click="handleJump($event,item)">
								<view class="ajc of-rel">
									<van-icon name="" dot class="icon-red" v-if="showIcon && item.flag==='red'"/>
									<image class="square-70" :src="item.icon"></image>
								</view>
								<view class="ajc">
									{{item.name}}
								</view>
							</button>
							<button v-else class="flex-col icon-box" @click="handleNavItemClick(item)">
								<view class="ajc of-rel">
									<van-icon name="" dot class="icon-red" v-if="showIcon && item.flag==='red'"/>
									<image class="square-70" :src="item.icon"></image>
								</view>
								<view class="ajc">
									{{item.name}}
								</view>
							</button>
						</view>
					</view>
				</view>
			</view>
			<!-- #endif -->
		</view>
		</view>
		<van-dialog id="van-dialog" />
		<userProfile :showPopup="showPopup" @onClose="onClose" ref="userProfile"></userProfile>
		<!-- 咪咕活动弹框 -->
		<van-overlay z-index="1000" :show="showActivity">
			<view class="activity-view activity-view-flex" v-if="!showActivityGuide">
				<button :open-type="mobile === '' ? 'getPhoneNumber' : ''" @getphonenumber="getPhoneNumber($event, 'miguAd')"
					@click="handleCouponReceive">
					<image mode="aspectFit" src="https://image.bolink.club/migu_1.png" class="activity-ad--img"></image>
				</button>
			</view>
			<view class="activity-view" v-if="showActivityGuide">
				<image mode="aspectFit" src="https://image.bolink.club/my_coupon_guide.png" class="activity-ad--guide"
					@click="showActivity = false"></image>
			</view>
		</van-overlay>
		<tab-bar :currentIndex="2"></tab-bar>
		
	</view>
</template>

<script>
import BaseConfig from "../../common/config/index.config.js"
import apis from "../../common/apis/index"
import constant from "../../common/utils/constant.js"
import form from "../../common/utils/form.js"
import guide from "../../components/guide/guide.vue"
import CustomPanel from "../index/CustomPanel.vue"
import BlCell from "../../components/bl-cell/bl-cell.vue"
import userProfile from '../../components/user-profile/user-profile.vue'
import util from '../../common/utils/util'
let app = getApp()
export default {
	components: {
		guide,
		CustomPanel,
		BlCell,
		userProfile
	},
	data() {
		return {
			status: "",
			mySeverList: [
				{
					name: "消息", //名称
					icon: "https://image.bolink.club/FgU34kwkXbMixFjc6ipjiarjhVjJ", //图标
					target: "navigateTo", // 跳转方式
					path: "/pagesE/user/message", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入消息",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "消息列表",
					},
				},
				{
					name: "分享", //名称
					icon: "https://image.bolink.club/FuHvm6wfS7lK_T_V3cwwSTd8_JEM", //图标
					target: "navigateTo", // 跳转方式
					path: "/pages/activity/dis", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入分享",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "消息分享列表",
					},
				},
				{
					name: "开发票", //名称
					icon: "https://image.bolink.club/yima/invoice.png", //图标
					target: "navigateTo", // 跳转方式
					path: "/pagesE/user/record", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入开发票页面",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "发票列表",
					},
				},
				{
					name: "常见问题", //名称
					icon: "https://image.bolink.club/FpyVQwumUh5cJVsOL73krhE9D1E3", //
					flag: "showModel",
				},
			],
			communityList: [
				{
					name: "消息", //名称
					icon: "https://image.bolink.club/FgU34kwkXbMixFjc6ipjiarjhVjJ", //图标
					target: "navigateTo", // 跳转方式
					path: "/pagesE/user/message", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入消息",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "消息列表",
					},
				},
				{
					name: "分享", //名称
					icon: "https://image.bolink.club/FuHvm6wfS7lK_T_V3cwwSTd8_JEM", //图标
					target: "navigateTo", // 跳转方式
					path: "/pages/activity/dis", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入分享",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "消息分享列表",
					},
				},
				{
					name: "开发票", //名称
					icon: "https://image.bolink.club/yima/invoice.png", //图标
					target: "navigateTo", // 跳转方式
					path: "/pagesE/user/record", // 跳转路径
					type: "getphonenumber", // 容器类型 - 需要获取手机号
					meta: {
						keyWord: "我的页-点击进入开发票页面",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "发票列表",
					},
				},
			],
			otherList: [
				{
					name: "关于我们", //名称
					icon: "https://image.bolink.club/FgU34kwkXbMixFjc6ipjiarjhVjJ", //图标
					target: "navigateTo", // 跳转方式
					path: "/pagesA/agreement/agreement", // 跳转路径
					meta: {
						keyWord: "我的页-点击进入协议",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "协议列表",
					},
				},
			],
			showIcon: false,
			showPopup: false,
			avatarUrl: "", //用户头像
			defaultAvatarUrl: '',//默认头像
			nickname: "", //用户昵称
			// 获取的用户楼宇信息
			userBuildingInfo: {},
			customPanelStyle: {
				margin: "0 28rpx 20rpx 28rpx",
				padding: "28rpx 36rpx",
			},
			// mywalletData:{
			// 	name: '钱包充值',
			// 	icon: 'https://image.bolink.club/Fo5wbMpypDPfr2EjOX9Q4B9X_gvc',
			// 	target: 'navigateTo',// 跳转方式
			// 	path: '/pagesF/order/mywallet',// 跳转路径
			// 	type: 'getphonenumber',// 容器类型 - 需要获取手机号
			// 	flag: 'mywallet',// 功能标记，一般和type配合使用
			// 	meta: {
			// 		keyWord: '发现页-点击进入一键乘梯',
			// 		clickType: 'Button',
			// 		jumpType: '本程序页面',
			// 		jumpDesc: '一键乘梯',
			// 	}
			// },//钱包余额跳转数据
			showActivityGuide: false,
			showActivity: false,
			showGuide: false,
			guideTips: "点击右上角 “···”按钮，可以直接分享到微信朋友圈",
			mobile: "", // 用户手机号
			encryptionMobile: "",
			carListLen: 0,
			showQaPopup: false,
			username: "请先登录",
			userInfo: app.globalData.userInfo, // 用户信息
			showSwiper: true,
			carList: [], // 车牌信息
			current: 0, // 当前车牌下标
			amountTotal:'',//钱包总金额
			stateList: [], // 车牌的状态数据
			windowHeight: null,
			statusBarHeight: null,
			navHeight: null,
			cardTotal: 0,
			isShowToggleTip: false,
			showWuye: false, // 物业管理
			showYezhu: false, // 业主菜单是否展示
			showTimeCheck: false, // 时间校验是否展示
			appletConfiguration: null, //小程序配置
		}
	},
	computed: {
		isShowMember() {
			const { userType } = this.userBuildingInfo
			return userType === 1 || userType === 3
		},
		enrollStatusText() {
			let str = "未提交",
				status = this.userBuildingInfo.status,
				arr = constant.INFO_TYPE
			//状态，1 审核通过，0 审核不通过，2 未审核，3 未初始化，4未提交
			arr.map((item) => {
				if (String(item.value_no) === String(status)) {
					str = item.value_name
				}
			})
			return str
		},
		userAddress() {
			let str = "点击选择项目",
				bl_community_name = ""
			this.isShowToggleTip = false
			if (this.userBuildingInfo.buildName) {
				bl_community_name = uni.getStorageSync("bl_community_name") || ""
			} else {
				bl_community_name = uni.getStorageSync("bl_community_name") || ""
			}
			if (bl_community_name !== "") {
				str = bl_community_name
				this.isShowToggleTip = true
			}
			return str
		},
		canShowMask() {
			return this.userInfo === ""
		},
		verifyPhone() {
			let boo = true
			try {
				let mobile = this.mobile
				console.log("verifyPhone", mobile, String(mobile).length === 11)
				if (mobile && String(mobile).length === 11) {
					boo = false
				} else {
					boo = true
				}
			} catch (e) {
				boo = true
			}
			return boo
		},
	},
	/**
	 * @param {Object} options
	 * 	activity -- 展示咪咕活动信息
	 */
	onLoad(options) {
		// #ifdef MP-WEIXIN
		this.appletConfiguration = util.getAppletConfiguration();
		// #endif
		getApp()
			.getSystemInfo()
			.then((res) => {
				this.windowHeight = res.windowHeight
				this.statusBarHeight = res.statusBarHeight
				this.navHeight = res.navHeight || res.titleBarHeight
			})
		// let formData = new FormData()

		if (options.activity) {
			this.showActivity = true
		} else {
			setTimeout(() => {
				this.showGuide = true
			}, 0)
			setTimeout(() => {
				this.showGuide = false
			}, 3000)
		}
		// #ifdef MP-ALIPAY
		my.setNavigationBar({
		  frontColor: '#000000',
		  backgroundColor: '#ff0000',
		})
		// #endif
	},
	onShow() {
		uni.hideTabBar()
		this.mobile = uni.getStorageSync("mobile")
		this.getUserInfoNickname(1)

		this.encryptionMobile = this.encryption(this.mobile)
		this.getElementIcon()
		if (this.mobile) {
			this.login()
			this.queryMyWallet()
		}
	},
	onHide() {
		this.showActivity = false
	},
	onReady() {
		app.pagePathRecord()
	},
	watch: {
		carList(val, oldVal) {
			this.carListLen = val.length
		},
	},
	methods: {
		// 查询钱包余额
		async queryMyWallet() {
			try {
				let data = await util.getWallet()
				// this.freeze_amount = data.freeze_amount
				// this.money = data.available_amount
				this.amountTotal = data.total
			} catch (error) {
				uni.showToast({
					title: '钱包查询异常',
					icon: 'none'
				})
			}
		},
		onClose(num) {
			this.showPopup = num
			this.getUserInfoNickname(2)
		},
		// 获取用户昵称 type 1 初始化 2保存后查询更新
		async getUserInfoNickname(type){
			if(this.mobile||!this.verifyPhone){
				let res=await util.getUserInfoNickname()
				if(res.flag){
					let data=res.user
					this.avatarUrl = data.headimgurl
					this.nickname = data.nickname
				}
			}

			let user=uni.getStorageSync("pushManageUser")
			if(type==1&&!user.nickname){
				apis.homeApis.uploadPic().then((res) => {
						this.avatarUrl = this.avatarUrl?this.avatarUrl:res.data.avatarUrl
				})
				this.nickname = "用户昵称"
			}

		},
		async JumpSettings(url, num) {
			// url=/pagesA/agreement/agreement 账户设置先缓存原来数据，2选择社区或者跳转到别的页面
			if (url == '/pagesA/agreement/agreement') {
				let agreementUser = {
					avatarUrl: this.avatarUrl,
					nickname: this.nickname,
					encryptionMobile: this.encryptionMobile,
					defaultAvatarUrl: this.defaultAvatarUrl
				}
				uni.setStorageSync("agreementUser", agreementUser)

			}
			if(num==2&&this.verifyPhone){
				let res=await util.getMobile()
				if(res.flag===0){
					let jumpUrl=url
					url = `/pagesA/agreementLogin/login?isEmpower=true&jumpType=3&jumpUrl=${jumpUrl}`
				}
				
			}
			uni.navigateTo({
				url,
			})


		},
		// 微信获取用户昵称头像弹框
		async uniGetUserProfile() {
			// #ifdef MP-WEIXIN
			if(this.avatarUrl){
				this.$refs.userProfile.avatarUrl=this.avatarUrl
			}
			this.$refs.userProfile.nickname =this.nickname&&this.nickname!=='用户昵称'?this.nickname:''
			this.$refs.userProfile.error =false
			this.showPopup=true
			//#endif 

			// #ifdef MP-ALIPAY
			let userInfo= await util.getAuthCode()
			this.avatarUrl=userInfo.headimgurl
			this.nickname=userInfo.nickname
			let params={
				nickName:userInfo.nickname,
				avatarUrl:userInfo.headimgurl
			}
			util.setUserInfo(params)

			//#endif 

		},
		login() {
			let params = {
				pageNum: 1,
				pageSize: 30,
				openId: uni.getStorageSync("openId"),
			}
			apis.homeApis
				.forumGetReplyList(params)
				.then((res) => {
					let index = res.data.rows.findIndex((item) => {
						return item.isRead === 0
					})
					this.showIcon = index > -1
				})
				.catch((err) => { })

			this.getcarinfo()
			// 判断是否有token和session_key
			app.isloginSuccess()
			let mobile = uni.getStorageSync("mobile")
			this.getUserInfo(mobile)
			this.getCouponData()
			this.initShow()
		},
		showErroTost() {
			uni.showToast({
				title: "功能暂未开放，敬请期待",
				icon: "none",
				duration: 3000,
			})
		},
		// 获取图标
		getElementIcon() {
			if(this.appletConfiguration && this.appletConfiguration.userDefaultMenu){
				this.mySeverList = this.appletConfiguration.userDefaultMenu
				this.communityList = this.appletConfiguration.communityMenu
				console.log(this.communityList)
			}else{
				apis.homeApis.getElementIcon(["2", "3", "4"]).then((res) => {
					console.log(res)
					if (res.data.length > 0) {
						let mySeverList = res.data[0].icons
						let length1 = res.data[0].icons.length
						for (let i = length1; i < 4; i++) {
							mySeverList.push({
								name: "",
								icon: "",
								target: "",
							})
						}
						// #ifdef MP-ALIPAY
						mySeverList=[]
						res.data[0].icons.forEach((item)=>{
							if(item.name!=='我的桩'){
								mySeverList.push(item)
							}
							
						})
						// #endif
						this.mySeverList = mySeverList
						if (res.data.length > 1) {
							let communityList = res.data[1].icons
							let length2 = res.data[1].icons.length
							const bl_user_info = uni.getStorageSync("bl_user_info")
							let userType = bl_user_info.userType || ""
							userType = userType && userType !== 1 && userType !== 3 // 根据用户类型显示菜单
							for (let i = 0; i < length2; i++) {
								if (communityList[i].flag == "personalMan" && !this.showYezhu) {
									communityList.splice(i, 1)
									i--
									length2--
								}
								if (communityList[i].flag == "timeMan" && !this.showTimeCheck) {
									communityList.splice(i, 1)
									i--
									length2--
								}

								if (communityList[i].flag == "memberMan" && userType) {
									communityList.splice(i, 1)
									i--
									length2--
								}

								if (communityList[i].flag == "VisitorMan" && userType) {
									communityList.splice(i, 1)
									i--
									length2--
								}
							}
							let lengthup = length2 <= 4 ? 4 : 8
							for (let i = length2; i < lengthup; i++) {
								communityList.push({
									name: "",
									icon: "",
									target: "",
								})
							}
							this.communityList = communityList
							console.log(this.communityList, "this.communityList")
						}
						if (res.data.length > 2) {
							let otherList = res.data[2].icons
							let length3 = res.data[2].icons.length
							for (let i = length3; i < 4; i++) {
								otherList.push({
									name: "",
									icon: "",
									target: "",
								})
							}
							// 设备运维展示处理
							for (let i in otherList) {
								if (otherList[i].name === "设备运维") {
									let flag = otherList[i].flag
									let mobile = uni.getStorageSync("mobile")
									if (!mobile || !flag || !flag.includes(mobile)) {
										otherList.splice(i, 1)
									}
								}
							}
							this.otherList = otherList
						}
					}
				})
			}
		},
		// 处理获取手机号后的跳转
		async handleJump(evt, row) {
			let res=await util.getMobile()
			if(res.flag===0){
				row=JSON.stringify(row)
				uni.setStorageSync("jumpRow",row)
				uni.navigateTo({
					url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=1`,
				})
				return
			}else{
				this.handleNavItemClick(row)
			}
		},
		hintModel(text) {
			uni.showModal({
				title: "",
				showCancel: false,
				confirmText: "好的",
				content: text || "暂无此功能，敬请期待",
				success: function (res) { },
			})
		},
		// 点击导航栏子元素
		handleNavItemClick(row, name) {
			console.log(row)
			// #ifdef MP-ALIPAY
			if (
				name === "社区服务"
			) {
				this.showErroTost()
				return false
			}
			// if(row.name=='停车记录'){
			// 	row.path='/pagesE/invoice/record'
			// }
			// #endif
			// #ifdef APP-PLUS
			if (row.name === "停车记录") {
				this.showErroTost()
				return false
			}
			// #endif
			console.log(row)
			let startTime = new Date().getTime()
			if (row.flag === "showModel") {
				this.showQaPopup = true
				return false
			}
			if (row.flag === "basicInfo") {
				this.toBase()
				return false
			}
			if (row.flag === "memberMan") {
				this.goMemberPage()
				return false
			}
			if (row.flag === "VisitorMan") {
				this.goVisitorsPage()
				return false
			}
			if (row.icon === "") {
				return false
			}
			if (row.path === undefined || row.path === "") {
				return this.hintModel()
			}
			// 重新组装 path 数据
			let jumpPath = row.path

			// 页内跳转
			if (row.target === "tab") {
				uni.switchTab({
					url: jumpPath,
				})
			} else if (row.target === "url") {
				if (row.path === "placement_information") {
					if (Math.round(new Date()) > BaseConfig.releaseTime) {
						getApp().jumpWeiBao("weibao-车险服务导航")
					} else {
						uni.showToast({
							title: "功能暂未开通，敬请期待",
							icon: "none",
							duration: 3000,
						})
					}
					return false
				}
				getApp().jumpAd({
					url: jumpPath,
					keyWord: row.meta.keyWord,
				})
			} else if (row.target === "mini_program") {
				let meta = row.meta
				uni.navigateToMiniProgram({
					appId: meta.appId,
					path: jumpPath,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: meta.keyWord,
							clickType: "Button",
							jumpType: meta.jumpType,
							jumpDesc: meta.jumpDesc,
							result:
								res.errMsg == "navigateToMiniProgram:ok" ? "成功" : "失败",
							startTime: startTime,
							endTime: new Date().getTime(),
						})
					},
				})
			} else {
				let meta = row.meta
				uni.navigateTo({
					url: jumpPath,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: meta.keyWord,
							clickType: meta.clickType,
							jumpType: meta.jumpType,
							jumpDesc: meta.jumpDesc,
							result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
							startTime: startTime,
							endTime: new Date().getTime(),
						})
					},
				})
			}
		},
		getMobile() {
			let boo = true
			try {
				let mobile = uni.getStorageSync("mobile")
				if (mobile && String(mobile).length === 11) {
					boo = false
				} else {
					boo = true
				}
			} catch (e) {
				boo = true
			}

			return boo
		},
		clickImage() {
			uni.chooseImage({
				count: 1,
				crop: {
					width: 65,
					height: 65,
				},
				success: (res) => {
					console.log(res)
					let url = BaseConfig.baseUrl + "/capp/api/user/avatar"
					let filePath = res.tempFilePaths[0]
					uni.uploadFile({
						url: url,
						filePath: filePath,
						name: "avatar",
						header: {
							"content-type":
								"multipart/form-data;boundary=" + new Date().getTime(),
							token: uni.getStorageSync("token"),
						},
						success: (ret) => {
							let data = JSON.parse(ret.data)
							console.log(data.data.avatarUrl)
							this.avatarUrl = data.data.avatarUrl
							this.defaultAvatarUrl = data.data.avatarUrl
						},
					})
				},
			})
		},
		initShow() {
			const projectId = uni.getStorageSync("bl_community_ladder_id")
			const bl_user_info = uni.getStorageSync("bl_user_info")
			if (projectId && bl_user_info.status == 1 && bl_user_info.isProperty) {
				this.showYezhu = true
				let control = uni.getStorageSync("bl_door_access_group")
				if (control) {
					let { TkDeviceType } = control
					if (TkDeviceType == 1) {
						this.showTimeCheck = true
					} else {
						this.showTimeCheck = false
					}
				} else {
					let params = {
						mobile: uni.getStorageSync("mobile"),
						projectId,
						userId: bl_user_info.id || -1,
					}
					apis.smartCommunityApis
						.selectbyid(params)
						.then((res) => {
							if (res.code === 200) {
								uni.setStorageSync("bl_door_access_group", res.data) // 缓存门禁权限接口数据
								let { TkDeviceType } = res.data
								if (TkDeviceType == 1) {
									this.showTimeCheck = true
								} else {
									this.showTimeCheck = false
								}
							} else {
								this.showTimeCheck = false
							}
						})
						.catch((err) => {
							this.showTimeCheck = false
						})
				}
			} else {
				this.showTimeCheck = false
				this.showYezhu = false
			}
		},
		handleCommunitySwitch() {
			if (this.verifyPhone) {
				return false
			}
			uni.navigateTo({
				url: "/pagesI/index/index",
			})
		},
		// 成员管理页面
		goMemberPage() {
			if (this.verifyPhone) {
				return false
			}
			let { status } = this.userBuildingInfo
			if (status === 1) {
				uni.navigateTo({
					url: "/pagesG/member/member",
				})
			} else {
				uni.showToast({
					title: "基本信息审核后才能使用该功能",
					icon: "none",
					duration: 5000,
				})
			}
		},
		// 访客管理页面
		goVisitorsPage() {
			if (this.verifyPhone) {
				return false
			}
			let { status } = this.userBuildingInfo
			if (status === 1) {
				uni.navigateTo({
					url: "/pagesI/visitors/visitors",
				})
			} else {
				uni.showToast({
					title: "基本信息审核后才能使用该功能",
					icon: "none",
					duration: 5000,
				})
			}
		},
		// 业主录入页面
		goOwnerPage() {
			if (this.verifyPhone) {
				return false
			}
			uni.navigateTo({
				url: "/pagesG/owners/personal",
			})
		},
		jumpProcess() {
			if (this.verifyPhone) {
				return false
			}

			let { status } = this.userBuildingInfo
			if (status === 0 || status === 2) {
				uni.navigateTo({
					url: `/pagesG/collect_user_info/infoResults?status=${status}`,
				})
				return false
			}
			if (status === 1) {
				uni.navigateTo({
					url: `/pagesG/collect_user_info/basePreview`,
				})
				return false
			}
			uni.navigateTo({
				url: "/pagesG/collect_user_info/base",
			})

			try {
				// const bl_community_id = uni.getStorageSync('bl_community_id');
				// if (bl_community_id) {
				// }
			} catch (e) {
				//TODO handle the exception
			}
		},
		toBase() {
			if (this.verifyPhone) {
				return false
			}
			//状态，1 审核通过，0 审核不通过，2 未审核
			let { status, detail } = this.userBuildingInfo
			// if (status === 0 || status === 2) {
			// uni.navigateTo({
			// 	url: `/pagesG/collect_user_info/infoResults?status=${status}`
			// })
			// return false;
			// }
			// if (status === 1) {
			// 	uni.navigateTo({
			// 		url: `/pagesG/collect_user_info/basePreview`
			// 	})
			// 	return false;
			// }
			if (status === 0) {
				uni.navigateTo({
					url: `/pagesG/collect_user_info/infoResults?status=${status}&detail=${detail}`,
				})
				return false
			}
			if (status === 1 || status === 2) {
				uni.navigateTo({
					url: `/pagesG/collect_user_info/basePreview?info=${encodeURIComponent(
						JSON.stringify(this.userBuildingInfo)
					)}`,
				})
				return false
			}
			uni.navigateTo({
				url: "/pagesG/collect_user_info/base",
			})
		},
		// 切换房屋
		handleHouseToggle() {
			// #ifdef MP-ALIPAY
			this.showErroTost()
			return false
			// #endif
			uni.navigateTo({
				url: "/pagesI/index/index",
			})
		},
		/**
		 * @desc 根据手机号获取用户信息
		 * @param {String} mobile
		 */
		getUserInfo(mobile) {
			app
				.getRemoteUserInfo()
				.then((res) => {
					console.log("get user info: ", res)
					this.userBuildingInfo = res
				})
				.catch((err) => {
					this.userBuildingInfo = {}
				})
			return false
			apis.smartCommunityApis
				.getUserInfo({
					mobile: mobile,
					comid: uni.getStorageSync("bl_community_id"),
					openid: uni.getStorageSync("openId"),
				})
				.then((res) => {
					console.log("get user info: ", res)
					if (res.code === 200) {
						this.userBuildingInfo = res.data
					} else if (res.code === 203) {
						this.userBuildingInfo = {}
					} else {
						this.userBuildingInfo = {}
					}
					uni.setStorageSync("bl_user_info", this.userBuildingInfo)
				})
				.catch((err) => {
					this.userBuildingInfo = {}
					uni.setStorageSync("bl_user_info", this.userBuildingInfo)
				})
			this.status = this.userBuildingInfo.status
		},
		// 咪咕券领取
		handleCouponReceive() {
			if (this.mobile === "") {
				return false
			}
			apis.homeApis
				.getMiguCoupon()
				.then((res) => {
					console.log("ad res: ", res)
					if (res.status === 200) {
						this.showActivityGuide = true
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 5000,
						})
						setTimeout(() => {
							this.showActivity = false
						}, 5000)
					}
				})
				.catch((err) => {
					uni.showToast({
						title: "网络错误,请稍后重试",
						icon: "none",
						duration: 3000,
					})
					this.showActivity = false
				})
		},
		// 获取卡包数量
		getCouponData() {
			let params = {
				type: 1,
			}
			apis.homeApis
				.getcouponByopenId(params)
				.then((res) => {
					let rows = res.data.rows || [];
					let useTickets = res.data.useTickets || [];
					this.cardTotal = rows.length + useTickets.length
				})
				.catch((err) => {
					console.log(err, "卡包查询失败")
				})
		},
		// 加密手机号
		encryption(value) {
			if (!value) return
			var reg = /^(\d{3})\d{4}(\d{4})$/
			return value.replace(reg, "$1****$2")
		},
		/**
		 * @param {Object} e event
		 * @param {Object} type 点击的某一行标记
		 */
		async getPhoneAgreement(e, name){
			console.log(e, '我的页面获取手机', name)			
			let res=await util.getMobile()
			if(res.flag===0){
				uni.navigateTo({
					url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=4`,
				})
			}else{
				this.login()
				this.encryptionMobile = this.encryption(this.mobile)
				this.getElementIcon()
				
				if (name === "miguAd") {
					this.handleCouponReceive()
				}
			}
			

		},
		getPhoneNumber(e, name) {
			// #ifdef MP-ALIPAY
			my.getPhoneNumber({
				success: (res) => {
					console.log("授权成功", res)
					getApp()
						.getPhoneNumber(e, this, res)
						.then((res) => {
							if (res) {
								this.mobile = res.mobile
								if (res.mobile != uni.getStorageSync("mobile")) {
									uni.removeStorageSync("bl_door_access_group")
									uni.removeStorageSync("bl_user_info")
									uni.removeStorageSync("bl_door_control_array")
									uni.removeStorageSync("bl_door_control_group")
									uni.removeStorageSync("bl_community_id")
									uni.removeStorageSync("bl_community_ladder_id")
									uni.removeStorageSync("bl_community_name")
								}
								this.getUserInfoNickname()
								this.login()
								this.encryptionMobile = this.encryption(this.mobile)
								this.getElementIcon()

								if (name === "miguAd") {
									this.handleCouponReceive()
								}
							}
						})
						.catch((res) => {
							uni.showToast({
								title: "糟糕，网络出小差了",
								icon: "none",
							})
						})
				},
				fail: (err) => {
					console.log("授权失败", err)
				},
			})
			// #endif

			// #ifdef MP-WEIXIN
			getApp()
				.getPhoneNumber(e)
				.then((res) => {
					if (res) {
						this.mobile = res.mobile
						if (res.mobile != uni.getStorageSync("mobile")) {
							uni.removeStorageSync("bl_door_access_group")
							uni.removeStorageSync("bl_user_info")
							uni.removeStorageSync("bl_door_control_array")
							uni.removeStorageSync("bl_door_control_group")
							uni.removeStorageSync("bl_community_id")
							uni.removeStorageSync("bl_community_ladder_id")
							uni.removeStorageSync("bl_community_name")
						}
						this.getUserInfoNickname()
						this.login()
						this.encryptionMobile = this.encryption(this.mobile)
						this.getElementIcon()

						if (name === "miguAd") {
							this.handleCouponReceive()
						}
					}
				})
				.catch((res) => {
					uni.showToast({
						title: "糟糕，网络出小差了",
						icon: "none",
					})
				})
			// #endif
		},
		// 获取手机号并跳转停车券页面
		getPhoneNumberToMycoupon(e) {
			// #ifdef APP-PLUS
			return this.showErroTost()
			// #endif
			let startTime = new Date().getTime()
			uni.navigateTo({
				url: "/pagesC/coupon/index",
				complete: (res) => {
					app.eventRecord({
						keyWord: "我的-导航-停车劵",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "停车劵页面",
						result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
						startTime: startTime,
						endTime: new Date().getTime(),
					})
				},
			})
		},
		// 获取用户信息的回调
		getUserProfile(e) {
			let type = e.currentTarget.dataset.type
			app.updataUserInfo().then(() => {
				this.userInfo = app.globalData.userInfo
				this.getcarinfo()
				if (type) {
					this.jumpNav(parseInt(type))
				}
			})
		},
		// 跳转违章
		jumpWeizhang(item) {
			uni.navigateTo({
				url: `/pagesE/user/rules?plateNumber=${item.plateNumber}`,
			})
		},
		// 点击操作
		jumpNav(type) {
			let startTime = new Date().getTime()
			console.log(type)
			// if (type<6 && (!this.token || !this.userInfo)) {
			// 	uni.showToast({
			// 		title: "请先登录",
			// 		icon: "none"
			// 	})
			// 	return false;
			// }
			switch (type) {
				case 1: // 我的车辆
					uni.navigateTo({
						url: "/pagesD/car/carList",
					})
					break
				case 2: // 我的驾驶证
					uni.navigateTo({
						url: "/pageE/user/driving",
					})
					break
				case 3: // 我的违章
					uni.navigateTo({
						url: "/pagesE/user/rules",
					})
					break
				case 4: // 缴费记录
					uni.navigateTo({
						url: "/pagesE/invoice/record",
						complete: (res) => {
							app.eventRecord({
								keyWord: "我的-导航-缴费记录",
								clickType: "Button",
								jumpType: "本程序页面",
								jumpDesc: "缴费记录页面",
								result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
								startTime: startTime,
								endTime: new Date().getTime(),
							})
						},
					})
					break
				case 5: // 我的停车劵
					if (this.mobile) {
						uni.navigateTo({
							url: "/pagesE/user/mycoupon",
							complete: (res) => {
								app.eventRecord({
									keyWord: "我的-导航-停车劵",
									clickType: "Button",
									jumpType: "本程序页面",
									jumpDesc: "停车劵页面",
									result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
									startTime: startTime,
									endTime: new Date().getTime(),
								})
							},
						})
					}
					break
				case 6: // 消息通知
					uni.navigateTo({
						url: "/pagesE/user/message",
						complete: (res) => {
							app.eventRecord({
								keyWord: "我的-导航-消息通知",
								clickType: "Button",
								jumpType: "本程序页面",
								jumpDesc: "消息通知页面",
								result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
								startTime: startTime,
								endTime: new Date().getTime(),
							})
						},
					})
					break
				case 7: // 常见问题
					this.showQaPopup = true
					// uni.navigateTo({
					// 	url: "/pagesE/user/question",
					// 	complete: (res) => {
					// 		app.eventRecord({
					// 			keyWord: '我的-导航-常见问题',
					// 			clickType: 'Button',
					// 			jumpType: '本程序页面',
					// 			jumpDesc: '常见问题页面',
					// 			result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
					// 			startTime: startTime,
					// 			endTime: new Date().getTime()
					// 		});
					// 	}
					// })
					break
				case 8: // 我要吐槽
					uni.navigateTo({
						url: "/pages/activity/dis",
						complete: (res) => {
							app.eventRecord({
								keyWord: "点击吐槽图标-跳转",
								clickType: "图片",
								jumpType: "本程序页面",
								jumpDesc: "吐槽页面",
								result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
								startTime: startTime,
								endTime: new Date().getTime(),
							})
						},
					})
					break
				case 10: // 编辑
					uni.navigateTo({
						url: "/pagesD/car/car",
					})
					break

				case 9: // 充电订单
					uni.navigateTo({
						url: "/pagesF/order/index",
					})
					break
				case 11: // 我的钱包
					uni.navigateTo({
						url: "/pagesF/order/mywallet",
					})
					break
				case 12: // 开发票
					uni.navigateTo({
						url: "/pagesE/user/record",
					})
					break
				default:
					break
			}
		},
		// 获取车牌信息
		getcarinfo() {
			apis.homeApis
				.getcarinfo()
				.then((res) => {
					if (res.status == 200) {
						uni.stopPullDownRefresh()
						let carList = res.data
						if (carList.length === 0) {
							this.carList = []
							return
						} else if (!carList || carList.length < 0) {
							return
						}
						let index
						if (res.carNum) {
							uni.setStorageSync("plateNumber", res.carNum.join(",")) // 缓存常用车
						}
						for (let i = 0; i < carList.length; i++) {
							if (carList[i].inCommonUse == 1) {
								index = i
							}
							try {
								carList[i].yearCheck = form.yearCheck(
									carList[i].registrationTime,
									carList[i].carType,
									carList[i].carApplication
								)
							} catch (e) {
								app.globalData.log.error(
									"--carList[i].yearCheck--->",
									e,
									carList[i]
								)
							}
						}
						carList.unshift(carList[index]) // 将常用车牌(inCommonUse==1)放到第一个
						carList.splice(index + 1, 1)
						let stateList = []
						for (let n = 0; n < carList.length; n++) {
							stateList.push(carList[n].state)
						}
						this.stateList = stateList
						this.carList = carList
						// if (this.carList.length > 0) {
						// 	this.preMargin = "30rpx";
						// 	this.nextMargin = "50rpx";
						// } else {
						// 	this.preMargin = "30rpx";
						// 	this.nextMargin = "50rpx";
						// }
					}
				})
				.catch((err) => {
					console.log(err)
				})
		},
		// 跳转编辑车辆信息页
		jumpCarEditor(item) {
			let startTime = new Date().getTime()
			let url = `/pagesD/car/carSubscribe`
			if (item) {
				url = `/pagesD/car/carSubscribe?item=${JSON.stringify(item)}`
			}
			uni.navigateTo({
				url: url,
				complete: (res) => {
					app.eventRecord({
						keyWord: "我的-顶部-车辆滚动卡片",
						clickType: "Button",
						jumpType: "本程序页面",
						jumpDesc: "编辑车辆信息页",
						result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
						startTime: startTime,
						endTime: new Date().getTime(),
					})
				},
			})
		},
		// app登录
		toLogin(){
			console.log('点击登陆')
			uni.navigateTo({
				url:'/pagesF/login/login',
				fail: err=>{
					console.log(err)
				}
			})
		}
	},
}
</script>
<style>

.my-page-custom-panel-style {
	margin: 0 28rpx 20rpx 28rpx;
	/* padding: 28rpx 36rpx; */
}
</style>
<style lang="less" scoped>
.userTop {
	width: 100%;
	position: relative;
 }

.icon-red {
	position: absolute;
	top: 10rpx;
	left: 50rpx;
}

.icon-comit {
	position: absolute;
	top: 0rpx;
	right: 10rpx;
	width: 50rpx;
	height: 20rpx;
	background-size: 100% 100%;
}

.bg-adopt {
	background: url("https://image.bolink.club/yima/adopt.png");
	background-repeat: no-repeat;
	background-size: cover;
}

.bg-cancelComit {
	background: url("https://image.bolink.club/yima/cancelComit.png");
	background-repeat: no-repeat;
	background-size: cover;
}

.bg-confirmComit {
	background: url("https://image.bolink.club/yima/confirmComit.png");
	background-repeat: no-repeat;
	background-size: cover;
}

.bl-cell {
	display: flex;
	align-items: center;
	padding: 10rpx 0;

	&:active {
		background-color: rgba(0, 0, 0, 0.1);
		opacity: 0.7;
	}

	&--icon {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 35rpx;
		height: 35rpx;
	}

	&--image {
		margin: 0;
		padding: 0;
	}

	&--text {
		margin: 0 21rpx;
		flex: 1;
		font-weight: 500;
		font-size: 32rpx;
	}
}

.custom-tag {
	display: flex;
	margin-right: 21rpx;
	padding: 13rpx 30rpx;
	min-width: 156rpx;
	font-size: 24rpx;
	border-top-left-radius: 30rpx 30rpx;
	border-bottom-right-radius: 30rpx 30rpx;
	text-align: center;
	background: #4894ff;

	.custom-tag--icon {
		margin: 0 10rpx 0 0;
		padding: 0;
		width: 23rpx;
		height: 26rpx;
	}

	.custom-tag--text {
		line-height: 26rpx;
		color: #fff;
	}
}

.activity-view {
	display: block;
	width: 100%;
	height: 100%;

	.activity-ad--img {
		margin: 0;
		padding: 0;
		height: 624rpx;
	}

	.activity-ad--guide {
		position: relative;
		top: 311rpx;
		left: 30%;
		margin: 0;
		padding: 0;
		width: 417rpx;
	}
}

.activity-view-flex {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.user {
	width: 100%;
	background-image: url('http://image.bolink.club/FhUwC1l_1V1FEgJut38yPKtElz8v');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	// background: #f8f8f8;
	// #ifdef MP-ALIPAY
	overflow-x: hidden;
	// #endif
}

//#ifdef MP-WEIXIN
.custom-back-nav {
	top: 0;
	left: 0;
	width: 100%;
	height: 540rpx !important;
	background: linear-gradient(124deg, #2f5ae5 0%, #468cfe 100%);
	border-bottom-right-radius: 30rpx;
	border-bottom-left-radius: 30rpx;
	z-index: 0;
}
// #endif

.container{
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	width:100%;
	padding-bottom: 120rpx;
}
.user-info {
	display: flex;
	justify-content: space-between;
	height: 120rpx;
	margin-top: 30rpx;
	.user-information {
		display: flex;
		padding: 0 40rpx;

		.avatar {
			width: 130rpx;
			height: 130rpx;
			border-radius: 50%;
			padding: 10rpx;
		}

		.avatar image {
			width: 100%;
			height: 100%;
			border-radius: 50%;
			box-shadow: 0px 0px 10rpx 0px rgba(131, 131, 131, 0.15);
			border: 3rpx solid #FFFFFF;
		}

		.user-name-container {
			padding: 0 32rpx;
			// #ifdef MP-ALIPAY
			padding-top:15rpx;
			// #endif

			.user-name {
				height: 90rpx;
				font-size: 46rpx;
				font-weight: 500;
				color: #000000;
				display: inline-block;
				margin-top: -15rpx;
				// #ifdef MP-WEIXIN
				width:310rpx;			
				//#endif 
				// #ifdef MP-ALIPAY
				width: 450rpx;
				// #endif
				white-space:nowrap;
				overflow:hidden;
				text-overflow:ellipsis;
			}

			.setup-container {
				display: flex;
				font-size: 24rpx;
				font-weight: 500;
				color: #000000;
				align-items: center;
				margin-top: -6rpx;

				.user-setup {
					width: 24rpx;
					height: 22rpx;
					margin-right: 10rpx;
				}
			}
		}
	}

	.news-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 159rpx;
		height: 48rpx;
		border-radius: 20rpx;
		  background-image: linear-gradient(
		    153deg,
		    rgba(65, 226, 157, 0.18) 0,
		    rgba(4, 149, 88, 0.18) 100%
		  );
		margin: 35rpx 32rpx 0 0;
		font-size: 24rpx;
		color: #000000;

		.user-news {
			width: 24rpx;
			height: 24rpx;
			margin-right: 10rpx;
		}
	}
}

.user-menu {
	display: flex;
	justify-content: space-between;
	height: 90rpx;
	padding: 0 63rpx;
	margin: 40rpx 0 32rpx 0;
	align-items: center;

	.menu-content {
		width: 140rpx;
		font-size: 26rpx;
		color: #000000;
		text-align: center;
	}
	.menu-content-margin{
		margin-top:-6rpx;
	}
	.wallet_container{
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		width: 140rpx;
		align-content: center;
		align-items: center;
		margin-bottom: 20rpx;
		margin-top: -6rpx;
		// #ifdef MP-ALIPAY
		margin-top: -4rpx;
		//#endif 
		.content{
			width: 140rpx;
			text-align: center;
			height: 54rpx;
			font-size:40rpx;
			font-weight: bold;
			padding-top: 4rpx;
			// #ifdef MP-ALIPAY
			padding-top:7rpx;			
			//#endif 

		}
		.wallet_title{
			width: 140rpx;
			text-align: center;
			height: 63rpx;
			margin-bottom: 7rpx;
			margin-top: -6rpx;
			padding-top:12rpx;
			// #ifdef MP-ALIPAY
			margin-bottom: 9rpx;
			padding-top:0rpx;	
			//#endif 
		}
	}

	.menu-img {
		width: 40rpx;
		height: 40rpx;
	}
}
.login-container{
	height: 210rpx;
	margin-top: 30rpx;
	.login-btn{
		box-shadow: 0 12rpx 32rpx 0 rgba(65, 226, 157, 0.28);
		// background-image: linear-gradient(90deg,rgba(65, 226, 157, 1) 0,rgba(4, 149, 88, 1) 100%);
		background:#ffffff;
		color:#0b9e60;
		border-radius: 44rpx;
		height: 74rpx;
		width: 200rpx;
		font-weight: 500;
		font-size: 28rpx;
		line-height: 74rpx;
		text-align: center;
		margin:0 auto;
	}
}
.car-container {
	width: 100%;
	height: 146rpx;
	margin: 0 auto;
	padding: 0 28rpx;

	.car-content {
		display: flex;
		justify-content: space-between;
		width: 100%;
		// background-color: #FFFFFF;
		background-image: url(http://image.bolink.club/Fuoda5TIHUzBtU2DSsokYHIjIIvC);
		background-size: 100% 100%;
		padding: 32rpx;
		border-radius: 20rpx;
		align-items: center;

		.add-car {
			font-size: 36tpx;
			font-weight: 800;
			color: #242e3e;
			line-height: 36rpx;
			font-family: PingFang-SC-Heavy, PingFang-SC;
		}

		.car-remind {
			font-size: 26rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			color: #828ea6;
			line-height: 26rpx;
			margin-top: 20rpx;
		}

		.add-car-btn {
			box-shadow: 0 12rpx 32rpx 0 rgba(57, 112, 240, 0.28);
			  background-image: linear-gradient(
				90deg,
				rgba(65, 226, 157, 1) 0,
				rgba(4, 149, 88, 1) 100%
			  );
			border-radius: 44rpx;
			height: 74rpx;
			width: 200rpx;
			font-weight: 500;
			color: #fefefe;
			font-size: 28rpx;
			line-height: 74rpx;
			text-align: center;
		}

		.car-img {
			width: 240rpx;
			height: 76rpx;
		}
	}
}

.charge-menu-container {
	width: 100%;
	margin: 20rpx auto;
	padding: 0 28rpx;

	.charge-menu-content {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		background-color: #ffffff;
		padding: 20rpx 26rpx;
		border-radius: 20rpx;

		.content {
			width: 140rpx;
			text-align: center;
			font-size: 28rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			color: #242e3e;
			margin: 15rpx 10rpx 15rpx 10rpx;
			height: 112rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			//#ifdef MP-ALIPAY
			height:130rpx;
			margin: 15rpx 10rpx 15rpx 10rpx;
			//#endif

			.menu-img {
				width: 48rpx;
				height: 48rpx;
			}

			.menu {
				font-size: 26rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 500;
				color: #242e3e;
				margin-top: 5rpx;
				width: 140rpx;
				text-align: center;
				
			}
		}
	}
}
// .charge-menu-content view:nth-child(4n){
// 	margin: 10rpx 0 10rpx 0;

// }
.community-service-container {
	width: 100%;
	margin: 20rpx auto;
	padding: 0 28rpx;
	height: 396rpx;

	.community-service-content {
		width: 100%;
		background-color: #ffffff;
		padding: 0 22rpx;
		border-radius: 20rpx;

		.community-service {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 36rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #242e3e;
			line-height: 36rpx;
			height: 112rpx;
			width: 100%;
			padding: 0 10rpx;
			margin: 32rpx auto 0 auto;

			.community {
				height: 100rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #828ea6;
				display: flex;
				align-items: center;
			}

			.community-service-arrow {
				width: 48rpx;
				height: 48rpx;
			}
		}

		.community-service-menu {
			width: 100%;
			display: flex;
			flex-wrap: wrap;

			.content {
				width: 140rpx;
				text-align: center;
				font-size: 28rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 500;
				color: #242e3e;
				margin: 0 14rpx 38rpx 10rpx;
				height: 112rpx;
				display: flex;
				flex-wrap: wrap;
				justify-content: center;

				.menu-img {
					width: 48rpx;
					height: 48rpx;
					margin-top: 16rpx;
				}
			}
		}
	}
}

.ava {
	width: 130rpx;
	height: 130rpx;
	margin-left: 20rpx;
	border-radius: 50%;
	padding: 10rpx;
}

.ava image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	box-shadow: 0px 0px 10rpx 0px rgba(131, 131, 131, 0.15);
}

.user-house {
	display: flex;
	font-size: 26rpx;
	font-weight: 500;
	text-align: left;
	color: #b0b6c1;

	&--icon {
		margin: 0;
		padding: 0;
		width: 30rpx;
		height: 37rpx;
	}

	&--name {
		margin: 0 10rpx;
	}
}

.small-gray {
	font-size: 26rpx;
	font-family: PingFang SC, PingFang SC-Medium;
	font-weight: 500;
	color: #828ea6;
}

.car-gray {
	color: #e1edff;
}

.main-gray {
	color: #bedcff;
}

.small-black {
	font-size: 40rpx;
	font-family: DIN, DIN-Bold;
	font-weight: 700;
	color: #242e3e;
}

.cc {
	background-color: #ccc;
}

.car-message {
	height: 165rpx;
	padding: 38rpx 0 0 32rpx;
	background: url("https://image.bolink.club/yima/addCarNumberBg.png");
	background-repeat: no-repeat;
	background-size: cover;

	.message {
		width: 350rpx;

		.title {
			color: #ffffff;
			font-size: 32rpx;
			font-family: PingFang-SC-Bold, PingFang-SC;
			font-weight: bold;
			color: #ffffff;
			line-height: 45rpx;
			margin-top: 5rpx;
		}

		.tip {
			color: #aac9f9;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #aac9f9;
			line-height: 33rpx;
			margin-top: 10rpx;
		}
	}

	.add-btn {
		margin: 22rpx 30rpx 22rpx 22rpx;
		width: 50rpx;
		height: 56rpx;
		line-height: 56rpx;
		font-weight: 700;
		color: #3970f0;
		background: linear-gradient(286deg, #f4d365 0%, #f8ec83 100%);
		border-radius: 33rpx;
	}
}

.car-card {
	height: 190rpx;
	width: 100%;

	.add-card {
		height: 170rpx;
		width: 170rpx;
		background: url("https://image.bolink.club/yima/addCarBg.png");
		background-repeat: no-repeat;
		background-size: 100% 100%;
		position: relative;

		.add-btn {
			top: 40rpx;
			left: 50rpx;
			height: 90rpx;
			width: 90rpx;
			background: url("https://image.bolink.club/FvvN1BhZqbktJ8vPtdOFm8YxczuX");
			background-repeat: no-repeat;
			background-size: cover;
		}
	}

	.car-msg {
		margin: 0 0 0rpx 20rpx;
		height: 170rpx;
		border-radius: 20rpx;
		// background: url('https://image.bolink.club/FmHqFhN-uN9Ut9KHRtqRA3VyuqYk');
		background: url("https://image.bolink.club/yima/showCarNumberBg.png");
		background-repeat: no-repeat;
		background-size: cover;
	}
}

.user-card {
	flex: 1;
	display: block;
	border-radius: 30rpx;
	background-color: #fff;
	margin: 0 28rpx;
	// width: calc(100vw - 80rpx);

	border-radius: 30rpx;

	.card-title {
		width: 100^;
		height: 50prx;
		font-size: 36rpx;
		font-family: PingFang SC, PingFang SC-Heavy;
		font-weight: 800;
		color: #242e3e;
		padding: 28rpx 0 40rpx 34rpx;

		&.reset-pad {
			padding: 28rpx 0 20rpx 34rpx;
		}
	}

	.card-menu {
		margin: 20rpx 0;
		height: 34rpx;
		font-size: 32rpx;
		font-family: PingFang SC, PingFang SC-Medium;
		font-weight: 500;
		color: #242e3e;

		.menu-title {
			height: 34rpx;
			line-height: 34rpx;
			width: 540rpx;
		}
	}

	.icon-box {
		height: 170rpx;
		font-size: 28rpx;
		font-family: PingFang SC, PingFang SC-Medium;
		font-weight: 500;
		color: #242e3e;
	}
}

.my-server {
	height: 287rpx;
}

.community {
	height: 450rpx;
}

.my-car {
	height: 250rpx;
}

.icon-order {
	height: 34rpx;
	width: 28rpx;
	margin: 0 3rpx;
}

.icon-right {
	height: 30rpx;
	width: 18rpx;
	padding: 2rpx;
}

.icon-phone {
	// height: 23rpx;
	width: 14rpx;
	margin-right: 5rpx;
}

.my-swiper {
	height: 220rpx;
}

.my-swiper-d {
	height: 220rpx;
	margin: 0 30rpx;
	padding: 0 5rpx;
}

.top-card {
	font-size: 26rpx;
	color: #fff;
	// height: 290rpx;
	margin-right: 35rpx;
	border-radius: 20rpx;
	position: relative;
}
</style>
