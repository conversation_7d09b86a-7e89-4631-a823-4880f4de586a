<template>
	<van-popup
		:show="showPopup"
		@close="onClose"
		round
		:close-on-click-overlay="false"
	>
		<view class="container">
			<button
				class="avatar-wrapper"
				open-type="chooseAvatar"
				@chooseavatar="onChooseAvatar"
			>
				<image class="avatar" :src="avatarUrl"></image>
			</button>
			<view class="nickname-container">
				<view style="padding-left: 10rpx;width:80rpx">昵称</view>
				<form>
					<input
						type="nickname"
						class="weui-input nickname"
						placeholder="请输入昵称"
						maxlength="16"
						v-model.trim="nickname"						
						minlength="1"

					/>
				</form>
			</view>
			<view v-show="error" class="error">请输入昵称</view>
			<view class="footer">
				<view class="cancel btn" @click="onClose">取消</view>
				<view class="submit btn" @click="submit">确定</view>
			</view>
		</view>
	</van-popup>
</template>

<script>
import apis from '../../common/apis/index.js'
import util from '../../common/utils/util.js'
export default {
	name: "userProfile",
	props: {
		showPopup: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			nickname: "",
			avatarUrl: "http://image.bolink.club/Fs5tt535seintY4699eH0MIEbYTQ",
			defaultUrl: "http://image.bolink.club/Fs5tt535seintY4699eH0MIEbYTQ",
			error:false,
		}
	},
	methods: {
		onChooseAvatar(e) {
			// 用户上传图片是微信临时链接，需要传到服务器换成永久的链接	
				let avatarUrl=this.avatarUrl//原来的头像
				this.avatarUrl = e.detail.avatarUrl
				apis.homeApis
					.imageUpload(e.detail.avatarUrl	)
					.then((res) => {						
						if (res.status === 200) {
							this.avatarUrl = res.data
						} else {
							this.avatarUrl=avatarUrl
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 3000,
							})
						}
					})
					.catch((err) => {
						
					})

		},
		onClose() {
			this.$emit("onClose", false)
			this.avatarUrl = this.defaultUrl
			this.nickname = ""
			this.error=false
		},
		async submit() {
			this.nickname = this.nickname.replace(/\s*/g, "")
			if (!this.nickname) {
				this.error=true
				return
			}
			this.error=false
			let params={
				nickName:this.nickname,
				avatarUrl:this.avatarUrl
			}
			let res=await util.setUserInfo(params)
			if(res.flag){
				this.onClose()
			}			
		}
	},
}
</script>
<style lang="scss" scoped>
.container {
	padding: 20rpx;
	width: 90vw;
	height: 470rpx;
	text-align: center;
	.avatar {
		width: 130rpx;
		height: 130rpx;
		border-radius: 20rpx;
	}
	.nickname-container {
		font-size: 28rpx;
		border-bottom: 1rpx solid #f8f8f8;
		border-top: 1rpx solid #f8f8f8;
		height: 100rpx;
		line-height: 100rpx;
		display: flex;
		color: #4a556b;
		.nickname {
			height: 100rpx;
			line-height: 100rpx;
			width:100%;
		}
	}
	.error{
		width:100%;
		text-align: left;
		font-size: 28rpx;
		color: red;
	}
	.footer {
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		justify-content: space-between;
		padding: 45rpx 40rpx 20rpx 40rpx;
		margin-bottom: 20rpx;
		.btn {
			width: 200rpx;
			height: 86rpx;
			line-height: 86rpx;
			font-size: 28rpx;
			color: #4a556b;
			border-radius: 50rpx;
		}
		.cancel {
			border: 1rpx solid #3078f4;
			color: #3078f4;
		}
		.submit {
			background: #3078f4;
			color: #f8f8f8;
		}
	}
}
</style>
