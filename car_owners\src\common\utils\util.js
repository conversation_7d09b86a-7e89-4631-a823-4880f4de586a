import apis from '../apis/index'
import utils from '../utils'
import md5 from '../../pagesF/static/utils/md5.js';
import BaseConfig from '../config/index.config.js'
import power from '../utils/power.js';
import authority from '../config/authority.js'
let source=localStorage.getItem('h5_source')
if(source=='zfb'){
	/* 	BaseConfig.parkAppid=BaseConfig.baseUrl.includes('mt.bolink.club')?'2017080408033352':'wxc4eced1c73751e0b' */
}
function padLeftZero(str) {
	return ('00' + str).substr(str.length);
};

function formatDate(date, fmt = "yyyy-MM-dd") {
	if (date === null || date === undefined || date === '') return '';
	date = new Date(date);
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	let o = {
		'M+': date.getMonth() + 1,
		'd+': date.getDate(),
		'h+': date.getHours(),
		'm+': date.getMinutes(),
		's+': date.getSeconds()
	};
	if (/(c)/.test(fmt)) {
		let currentDate = new Date(this.formatDate(new Date())).getTime()
		let timeDifference = new Date(this.formatDate(date)).getTime() - currentDate
		let str = timeDifference == 86400000 ? '（明天）' : timeDifference == 0 ? '（今天）' : ''
		fmt = fmt.replace(/(c)/, str);
	}
	for (let k in o) {
		if (new RegExp(`(${k})`).test(fmt)) {
			let str = o[k] + '';
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
		}
	}
	return fmt;
}

/**
 * @description 根据时间戳长度，自动补充长度为毫秒值
 */
function formatTimestamp(val) {
	if (val) {
		let len = String(val).length;
		if (len === 10 && val > 0) {
			return val * 1000;
		} else if (len === 13) {
			return val;
		}
		// 1970年之前出生的
		else if (val < 0) {
			return (val + '000') - 0;
		} else {
			return (val + '000') - 0;
		}
	} else {
		return '';
	}
}
/*
 * 将cityNo 转 cityName
 * cityData:原数据
 * cityNo：二级地区编码
 */
function getCityName(cityData, cityNo) {
	if (!cityNo) return;
	if (!(cityData instanceof Array)) return;
	// 9112
	cityNo += "";
	for (let i = 0; i < cityData.length; i++) {
		let sheng = cityData[i];
		for (let j = 0; j < sheng.children.length; j++) {
			let shi = sheng.children[j];
			if (shi.value == cityNo) {
				// 使用return 终止循环
				return `${sheng.label}-${shi.label}`;
			}
		}
	}
}

/*
 * obj 转 params字符串参数
 * 例子：{a:1,b:2} => a=1&b=2
 */
function objParseParam(obj) {
	let paramsStr = "";
	if (obj instanceof Array) return paramsStr;
	if (!(obj instanceof Object)) return paramsStr;
	for (let key in obj) {
		paramsStr += `${key}=${obj[key]}&`;
	}
	return paramsStr.substring(0, paramsStr.length - 1);
}

/*
 * obj 转 路由地址带参数
 * 例子：{a:1,b:2} => /pages/index/index?a=1&b=2
 */
function objParseUrlAndParam(path, obj) {
	let url = path || "/";
	let paramsStr = "";
	if (obj instanceof Array) return url;
	if (!(obj instanceof Object)) return url;
	paramsStr = this.objParseParam(obj);
	paramsStr && (url += "?");
	url += paramsStr;
	return url;
}
/*
 * 获取url字符串参数
 */
function getRequestParameters(locationhref) {
	let href = locationhref || "";
	let theRequest = new Object();
	let str = href.split("?")[1];
	if (str != undefined) {
		let strs = str.split("&");
		for (let i = 0; i < strs.length; i++) {
			theRequest[strs[i].split("=")[0]] = (strs[i].split("=")[1]);
		}
	}
	return theRequest;
}

//获取页面路由以及参数
function getRouterParams() {
	const pages = getCurrentPages()
	let currentPage = pages[pages.length - 1]
	let currentRouter = {
		route: currentPage.route,
		params: currentPage.options
	}
	return currentRouter
}

/**
 * 小程序获取openid
 * @param {string} code 微信login获取接口
 */
function getOpenid(code) {

	return new Promise((resolve, reject) => {
		apis.userApis.GetUserInfoByCode({
				code: code
			}).then(res => {
				uni.setStorageSync('userUnionId', res.data.unionId)
				res.data.uid && setLocalStorage('uid', res.data.uid);
				setLocalStorage('userId', res.data.id);
				setLocalStorage('openid', res.data.openId);
				setLocalStorage('isPrefect', res.data.isPrefect);
				resolve(res.data.openId);
			})
			.catch(res => {
				// utils._util.showToast(res.msg)
				reject(res)
			})
	})

}
// 通过后台获取用户数据
function getRemoteUserInfo(comid) {
	return new Promise((resolve, reject) => {
		let params = {
			'mobile': uni.getStorageSync('mobile'),
			comid,
			'openid': uni.getStorageSync('openId')
		}
		apis.smartCommunityApis.getUserInfo(params).then(res => {
			console.log(res, 'userInfo')
			let userBuildingInfo = {}
			if (res.code === 200) {
				userBuildingInfo = res.data;

			}
			// 未选择小区 - 第一次进入
			else if (res.code === 203) {
				uni.removeStorageSync('bl_community_id');
				uni.removeStorageSync('bl_community_ladder_id');
				uni.removeStorageSync('bl_community_name');
				userBuildingInfo = {};
			} else {
				userBuildingInfo = {};
				uni.removeStorageSync('bl_community_id');
				uni.removeStorageSync('bl_community_ladder_id');
				uni.removeStorageSync('bl_community_name');
			}
			uni.setStorageSync('bl_user_info', userBuildingInfo);
			resolve(userBuildingInfo);
		}).catch(err => {
			let userBuildingInfo = {};
			uni.setStorageSync('bl_user_info', userBuildingInfo);
			reject(err);
		})
	})
}
/* 扫码后查询门禁二维码是梯控还是单车 */
function queryByDeviceSn(deviceSn, jumpType) {
	//jumpType=1 空白页调用，防止返回到空白页使用uni.reLaunch
	let data = {
		deviceSn,
		openId: uni.getStorageSync('openId'),
		phone: uni.getStorageSync('mobile'),
		appId: uni.getAccountInfoSync().miniProgram.appId,
		uid: uni.getStorageSync('uid')
	}
	return new Promise((resolve, reject) => {
		apis.chargingPile.queryByDeviceSn(data).then(async res => {
			if (res.code == 200) {
				let deviceSn = JSON.parse(res.data)
				console.log('deviceSn门禁信息',deviceSn)
				if (deviceSn.type == 2) {				
					deviceSn.deviceSn = data.deviceSn
					uni.setStorageSync('deviceSn', deviceSn)
					let url = ''
					let userData = await selectByUserId(deviceSn.powerStationId)
					console.log('userData',userData)
					//flag 1 没有用户信息 按照空白码临时车收费模式（1、预付费；2、后付费）跳转页面  flag 2有用户信息 有预付套餐先跳转 预付
					//temporaryCarPayModel 1、预付费；2、后付费  billType 2临时 1包月
					//deviceDirection 0进  1出  出场根据接口返回跳转

					if(deviceSn.bikeUserBillingInfoTbVo && deviceSn.bikeUserBillingInfoTbVo.billType==1 && userData.billType==1&&deviceSn.deviceDirection == 1){
						//包月出场 
						url=`/pagesI/bicycle/monthlyExit?powerStationId=${deviceSn.powerStationId}&userAreaEffective=${JSON.stringify(userData)}`
						
					} else if(deviceSn.deviceDirection == 0 && !userData.isExpire && userData.billType==1) {
						//包月入场 已到期
						url=`/pagesI/bicycle/monthlyAdmission?powerStationId=${deviceSn.powerStationId}&userAreaEffective=${JSON.stringify(userData)}`
						
					}else if(userData.isExpire&&userData.billType==1){
						//买了包月套餐有效期内87t
						 url=deviceSn.deviceDirection == 1?`/pagesI/bicycle/monthlyExit?powerStationId=${deviceSn.powerStationId}&userAreaEffective=${JSON.stringify(userData)}`:`/pagesI/bicycle/monthlyAdmission?powerStationId=${deviceSn.powerStationId}&userAreaEffective=${JSON.stringify(userData)}`
					}else if (!deviceSn.temporaryBilling) {
						//开始判断临时停车
						url = `/pagesI/bicycle/tip?deviceDirection=${deviceSn.deviceDirection}`
					} else if (deviceSn.deviceDirection == 1) {
						console.log('临时出场')
						url = deviceSn.temporaryCarPayModel == 1 ?
							`/pagesI/bicycle/temporaryPackage?powerStationId=${deviceSn.powerStationId}` :
							deviceSn.temporaryCarPayModel == 2 ?
							`/pagesI/bicycle/temporaryPostpaidAdmission?powerStationId=${deviceSn.powerStationId}` :
							''
						if (deviceSn.temporaryCarPayModel == 2 && !deviceSn.orderId) {
							wx.showToast({
								title: "未查到入场记录",
								icon: "none"
							});
							return
						}
						if (deviceSn.temporaryCarPayModel == 1 && (!userData.isExpire) && !deviceSn.orderId) {
							wx.showToast({
								title: "未查到入场记录",
								icon: "none"
							});
							return
						}
						console.log('临时出场22',url)

					} else if (userData.flag === 1) {
						url = deviceSn.temporaryCarPayModel == 1 ?
							`/pagesI/bicycle/temporaryPackage?powerStationId=${deviceSn.powerStationId}` :
							deviceSn.temporaryCarPayModel == 2 ?
							`/pagesI/bicycle/temporaryPostpaidAdmission?powerStationId=${deviceSn.powerStationId}` :
							''

					} else {
						url = (userData.effectiveTime && userData.isExpire && userData.billType ==
								2 && (userData.outTimes || userData.inTimes)) ?
							`/pagesI/bicycle/temporaryPackage?powerStationId=${deviceSn.powerStationId}` :
							deviceSn.temporaryCarPayModel == 1 ?
							`/pagesI/bicycle/temporaryPackage?powerStationId=${deviceSn.powerStationId}` :
							deviceSn.temporaryCarPayModel == 2 ?
							`/pagesI/bicycle/temporaryPostpaidAdmission?powerStationId=${deviceSn.powerStationId}` :
							''
					}
					if (jumpType === 1) {
						uni.reLaunch({
							url
						})
					} else {

						reNavigateTo(url)
					}

				} else if (deviceSn.type == 1) {
					if (jumpType === 1) {
						uni.reLaunch({
							url: `/pagesI/control/openDoorByQrcode?scene=${deviceSn}`
						})
					} else {
						reNavigateTo(`/pagesI/control/openDoorByQrcode?scene=${deviceSn}`)
					}


				} else {
					reject(res)
				}

			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
					duration: 2000,
				})
				reject(res)

			}
		}).catch(err => {
			console.log('空白码识别失败',err)
			uni.showToast({
				title: '空白码识别失败',
				icon: 'none',
				duration: 2000,
			})


		})
	})

}
/* 查询用户单车停车有效期 */
function selectByUserId(powerStationId,id) {
	console.log('查询有效期')
	return new Promise(async (resolve, reject) => {
		let user={}
		if(!id){
			user = await getChargeUserManager(powerStationId)
			if (!user) {
				resolve({
					flag: 1
				})
				return
			}
		}

		let data = {
			userId: user.id||id,
			powerStationId
		}
		apis.chargingPile.selectByUserId(data).then(res => {
			if (res.code == 200) {
				//billType 2临时 1包月
				let userAreaEffective = res.data[0]
				let time = parseInt(new Date().getTime() / 1000) + ''
				let isExpire = userAreaEffective.effectiveTime - time > 0 ? true : false
				let data = {
					...userAreaEffective,
					isExpire,
				}
				resolve({
					flag: 2,
					...data
				})

			} else {
				resolve({
					flag: 1
				})
				uni.showToast({
					title: res.message,
					icon: 'none',
					duration: 2000,
				})
			}
		}).catch(err => {
			resolve({
				flag: 1
			})
		})
	})

}
// 查询单车停车配置信息,用户信息必填项
function ParkingConfigInfo(powerStationId) {
	return new Promise(async (resolve, reject) => {
		let data = {
			powerStationId
		}
		apis.chargingPile.ParkingConfigInfo(data).then(res => {
			if (res.code == 200) {
				var returnData = res.data;
				if (returnData !== null) {
					resolve(returnData)
					return
				}
				resolve({})
			}

		}).catch(err => {
			resolve({})
		})
	})

}
/* 通过手机号和充电站查询人员 */
function getChargeUserManager(powerStationId) {
	return new Promise((resolve, reject) => {
		let data = {
			powerStationId,
			phone: uni.getStorageSync('mobile'),
			openId: uni.getStorageSync('openId')
		}
		apis.chargingPile.chargeUserManager(data).then(res => {
			if (res.code == 200) {
				if (res.data.powerStationTbId == powerStationId) {
					resolve(res.data)
				} else {
					resolve()
				}

			} else {
				resolve()
			}

		}).catch(err => {
			resolve()

		})
	})

}

function getUserInfo() {
	// #ifdef MP-WEIXIN || MP-ALIPAY
	uni.login({
		success(res) {
			getOpenid(res.code);
		}
	})
	// #endif
}


//消息订阅
function messageSubscription(tmplIds, payload) {
	if (!tmplIds) return;
	if (wx.requestSubscribeMessage) {
		wx.requestSubscribeMessage({
			tmplIds, // 模板id的数组
			success(res) {
				console.log("消息订阅success:", res);
			},
			fail(res) {
				console.log("消息订阅fail:", res);
			},
			complete(res) {
				payload && payload.callback();
			}
		})
	} else {
		// 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
		wx.showModal({
			title: '提示',
			content: '当前微信版本过低，无法使用消息提醒功能，请升级到最新微信版本后重试。',
			showCancel: false,
			success(res) {
				if (res.confirm) {
					payload && payload.callback()
					console.log('用户点击确定')
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
	}
}

//打开微信导航,显示当前位置
function navigation(option) {
	uni.openLocation({
		latitude: option.latitude,
		longitude: option.longitude,
		name: option.name || null,
		address: option.detailAddress || option.address || null,
		scale: option.scale || null,
		success: function() {
			console.log('打开地图success');
		},
		fail: function() {
			console.log('打开地图fail');
		}
	});
}

// 获取地理位置
function getLocation() {
	var _this = this
	return new Promise(function(resolve, reject) {
		uni.getLocation({
			success: function(t) {
				resolve(t)
			},
			fail: function(t) {
				console.log(t),
					reject(t)
			}
		})
	})
}

/**
 *  授权检测
 *  @param {string} task    接受授权功能
 */
function handleAuthorize(task) {
	const _this = this // 下边this 为undefined
	return new Promise((resolve, reject) => {
		uni.authorize({
			scope: `scope.${task}`, // 参数见文档[https://uniapp.dcloud.io/api/other/authorize]
			success(res) {
				// 用户信息
				// task == 'userInfo' && getUserInfo()
				// 地理位置
				// task == 'userLocation' && getLocation()
				// 通讯地址 address
				// 录音功能 record
				// 保存相册 writePhotosAlbum
				// 摄像头   camera
				resolve(1)
			},
			// 授权失败
			fail(err) {
				err = err['errMsg']
				uni.showModal({
						title: '温馨提示',
						content: '为享受智能小程序，您必须授权!',
						showCancel: true,
						confirmText: '确认授权'
					})
					// 这里只设置了确认按钮，没有取消按钮
					.then(res => {
						//res ： [null, {cancel: false, confirm: true, errMsg: 'showModal: ok'}]
						if (res[1]['confirm']) { // 点击了确认按钮时
							uni.openSetting({
								success(res) {
									console.log('小程序设置界面：', res)
									if (res.authSetting["scope.userInfo"]) {
										resolve(1)
										getUserInfo()
									}
									// res.authSetting = {
									//   "scope.userInfo": true,
									//   "scope.userLocation": true
									// }
									// resolve(1)
								}
							})
						} else {
							reject(0)
						}
					})
			}
		})
	})

}

// 页面toast交互提示
function showToast(title, icon = 'none') {
	setTimeout(() => {
		uni.showToast({
			title,
			icon,
			duration: 5000
		});
	}, 200)
}

// 根据key获取本地存储
function getLocalStorage(key) {
	try {
		return uni.getStorageSync(key);
	} catch (e) {
		//TODO handle the exception
		console.error('getStorageSync error')
	}
}

// 根据key删除本地存储
function removeLocalStorage(key) {
	try {
		uni.removeStorageSync(key);
	} catch (e) {
		//TODO handle the exception
		console.error('removeStorageSync error')
	}
}

// 通过key-value设置本地存储
function setLocalStorage(key, value) {
	try {
		uni.setStorageSync(key, value);
	} catch (e) {
		//TODO handle the exception
		console.error('setStorageSync error')
	}
}

/**
 * 处理边角小功能[复制文本、打电话、保存通讯录、跳转外部链接]
 * @param {string} url     路径:页面路径[跳转]、电话号码[拨打、复制、保存通讯录]、http链接[转义路径]、wx文章链接[分段]
 * @param {string} method  跳转方式 navigateTo[默认]
 */
function goUrl(url, method) {
	if (method = method || 'navigateTo', url) {
		if (-1 < url.indexOf('copy:')) {
			url = url.split(':')[1];
			/^\d{7,23}$/.test(url) ?
				uni.showActionSheet({
					itemList: ['呼叫', '复制', '添加到手机通讯录'],
					success: function(t) {
						0 == t.tapIndex ?
							(console.log('呼叫'),
								uni.makePhoneCall({
									phoneNumber: url
								})) :
							1 == t.tapIndex ?
							(console.log('复制'),
								uni.setClipboardData({
									data: url,
									success: function(t) {
										console.log(t),
											uni.getClipboardData({
												success: function(t) {
													console.log('复制文本成功 ==>>', t.data)
												}
											})
									}
								})) :
							2 == t.tapIndex &&
							(console.log('添加到手机通讯录'),
								uni.addPhoneContact({
									firstName: ' ',
									mobilePhoneNumber: url,
									success: function(t) {
										console.log('添加到手机通讯录 ==> ', t)
									},
									fail: function(t) {
										console.log('添加到手机通讯录fail ==> ', t)
									}
								}))
					}
				}) :
				(uni.setClipboardData({
					data: url,
					success: function(t) {
						uni.getClipboardData({
							success: function(t) {
								console.log('复制文本成功 ==>>', t.data)
							}
						})
					}
				}))
		} else {
			if (-1 < url.indexOf('tel:')) {
				uni.makePhoneCall({
					phoneNumber: url.split(':')[1]
				})
			} else {
				if (-1 < url.indexOf('http')) {
					return (url = encodeURIComponent(url)), undefined
				}

				if (0 == url.indexOf('wx')) {
					var r,
						a = '',
						i = 'release',
						s = url.split(':')
					return (
						1 == s.length ?
						(r = s[0]) :
						2 == s.length ?
						((r = s[0]), (a = s[1])) :
						3 == s.length && ((r = s[0]), (a = s[1]), (i = s[2])),
						undefined
					)
				}
				uni[method]({
					url: url
				})
			}
		}
	}
}
/**
 * 随即返回指定长度的字符串
 * @param {Object} len
 */
function randomString(len) {
	len = len || 32;
	var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
	var maxPos = $chars.length;
	var pwd = '';
	for (let i = 0; i < len; i++) {
		pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
	}
	return pwd;
}

/**
 * @desc 判断传入的值是正常值
 * @param v
 */
function isOkTheValue(v) {

	if (v === undefined || v === null || v === 'undefined' || v === null) {
		v = ""
	}
	return v
}

/**
 * underscore 节流函数，返回函数连续调用时，func 执行频率限定为 次 / wait
 *
 * @param  {function}   func      回调函数
 * @param  {number}     wait      表示时间窗口的间隔
 * @param  {object}     options   如果想忽略开始函数的的调用，传入{leading: false}。
 *                                如果想忽略结尾函数的调用，传入{trailing: false}
 *                                两者不能共存，否则函数不能执行
 * @return {function}             返回客户调用函数
 */
function throttle(func, wait, options) {
	var context, args, result;
	var timeout = null;
	// 之前的时间戳
	var previous = 0;
	// 如果 options 没传则设为空对象
	if (!options) options = {};
	// 定时器回调函数
	var later = function() {
		// 如果设置了 leading，就将 previous 设为 0
		// 用于下面函数的第一个 if 判断
		previous = options.leading === false ? 0 : _.now();
		// 置空一是为了防止内存泄漏，二是为了下面的定时器判断
		timeout = null;
		result = func.apply(context, args);
		if (!timeout) context = args = null;
	};
	return function() {
		// 获得当前时间戳
		var now = +new Date();
		// 首次进入前者肯定为 true
		// 如果需要第一次不执行函数
		// 就将上次时间戳设为当前的
		// 这样在接下来计算 remaining 的值时会大于0
		if (!previous && options.leading === false) previous = now;
		// 计算剩余时间
		var remaining = wait - (now - previous);
		context = this;
		args = arguments;
		// 如果当前调用已经大于上次调用时间 + wait
		// 或者用户手动调了时间
		// 如果设置了 trailing，只会进入这个条件
		// 如果没有设置 leading，那么第一次会进入这个条件
		// 还有一点，你可能会觉得开启了定时器那么应该不会进入这个 if 条件了
		// 其实还是会进入的，因为定时器的延时
		// 并不是准确的时间，很可能你设置了2秒
		// 但是他需要2.2秒才触发，这时候就会进入这个条件
		if (remaining <= 0 || remaining > wait) {
			// 如果存在定时器就清理掉否则会调用二次回调
			if (timeout) {
				clearTimeout(timeout);
				timeout = null;
			}
			previous = now;
			result = func.apply(context, args);
			if (!timeout) context = args = null;
		} else if (!timeout && options.trailing !== false) {
			// 判断是否设置了定时器和 trailing
			// 没有的话就开启一个定时器
			// 并且不能不能同时设置 leading 和 trailing
			timeout = setTimeout(later, remaining);
		}
		return result;
	};
};

function reqOpenid(code) {
	console.log('getOpenId1')
	return new Promise((resolve, reject) => {
		apis.homeApis.getOpenId({
			code: code
		}).then(res => {
			setLocalStorage('userUnionId', res.data.unionId)
			console.log('getOpenId2', res)
			res.data.userId && setLocalStorage('uid', res.data.userId)
			setLocalStorage('token', res.data.token)
			setLocalStorage('openId', res.data.openId)
			setLocalStorage('session_key', res.data.session_key)
			resolve(res)
		}).catch(res => {
			// showToast(res.msg)
			reject(res)
		})
	})


}
// 初始化小程序数据
function initData() {
	console.log('initData')
	let that = this;
	return new Promise((resolve, reject) => {
		// #ifdef MP-WEIXIN || MP-ALIPAY
		uni.login({
			success(res) {
				reqOpenid(res.code).then((res1) => {
					resolve(res1)
				});
			},
			fail(res) {
				reject()
			}
		})
		// #endif
		// #ifdef H5
		resolve(true)
		// #endif
	})
}
/**
 * @description 重组数据
 * @param {Object} data
 * @param {Object} key_name
 * @param {Object} key_no
 * @return [{value_name:'',value_no:'',rows:{}}]
 */
function assembleData(data, key_name, key_no) {
	let arr = [];
	if (data && data.length > 0) {
		data.map((item, index) => {
			if (item[key_name]) {
				arr.push({
					value_name: item[key_name],
					value_no: item[key_no],
					rows: item || {}
				})
			}

		})
	}
	return arr;
}

function generateUUID() {
	var s = [];
	var hexDigits = "0123456789abcdef";
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = "-";

	var uuid = s.join("");
	return uuid;
}
// 云平台token处理以及加密
function getCloudToken() {
	let that = this
	return new Promise(async (resolve, reject) => {
		let cloudToken = uni.getStorageSync('cloudToken')
		cloudToken = cloudToken ? JSON.parse(cloudToken) : null
		if (cloudToken) {
			resolve(cloudToken)
		} else {
			const openId = uni.getStorageSync('openId')
			if (openId) {
				apis.chargingPile.getCloudToken({
					openId
				}).then(res => {
					const data = res.data
					if (!data) reject('获取token失败！')
					let sign = res.data.substring(0, 16) + openId + res.data.substring(16, 32)
					sign = md5.md5(sign).toLowerCase()
					let cloudToken = {
						sign,
						token: res.data,
						openId
					}
					uni.setStorageSync('cloudToken', JSON.stringify(cloudToken))
					resolve(cloudToken)
				}).catch(err => {
					reject(err)
				})
			} else {
				await that.initData()
				that.getCloudToken()
			}
		}
	})
}
/**
 * @description 加密
 *
 */
function encryptionData(str) {
	return md5.md5(str).toLowerCase()
}
/**
 * @description 获取某年某月第一天和最后一天时间戳
 * @param year: 年份, month: 月份 
 *
 */
function getMonthDay(year, month) {
	let [ctime, etime] = ['', '']
	ctime = year + '/' + month + '/1'
	etime = month == 12 ? (Number(year) + 1).toString() + '/1/1' : year + '/' + (Number(month) + 1).toString() + '/1'
	ctime = Date.parse(new Date(ctime)) / 1000
	etime = Date.parse(new Date(etime)) / 1000 - 1
	return {
		ctime,
		etime
	}
}

function formatTime(date) {
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	return [year, month, day].map(formatNumber).join('-')
}

function formatNumber(n) {
	n = n.toString()
	return n[1] ? n : '0' + n
}

function reNavigateTo(url) {
	uni.navigateTo({
		url,
		fail: err => {
			console.log(err)
			uni.redirectTo({
				url,
			})
		},
	})
}

function supplyZelo(num, digit = 3) {
	if (!num && num != 0) {
		return ''
	}
	num = String(num);
	let l = digit - num.length;
	for (let i = 0; i < l; i++) {
		num = '0' + num;
	}
	return num
}

function getFaceDepartmentList(data) {
	return new Promise((resolve, reject) => {
		if (!data) {
			let bl_user_info = uni.getStorageSync('bl_user_info'),
				comid = uni.getStorageSync('bl_community_id'),
				projectId = uni.getStorageSync('bl_community_ladder_id'),
				createPeople = (bl_user_info && bl_user_info.userName) || '未知-小程序查询初始化'
			data = {
				comid,
				projectId,
				createPeople
			}
		}
		apis.smartCommunityApis.getFaceDepartmentList(data).then(res => {
			resolve(res.data)
		}).catch(res => {
			// showToast(res.msg)
			reject(res)
		})
	})


}
// 支付宝获取昵称，头像
function getAuthCode() {
	return new Promise((resolve, reject) => {
		let user = {}
		my.getAuthCode({
			scopes: 'auth_user',
			success: (res) => {
				my.getAuthUserInfo({
					success: (userInfo) => {
						user.nickname = userInfo.nickName
						user.headimgurl = userInfo.avatar
						uni.setStorageSync("pushManageUser", user)
						resolve(user)
					}
				})

			}
		})

	})


}


// 获取钱包余额
// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
function getWallet() {
	try {
		return new Promise((resolve, reject) => {
			let data = {}
			// #ifdef APP-PLUS
			data = {
				openId: uni.getStorageSync('openId'),
			}
			// #endif
			console.log(data);
			apis.homeApis.queryWallet(data).then((res) => {
				if (res.status == 200) {
					let available_amount = res.data.available_amount / 100 || 0
					let freeze_amount = res.data.freeze_amount / 100
					let total = available_amount + freeze_amount
					available_amount = available_amount ? available_amount.toFixed(2) : '0.00'
					freeze_amount = freeze_amount ? freeze_amount.toFixed(2) : '0.00'
					total = total ? total.toFixed(2) : '0.00'
					let result = {
						available_amount,
						freeze_amount,
						total
					}
					uni.setStorageSync('money', available_amount)
					resolve(result)
				} else {
					reject('获取钱包信息异常！')
				}
			}).catch(err => {
				console.log('cuouw', err)
			})
		})
	} catch (error) {
		console.log(error, '错误');
	}
}
// #endif
// 获取微信手机号组件配置
function getMicroConfig(){
	// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
	let params = {
		appid: wx.getAccountInfoSync().miniProgram.appId
	}	
	return new Promise((resolve, reject) => {
		apis.park.getMicroConfigPhone(params).then((res) => {
			console.log(res)
			if(Number(res.data) === 1){
				resolve({ mobile: 1})
			} else {
				resolve({ mobile: 0})
			}
		}).catch(err => {
			console.log('getMicroConfig===>', err)
			resolve({ mobile: 0})
		})
	})
	// #endif
}




// #ifdef H5
function getWallet() {
	try {
		let token = uni.getStorageSync('token')
		let phone = uni.getStorageSync('mobile')
		let wallet_id = uni.getStorageSync('walletId')
		let params = {
			token,
			phone,
			wallet_id,
			openId: phone,
			plat_id: BaseConfig.platId,
		}
		return new Promise((resolve, reject) => {
			apis.chargingPile.queryWallet(params).then((res) => {
				console.log(res)
				if (res.status == 200) {
					let available_amount = res.data.available_amount / 100 || 0
					let freeze_amount = res.data.freeze_amount / 100
					let total = available_amount + freeze_amount
					available_amount = available_amount ? available_amount.toFixed(2) : '0.00'
					freeze_amount = freeze_amount ? freeze_amount.toFixed(2) : '0.00'
					total = total ? total.toFixed(2) : '0.00'
					let result = {
						available_amount,
						freeze_amount,
						total
					}
					uni.setStorageSync('money', available_amount)
					resolve(result)
				} else {
					reject('获取钱包信息异常！')
				}

			}).catch(err => {
				console.log('cuouw', err)
			})
		})
	} catch (error) {
		console.log(error, '错误');
	}
}
// #endif

/* 获取充电桩信息 */
async function getPileMessage(flag = 0, queryType) {
	/**
	 * flag
	 * 0：不返回结果，抛异常，跳转
	 * 1：不返回结果，抛异常，不跳转
	 * 2: 只返回结果，不处理
	 * 3: 桩状态正常情况下返回结果，抛异常，页面不跳转
	 */
	// queryType.type=1查询枪信息加参数，queryType.type=2  从空白码页面进来，用uni.reLaunch 跳转，防止返回到空白页面
	const that = this
	const cloudToken = await this.getCloudToken()
	const param = {
		...cloudToken,
		code: uni.getStorageSync('gunNo'),
	}
	
	if(param.code == 'private'){
		uni.reLaunch({
			url: `/pagesJ/device/pile`
		})
		return;
	}
	queryType && queryType.type == 1 ? param.type = 1 : '' //输入枪编码页面，精确查询，加type:1参数
	return new Promise((resove, reject) => {
		apis.chargingPile.queryConnectorInfo(param).then((res) => {
			let errorMessage = ''
			if (res.code == 200) {
				let {
					status,
					userNum,
					chargeCode,
					orderId,
					sceneType,
					leverStatus,
					openId,
					connectorId,
					freeChargingSetup,
					qrCodeType,
					connectorInfoTbList,
					timeOut,
					minMoney,
					supportSmartCharging,
					smartChargingState
				} =
				res.data
				connectorId && uni.setStorageSync('gunNo', connectorId)
				chargeCode && uni.setStorageSync('pileCode', chargeCode)
				if (supportSmartCharging == 1) {
					if (smartChargingState == 1) {
						uni.reLaunch({
							url: '/pagesF/charge/index?errorMessage=其他人在使用'
						})
						return resove()
					} else if (status !== 2 && smartChargingState == 2) {
						uni.reLaunch({
							url: `/pagesF/charge/wait?type=2`
						})
						return resove()
					}
				}
				if (flag === 2) {
					return resove(res.data)
				}
				if (status == 2 && cloudToken.openId == userNum) {
					// 桩的使用者和当前用户一致
					orderId ? uni.setStorageSync('orderId', orderId) : ''
					if (queryType && queryType.type == 2) {
						uni.reLaunch({
							url: '/pagesF/charge/state'
						})
					} else {
						that.reNavigateTo('/pagesF/charge/state')
					}
					return resove()
				} else if (
					status == 1 ||
					status == 10 ||
					status == 0
				) {
					// 桩正常情况
					if (flag === 3) {
						return resove(res.data)
					}
					if (sceneType == 2 || sceneType == 3) {

						// if((leverStatus==0||leverStatus==1)&&(timeOut && cloudToken.openId !== openId)){
						// 	errorMessage="当前车位已有其他人在使用"
						// 	if(flag===0){
						// 		that.reNavigateTo('/pagesF/charge/index?errorMessage='+errorMessage);
						// 	}else{
						// 		uni.showToast({
						// 			title:errorMessage,
						// 			icon: 'none'
						// 		})
						// 	}
						// 	resove()
						// }else{
						// 	leverStatus==3 &&(leverStatus=0)
						// 	that.reNavigateTo(`/pagesF/charge/parkSelect?minMoney=${minMoney}&leverStatus=${leverStatus}&freeChargingSetup=${freeChargingSetup}&timeOut=${timeOut}`)
						// 	return resove()
						// }
						uni.reLaunch({
							url: `/pagesF/code/choose?sceneType=${sceneType}`
						})
					} else {
						// 根据码类型qrCodeType，1:桩码、2：桩码，扫码是桩跳转到Recharge页面，扫码是枪跳转到pileList页面
						if (qrCodeType === 1) {
							if (queryType && queryType.type == 2) {
								uni.reLaunch({
									url: '/pagesF/charge/Recharge'
								})
							} else {
								that.reNavigateTo('/pagesF/charge/Recharge')
							}

							return
						} else {
							uni.setStorageSync('connectorInfoTbList', connectorInfoTbList)
							if (queryType && queryType.type == 2) {
								uni.reLaunch({
									url: '/pagesF/charge/pileList'
								})
							} else {
								that.reNavigateTo('/pagesF/charge/pileList')
							}

							return
						}
					}
				} else {
					if (qrCodeType === 1) {
						// if(queryType&&queryType.type==2){
						// 	uni.reLaunch({
						// 		url:'/pagesF/charge/Recharge'
						// 	})
						// }else{
						// 	that.reNavigateTo('/pagesF/charge/Recharge')
						// }
						// return
					} else {
						// 如果是桩码则跳转到枪列表页面
						uni.setStorageSync('connectorInfoTbList', connectorInfoTbList)
						if (queryType && queryType.type == 2) {
							uni.reLaunch({
								url: '/pagesF/charge/pileList'
							})
						} else {
							that.reNavigateTo('/pagesF/charge/pileList')
						}
						return resove()
					}
					switch (status) {
						case 2:
							errorMessage = '充电桩其他用户在使用';
							break;
						case 3:
							errorMessage = '充电已停止请拔枪';
							break;
						case 4:
							errorMessage = '充电桩故障';
							break;
						case 5:
							errorMessage = '预约';
							break;
						case 6:
							errorMessage = '充电桩在维护';
							break;
						case 7:
							errorMessage = '启动中';
							break;
						case 8:
							errorMessage = '充电暂停';
							break;
						case 9:
							errorMessage = '定时启动状态';
							break;
						case 11:
							errorMessage = '定时启动状态';
							break;
						case 12:
							errorMessage = '启动失败';
							break;
						default:
							break;
					}
					if (!flag || flag === 0) {
						if (queryType && queryType.type == 2) {
							uni.reLaunch({
								url: '/pagesF/charge/index?errorMessage=' + errorMessage
							})
						} else {
							that.reNavigateTo('/pagesF/charge/index?errorMessage=' + errorMessage);
						}
					} else {
						uni.showToast({
							title: errorMessage,
							icon: 'none'
						})
					}
					return resove()
				}
			} else if (res.code == 4002) {
				return reject(res)
			} else if (res.code == 500) {
				uni.showToast({
					title: res.message,
					icon: 'none'
				})
				return resove()
			}
		})
	})
}
// 获取车场信息
function generateNolienceByUnionId(qr_code_id, qr_type) {
	const that = this
	return new Promise((resove, reject) => {
		apis.homeApis.generateNolienceByUnionId({
				qr_code_id: qr_code_id
			})
			.then(async res => {
				if (res.status == 200) {
					let dataInfo = {
						qr_code_id: qr_code_id,
						operate: 'query'
					}
					apis.homeApis.microcode(dataInfo).then(reset => {
						if (reset.status == 200) {
							const {
								qr_type,
								cappInCodeType,
								inParkNeedPrepay,
								in_channel_id,
								out_channel_id
							} = reset.data;
							if(cappInCodeType == 1){
								console.log('获取车场信息2',res,cappInCodeType)
								let parkInfo=JSON.stringify(res.data.parkInfo)
								console.log('inpark1111111------------inParkNeedPrepay',inParkNeedPrepay, 'in_channel_id', in_channel_id,parkInfo)
								if(inParkNeedPrepay == 1) {
									uni.reLaunch({
										url: '/pagesC/pay/inpark?qr_code_id=' + qr_code_id +
											'&qr_type=' + qr_type+'&parkInfo='+parkInfo+'&carNumber=' + res.data.licence + 
											'&inParkNeedPrepay=' + inParkNeedPrepay + '&inChannelId=' + in_channel_id
									})
									return 
								} else {
									uni.reLaunch({
										url: '/pagesC/pay/inpark?qr_code_id=' + qr_code_id +
											'&qr_type=' + qr_type+'&parkInfo='+parkInfo+'&carNumber=' + res.data.licence+'&in_channel_id='+in_channel_id
									})
									return 
								}
							}else {
								that.queryParkOrderDetail(res.data.licence, qr_code_id, qr_type).then(
									order => {
										console.log(order, 'order');
										// that.reNavigateTo('/pagesC/pay/orderdetail?carNumber='+res.data.licence+'&qr_code_id=' + qr_code_id +'&qr_type='+ qr_type)
										if (order.status === 200) {	
											uni.reLaunch({
												url: '/pagesC/pay/orderdetail?carNumber=' + res.data
													.licence + '&qr_code_id=' + qr_code_id +
													'&qr_type=' + qr_type +'&inParkNeedPrepay=' + inParkNeedPrepay + 
													'&inChannelId=' +  in_channel_id +'&out_channel_id='+out_channel_id+'&in_channel_id='+in_channel_id
											})
										}
										resove()
									}).catch(err => {
									// that.reNavigateTo('/pagesC/pay/inpark?qr_code_id=' + qr_code_id +'&qr_type=' + qr_type)
									let parkInfo=JSON.stringify(res.data.parkInfo)
									uni.reLaunch({
										url: '/pagesC/pay/inpark?qr_code_id=' + qr_code_id +
											'&qr_type=' + qr_type+'&parkInfo='+parkInfo+'&carNumber=' + res.data.licence
									})
								})
							}
						}
					}).catch(err => {
						console.log(err, '异常情况');
					})
					
				} else {
					let parkInfo=JSON.stringify(res.data.parkInfo)
					uni.reLaunch({
						url: '/pagesC/pay/inpark?qr_code_id=' + qr_code_id + '&qr_type=' +
							qr_type+'&parkInfo='+parkInfo+'&carNumber=' + res.data.licence
					})
				}
			})
	})
}
// 获取在场停车订单
function queryParkOrderDetail(plateNumber, qr_code_id, qr_type) {
	return new Promise((resove, reject) => {
		apis.homeApis.queryOrderDetail({
				plateNumber,
				qr_code_id,
				qr_type
			})
			.then(res => {
				if (res.status === 200) {
					resove(res)
				} else {
					reject(res)
				}
			})
	})
}
// 获取车位信息
function queryParklotByDeviceId() {
	return new Promise((resove, reject) => {
		let params = {
			num: uni.getStorageSync('pileCode'),
		}
		apis.park.getParkLotByNo(params).then(res => {
			if (res.status == 200) {
				resove({
					flag: 1
				})
			} else {
				resove({
					flag: 0
				})
			}
		}).catch(error => {
			resove({
				flag: 0
			})
		})
	})
}
// 获取是否存在待支付充电订单
function queryPayChargeOrder() {
	return new Promise((resove, reject) => {
		apis.chargingPile.getPaidOrder({
			openId: uni.getStorageSync('openId'),
		}).then(res => {
			let {
				data,
				code
			} = res
			if (code == 200) {
				if (data && data.length > 0) {
					return resove({
						flag: 1
					})
				} else {
					return resove({
						flag: 0
					})
				}
			}
		}).catch(err => {
			return resove({
				flag: 0
			})
		})
	})
}
//根据openid查询 用户手机号码
function getMobile() {
	return new Promise((resove, reject) => {
		apis.homeApis.getMobile({
			openId: uni.getStorageSync('openId'),
		}).then(res => {
			if (res.status == 200) {
				uni.setStorageSync("mobile", res.data)
				return resove({
					flag: 1,
					mobile: res.data
				})

			} else {
				return resove({
					flag: 0
				})
			}
		}).catch(err => {
			return resove({
				flag: 0
			})
		})
	})
}

async function dealQrcode(code) {
	const that = this
	await initData()
	console.log(code, 'dealQrcode')
	return new Promise(async (resove, reject) => {
		if (code && code.q) {
			code = code.q
		}

		let params = code && this.getRequestParameters(decodeURIComponent(code))
		// #ifdef MP-WEIXIN
		if(params.no){
			//跳转我的桩页面，不用调扫码接口，固定二维码
			myPile()
			return
		}
		// #endif
		console.log(params, code, params.id, '解析二维码')
		if (!params.id && params.p) {
			params.id = params.p
		}
		if (params.id && params.No) {		
			let codeInfo= params.No;
			if (codeInfo == '') {
				return reject('无效的二维码')
			} else {
				let pileCode = codeInfo.substring(0, codeInfo.length - 2)
				let gun ='0'+codeInfo.substring(codeInfo.length - 2, codeInfo.length)
				let url = '/pagesF/charge/index?scene=sn/' + pileCode + "*gun/" + gun
				this.reNavigateTo(url)
			}
		}else if (params.type == 'ticket') {
			// 固定码
			uni.reLaunch({
				url: `/pagesC/coupon/park?id=${params.id}&scanType=${params.scanType||0}&periodId=${params.tid||''}&codeId=${params.codeId||0}`
			})
		}else if(params.type == 'moveCar'){
			//挪车码
			uni.reLaunch({
				url: `/pagesC/removalCode/scanMoveCar?id=${params.id}`
			})
			return
		}else if(params.emergency_flag == '1'){
			//离线支付码
			uni.reLaunch({
				url: `/pagesC/park/offlinepay?orderInfo=${encodeURIComponent(JSON.stringify(params))}`
			})
			return
		}else if (params.id) {
			// let appletConfiguration = this.getAppletConfiguration();
			// let qrcodePrefix = appletConfiguration && appletConfiguration.qrcodePrefix ? appletConfiguration.qrcodePrefix : 'https://beta.bolink.club/payApi/t,https://s.bolink.club/payApi/t,https://s.bolink.club/p';
			// let currentQrcodePrefix = code.split('?')[0]
			// console.log(appletConfiguration,currentQrcodePrefix)
			// if(!qrcodePrefix.includes(currentQrcodePrefix)){
			// 	return reject('无效的二维码')
			// }
			// 空白码
			if (params.id.indexOf('/') != -1) {
				params.id = params.id.substring(0, params.id.indexOf('/'))
			}
			let data = {
				qr_code_id: params.id,
				operate: 'query'
			}
			// 获取二维码信息
			apis.homeApis.microcode(data).then(async res => {
				if (res.status == 200) {
					const {
						state,
						type,
						devCode,
						qr_type
					} = res.data
					if (qr_type && qr_type !== -1) {
						if (qr_type === 1) { //预付 
							// that.reNavigateTo('/pagesC/pay/editcarnumber?qr_code_id=' + params.id +'&qr_type=' + qr_type)
							uni.reLaunch({
								url: '/pagesC/pay/editcarnumber?qr_code_id=' +
									params.id + '&qr_type=' + qr_type
							})
						} else if (qr_type === 2) { //出场结算
							// that.reNavigateTo('/pagesC/pay/orderdetail?qr_code_id=' + params.id +'&qr_type=' + qr_type)
							uni.reLaunch({
								url: '/pagesC/pay/orderdetail?qr_code_id=' + params
									.id + '&qr_type=' + qr_type
							})
						} else if (qr_type === 3) { //无牌车入场
							// 获取车场信息
							that.generateNolienceByUnionId(params.id, qr_type)
						} else if (qr_type === 4) { //付款码
							that.reNavigateTo('/pagesC/pay/collectionCode?qr_code_id=' +
								params.id + '&qr_type=' + qr_type)
						} else {
							return reject('无效的二维码')
						}
					} else {
						// 扫码先判断是否有手机号码， 如果没有先获取，再继续下一步
						let mobile = uni.getStorageSync('mobile')
						let boo = true
						if (mobile && String(mobile).length === 11) {
							boo = false;
						} else {
							boo = true;
						}
						if (boo) {
							let res = await this.getMobile()
							if (res.flag === 0) {
								uni.setStorageSync('dealQrcode', code)
								let url =
									`/pagesA/agreementLogin/login?isEmpower=true&jumpType=5`
								this.reNavigateTo(url)
								return
							}
						}
						if (state) {
							if (devCode) {
								if (type === 0) {
									// 判断是否带有枪号，*代表带了枪号，如果没有枪编号则作为枪号
									if (devCode.includes("*")) {
										let tempArr = devCode.split('*')
										let tempObj = {}
										for (let i of tempArr) {
											let res = i.split('/')
											tempObj[res[0]] = res[1]
										}
										tempObj.sn ? params.sn = tempObj.sn : ''
										params.no = tempObj.gun ? tempObj.sn + tempObj.gun :
											tempObj.sn


									} else {
										params.sn = devCode
										params.no = devCode

									}
									uni.setStorageSync('pileCode', params.sn)
									uni.setStorageSync('gunNo', params.no)
									// 汽车充电桩扫码处理逻辑
									// 1，普通充电桩（标准场景）扫码后，不管有没有绑定车位，直接去 开始充电页面
									// 2，车位闸场景的充电桩，扫码后选择停车或者充电

									this.getPileMessage()
									/* 获取待支付订单，如果有待支付订单，需要先支付再进行其他操作 */
									// let openId = uni.getStorageSync('openId')
									// apis.chargingPile.getPaidOrder({
									// 	openId
									// }).then(res => {
									// 	let {
									// 		data,
									// 		code
									// 	} = res
									// 	if (code == 200) {
									// 		if (data && data.length > 0) {
									// 			this.reNavigateTo('/pagesF/order/payOrder?tarbar="pages/index/index"')
									// 			return resove()
									// 		} else {
									// 			return resove({ code:200})
									// 		}
									// 	}
									// })
									// let hasPayChargeOrder = await queryPayChargeOrder()
									// if(hasPayChargeOrder.flag==1){
									// 	this.reNavigateTo('/pagesF/order/payOrder?tarbar="pages/index/index"')
									// }else{
									// 	// 判断充电桩是否绑定车位
									// 	let hasParkLot = await queryParklotByDeviceId()
									// 	if(hasParkLot.flag==1){
									// 		uni.reLaunch({
									// 			url: '/pagesF/code/choose'
									// 		})
									// 	}else{
									// 		resove({code:200})
									// 	}
									// }
								} else if (type === 1) {
									// #ifdef H5
									return reject('无效的二维码')
									// #endif
									/* 单车判断是否带有枪号，*代表带了枪号，如果没有枪编号则作为枪号 */
									if (devCode.includes("*")) {
										let tempArr = devCode.split('*')
										let tempObj = {}
										for (let i of tempArr) {
											let res = i.split('/')
											tempObj[res[0]] = res[1]
										}
										tempObj.sn ? params.sn = tempObj.sn : ''
										params.no = tempObj.gun ? tempObj.sn + tempObj.gun :
											tempObj.sn
										uni.setStorageSync('socketCode', params.sn)
										uni.setStorageSync('socketNo', params.no)
										this.queryorder()

									} else {
										// 如果是桩码到选择枪页面
										this.reNavigateTo('/pagesH/equipment/jack?scene=' +
											devCode);

									}



									return resove()
								} else if (type === 2) {
									// #ifdef H5
									return reject('无效的二维码')
									// #endif
									// this.reNavigateTo('/pagesI/control/openDoorByQrcode?scene=' + devCode);
									await queryByDeviceSn(devCode).then((res) => {
										return false
									}).catch((err) => {

										console.log('通过设备序列号判断', err)
									})
									return resove()
								} else if (type === 5) {
									await getRemoteUserInfo(devCode)
									uni.setStorageSync("bl_community_ladder_id", params
										.projectId)
									uni.setStorageSync("bl_community_name", params
										.projectName)
									uni.setStorageSync("bl_community_id", devCode)
									uni.reLaunch({
										url: `/pagesI/visitors/facecaller?comid=${devCode}&params=${JSON.stringify(params)}`
									})
								} else if (type === 9) {
									uni.reLaunch({
										url: `/pagesB/map/chargeMessage?powerStationId=${devCode}`
									})
								} else if (type === 10) {
									let data = {
										id: devCode
									}
									console.log(devCode)
									apis.homeApis.faceProjectInfo(data).then(res => {
										console.log(res)
										if (res) {
											uni.redirectTo({
												url: `/pagesG/collect_user_info/base?comid=${res.comid}&id=${res.id}&projectName=${res.projectName}`
											});
										}
									})
								} else {
									return reject('无效的二维码')
								}
							} else {
								return reject('无效的二维码');
							}
						} else {
							let url = '/pagesF/code/emptyCode?qr_code_id=' + params.id +
								'&type=' + type
							this.reNavigateTo(url);
							return resove()
						}
					}
				} else {
					return reject('无效的二维码')
				}

			})
		} else if (params.sn && params.no) {
			// 粤电
			let pileCode = params.scene || params.sn
			if (pileCode == '') {
				return reject('无效的二维码')
			} else {
				let url = '/pagesF/charge/index?scene=sn/' + params.sn + "*gun/" + params.no
				this.reNavigateTo(url)
			}
		} else if (params.chargingNo || params.sn) {
			// 停碳科技
			let queryType = {
				type: 1
			}
			let pileCode = params.chargingNo || params.sn
			// let hasGun = gunNo ? null : 2
			uni.setStorageSync('gunNo', pileCode)
			this.getPileMessage(0, queryType).then().catch(err => {
				uni.showToast({
					title: err,
					icon: 'none'
				})
			})
		} else if (code.path) {
			// 小程序码
			let url = code.path[0] == '/' ? code.path : '/' + code.path
			this.reNavigateTo(url)
		} else if (code.result && code.result.indexOf('hlht') !== -1) {
			// 智泊惠
			let tempResult = code.result.substring(7, code.result.length)
			let tempArr = tempResult.split('.')
			tempArr[0] && (tempResult = tempArr[0])
			let url = '/pagesF/charge/index?scene=' + tempResult
			this.reNavigateTo(url)
		} else {
			return reject('无效的二维码')
		}
	})
}
// 通过桩编码查电站id
function queryByChargeCode(pileCode) {
	try {
		return new Promise((resolve, reject) => {
			let params = {
				chargeCode: pileCode,
			}
			apis.chargingPile.queryByChargeCode(params).then((res) => {
				if (res.code == 200) {
					let powerStationId = res.data.powerStationId
					resolve(powerStationId)
				} else {
					resolve(false)

				}
			}).catch(err => {
				console.log('查电站', err)

			})

		})

	} catch (error) {
		console.log(error, '错误');
	}

}
// 保存用户昵称
function setUserInfo(params) {
	let user = {
		headimgurl: params.avatarUrl ? params.avatarUrl : '',
		nickname: params.nickName ? params.nickName : '',
	}
	uni.setStorageSync("pushManageUser", user)
	return new Promise((resolve, reject) => {
		apis.homeApis.setUserInfo(params).then(res => {
			if (res.status == 200) {
				resolve({
					flag: true
				})
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				resolve({
					flag: false
				})
			}
		}).catch(err => {
			resolve({
				flag: false
			})
		})
	})
}
// 查询用户昵称
function getUserInfoNickname(params) {
	return new Promise((resolve, reject) => {
		apis.homeApis.getUserInfoNickname().then(res => {
			console.log('获取用户昵称getUserInfoNickname', res)
			if (res.status == 200) {
				let data = res.data
				let user = {
					headimgurl: data.avatarUrl ? data.avatarUrl : '',
					nickname: data.nickName ? data.nickName : '',
				}
				uni.setStorageSync("pushManageUser", user)
				resolve({
					flag: true,
					user
				})
			} else {
				resolve({
					flag: false
				})
			}
		}).catch(err => {
			resolve({
				flag: false
			})
		})
	})
}
// 判断用户需不需要登录
function queryBikeUser(id) {
	try {
		return new Promise((resolve, reject) => {
			let openId = uni.getStorageSync('openId')
			let params = {
				openId
			}
			apis.chargingPile.queryBikeUser(params).then((res) => {
				if (res.code == 200) {
					let data = {
						phone: res.data.phone,
						openId: openId
					}
					resolve(data)

				} else {
					uni.setStorageSync('stationId', id)
					resolve('2')
				}

			}).catch(err => {

			})


		})

	} catch (error) {
		console.log(error, '错误');
	}
}
// 添加用户
function chargeUserManagerAdd(phone, powerStationTbId) {
	try {
		return new Promise((resolve, reject) => {
			let openId = uni.getStorageSync('openId')
			if (!powerStationTbId) {
				powerStationTbId = uni.getStorageSync('stationId')
			}

			let data = {
				openId,
				powerStationTbId,
				phone
			}
			apis.chargingPile.chargeUserManagerAdd(data).then(res => {
				if (res.code == 200) {
					resolve(true)

				} else {
					resolve(false)
				}
			}).catch(err => {

			})


		})

	} catch (error) {
		console.log(error, '错误');
	}
}

/**
 * @describe 获取设备指令ID
 */
function getCommand(params) {
	return new Promise((resolve, reject) => {
		apis.smartCommunityApis.scanQrCodeDoor(params)
			.then(async res => {
				if (res.data.code == 200) {
					await this.recursionFetch(res.data.data.id).then(result => {
						console.log(result, '指令结果返回成功')
						resolve(result.value)
					}).catch(err => {
						console.log(err, '指令结果返回失败')
						reject('操作失败！')
					})
				} else {
					reject('操作失败！')
				}
			})
			.catch((err) => {
				return Promise.reject(err);
			});
	})
}
/**
 * @describe 根据指令循环查结果
 */
function recursionFetch(id, time = 0) {
	console.log('recuser', id, time)
	return new Promise((resolve, reject) => {
		this.getReslut(id, time)
			.then((res) => {
				// 满足条件
				if (res.time > 13) {
					reject('操作失败！')
				} else {
					// 不满足条件
					if (res.value.orderStatus === 1) {
						return resolve({
							done: false,
							value: res
						});
					} else if (res.value.orderStatus === 2) {
						return resolve({
							done: true,
							value: res.value
						})
					} else {
						return reject('操作失败！');
					}
				}
			})
			.catch((err) => {
				reject(err);
			});
	}).then((res) => {
		// 满足条件，返回
		if (res.done) {
			return Promise.resolve(res.value);
		} else {
			// 不满足条件，进入递归
			return this.recursionFetch(id, res.value.time);
		}
	}).catch((err) => {
		return Promise.reject(err);
	});
}
/**
 * @describe 根据指令查结果
 */
function getReslut(diiId, time = 0) {
	console.log('指令查结果', diiId, time)
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			apis.chargingPile.getOrderResult({
				diiId
			}).then(res => {
				console.log(res.data)
				time += 1
				resolve({
					time,
					value: res.data
				})
			}).catch(err => {
				resolve({
					time: 15
				});

			})
		}, 2000);
	})
}
/**
 * @describe 跳转一码小程序
 */

function navitagetoMicro(path) {
	uni.removeStorageSync('appInfo')
	plus.share.getServices(function(res) {
		var sweixin = null;
		for (var i = 0; i < res.length; i++) {
			var t = res[i];
			if (t.id == 'weixin') {
				sweixin = t;
			}
		}
		console.log(sweixin)
		if (sweixin) {
			let env = process.env.VUE_APP_MOD || process.env.NODE_ENV
			sweixin.launchMiniProgram({
				id: 'gh_9be1fa0ea1f2', //这里写你的小程序原始id（以gh开头）
				type: env == 'development' ? 1 : 0, //这里是不同的环境  0是正式环境  1测试版 2是体验版本
				path: path //这里是指定页的路径，如需传参直接字符串拼接（首页可以省略）
			}).catch(err => {
				console.log(err, '异常处理')
			});
		}
	}, res => {
		console.log(JSON.stringify(res));
	});
}

function afterPay() {
	return new Promise((resolve, reject) => {
		let appInfo = uni.getStorageSync('appInfo');
		(typeof appInfo == "string") && (appInfo = JSON.parse(appInfo));
		console.log(appInfo, typeof appInfo, appInfo.isPay)
		if (appInfo) {
			uni.removeStorageSync('appInfo')
			if (appInfo.isPay === 1) {
				if (appInfo.callbackUrl) {
					uni.setStorageSync('rechargeAmount', appInfo.amount / 100)
					this.reNavigateTo(appInfo.callbackUrl)
					resolve()
				}
				if (app.func) {
					resolve(app.func, app.params)
				}
			} else {
				reject()
				uni.showToast({
					title: "已取消支付",
					icon: 'none'
				})
			}
		}
	}).catch(err => {
		console.log('支付异常信息！', err)
	})
}

function modify(title, content) {
	uni.showModal({
		title: title,
		content: content,
		confirmText: '前往开启',
		success: function(res) {
			if (res.confirm) {
				power.gotoAppPermissionSetting(); //动态修改权限
			}
		}
	});
}
// 单车订单查询接口
async function queryorder() {
	const that = this
	const cloudToken = await this.getCloudToken()
	if (!cloudToken.token) return
	let params = {
		...cloudToken,
		state: 0,
		page: 1,
		rp: 1,
		type: 2,
	}
	return new Promise((resove, reject) => {
		apis.chargingPile.queryorder(params).then((res) => {
			console.log('单车充电', res, res.code == 200)
			if (res.code == 200) {
				if (res.data.list && res.data.list[0]) {
					uni.showToast({
						title: '已存在在充订单，无法启动充电！',
						icon: 'none',
						duration: 2000,
					})
					return resove({
						flag: 1
					})
				} else {
					console.log('res.code0000')
					// 单车开始充电
					this.reNavigateTo('/pagesH/order/openOrder');
					return resove({
						flag: 0
					})
				}
			} else if (res.code === 4002) {
				uni.setStorageSync('cloudToken', '')
				setTimeout(() => {
					that.queryorder()
				}, 1000)

			} else {
				uni.showToast({
					title: '网络错误，请稍后再试！',
					icon: 'none',
					duration: 2000,
				})
			}
			return resove({
				flag: 0
			})
		}).catch(err => {
			uni.showToast({
				title: '网络错误，请稍后再试！',
				icon: 'none',
				duration: 2000,
			})
			return resove({
				flag: 0
			})
		})

	})

}
async function myPile(){
	// 扫码先判断是否有手机号码， 如果没有先获取，再继续下一步
	console.log('mypile')
	let mobile = uni.getStorageSync('mobile')
	let boo = true
	if (mobile && String(mobile).length === 11) {
		boo = false;
	} else {
		boo = true;
	}
	let url=`/pagesJ/device/pile`
	if (boo) {
		let res = await getMobile()		
		if (res.flag === 0) {
			url = `/pagesA/agreementLogin/login?isEmpower=true&jumpType=3&jumpUrl=/pagesJ/device/pile`
			reNavigateTo(url)
			return
		}
		reNavigateTo(url)
	}else{
		reNavigateTo(url)
	}
	
}
function getAppletConfiguration() {
	const accountInfo = wx.getAccountInfoSync();
	let appId = accountInfo.miniProgram.appId;
	return authority[appId];
}

//  秒数转化为时分秒
function formatSeconds(value) {
	//  秒
	let second = parseInt(value)
	//  分
	let minute = 0
	//  小时
	let hour = 0
	//  天
	//  let day = 0
	//  如果秒数大于60，将秒数转换成整数
	if (second > 60) {
		//  获取分钟，除以60取整数，得到整数分钟
		minute = parseInt(second / 60)
		//  获取秒数，秒数取佘，得到整数秒数
		second = parseInt(second % 60)
		//  如果分钟大于60，将分钟转换成小时
		if (minute > 60) {
			//  获取小时，获取分钟除以60，得到整数小时
			hour = parseInt(minute / 60)
			//  获取小时后取佘的分，获取分钟除以60取佘的分
			minute = parseInt(minute % 60)
			//  如果小时大于24，将小时转换成天
			//  if (hour > 23) {
			//    //  获取天数，获取小时除以24，得到整天数
			//    day = parseInt(hour / 24)
			//    //  获取天数后取余的小时，获取小时除以24取余的小时
			//    hour = parseInt(hour % 24)
			//  }
		}
	}

	let result = {
		second,
		minute,
		hour
	}
	return result
}

function getUnionId() {
	// #ifdef MP-ALIPAY
	apis.homeApis.getUnionId({
		openId: uni.getStorageSync('openId')
	}).then(res => {
		console.log(res)
		setLocalStorage('userUnionId', res.data)
	}).catch(res => {
		// showToast(res.msg)
	})
	// #endif
}
// 微信静默授权测试
function parkH5Login(appid,paysence,type){
	// let result = "http://localhost:8080/?code=051Ziq0w39b5y13XlE0w3Aq3n42Ziq0s&state=STATE#/pagesB/map/chargeMap"
	let { origin, pathname, hash } = location
	console.log( origin, pathname, hash, '微信静默授权测试',appid,BaseConfig.parkAppid)
	if(appid==BaseConfig.parkAppid){
		if(hash.indexOf('?') === -1){
			hash += '?isLogin=1'
		}else{
			hash += '&isLogin=1'
		}
	}else{
		if(hash.indexOf('?') === -1){
			hash += '?isLogin=2'
		}else if(hash.indexOf('isLogin=2')!==-1){
			return
		}else{
			hash += '&isLogin=2'
		}
	}

	let url = `${origin}${pathname}${hash}&appid=${appid}`
	console.log('parkH5Login',url)
	let wxurl = ''
	if(type == 3){
		wxurl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(
			url
		)}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
	}else{
		wxurl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(
			url
		)}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`
	}
	let zfburl=`https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=${appid}&scope=auth_base&redirect_uri=${encodeURIComponent(
		url
	)}`
	let source=localStorage.getItem('h5_source')
	console.log(source)
	if(source=='wx'){
		window.location.href = wxurl
	}else{
		window.location.href = zfburl
	}

}
// 查询h5停车appid相关信息
function getPayH5Appid(paysence,type = 0){
	let parkInfo=uni.getStorageSync('parkInfo')
	let source=localStorage.getItem('h5_source')
	return new Promise((resove, reject) => {
		apis.park.getPayH5Appid({
			union_id:parkInfo.union_id,
			park_id:parkInfo.park_id,
			source:source==='wx'?'wx':'ali',
			paysence,//0预付 1直付
		}).then(res => {
			if(res.state==1){
				parkInfo.isLs=res.isLs
				parkInfo.appid=res.appid||''
				parkInfo.domain=res.domain?(`${res.domain}t?p=${uni.getStorageSync('qr_code_id')}&from_new_h5=1`):''
				parkInfo.paysence=paysence
				uni.setStorageSync('parkInfo',parkInfo)
					// 获取之前授权的openId和appid
				let authorizedOpenId = null;
				let authorizedAppid = null;
				let authorizedWxUnionId = null;
				let authorizedInfo = uni.getStorageSync('authorizedInfo')
				console.log(authorizedInfo, 'authorizedInfo');
				if (authorizedInfo) {
					authorizedOpenId = authorizedInfo.openId;
					authorizedAppid = authorizedInfo.appId;
					authorizedWxUnionId = authorizedInfo.wxUnionId;
				}
				if (authorizedOpenId && authorizedAppid && authorizedOpenId === uni.getStorageSync('payUserId') && authorizedAppid === parkInfo.appid) {
					// 授权信息一致，不需要再次授权
					console.log('不需要再次授权');
					uni.setStorageSync('payUserId', authorizedOpenId)
					apis.homeApis.getH5OpenId({openId: authorizedOpenId,unionId: authorizedWxUnionId || '',appId:parkInfo.appid}).then(res => {
						if (res.status === 200) {
							let authorizedInfo = {
								token: res.data.token,
								appId: parkInfo.appid,
								wxUnionId: authorizedWxUnionId,
								openId: authorizedOpenId
							}
							console.log('authorizedInfo===>new', authorizedInfo)
							if(authorizedInfo){
								uni.setStorageSync('authorizedInfo', authorizedInfo)
							}
							uni.setStorageSync('wxUnionId', authorizedWxUnionId)
							uni.setStorageSync('token', res.data.token)
							uni.setStorageSync('openId', authorizedOpenId)
						}else{
								
						}
					}).catch(res => {
						console.log('h5-getOpenId===>', res)
					})
				} else {
					// 授权信息不一致，需要再次授权
					console.log('需要再次授权');
					// 这里可以添加授权逻辑，例如跳转到授权页面
					if(res.appid){
						parkH5Login(res.appid,paysence,type)
					}
				}
			}else{
				uni.showToast({
					title: res.errmsg,
					icon: 'none',
					duration: 2000,
				})
			}
			resove(res)
		
		}).catch(res => {
			resove()
		})
	})
}
export default {
	//年月日
	formatDate,

	getCityName,
	//例子：{a:1,b:2} => a=1&b=2
	objParseParam,
	//{a:1,b:2} => /pages/index/index?a=1&b=2
	objParseUrlAndParam,
	//获取url字符串参数
	getRequestParameters,
	//获取页面路由以及参数
	getRouterParams,
	//消息订阅
	messageSubscription,
	//打开微信导航,显示当前位置
	navigation,
	// 获取地址位置
	getLocation,
	//showToast 提示信息
	showToast,
	//getLocalStorage 根据key获取storage
	getLocalStorage,
	//removeLocalStorage 根据key删除storage
	removeLocalStorage,
	//setLocalStorage 根据key value设置storage
	setLocalStorage,
	// 检测授权
	handleAuthorize,
	// 处理边角小功能[复制文本、打电话、保存通讯录、跳转外部链接]
	goUrl,
	// 返回指定程度的随机字符串
	randomString,
	isOkTheValue,
	initData,
	getOpenid,
	// 重组数据
	assembleData,
	//格式化成秒值 时间戳
	formatTimestamp,
	generateUUID,
	getCloudToken,
	getMonthDay,
	formatTime,
	reNavigateTo, // 重写跳转
	supplyZelo, // 补0操作
	getFaceDepartmentList,
	encryptionData,
	getWallet,
	dealQrcode,
	getPileMessage,
	queryByDeviceSn,
	queryByChargeCode, //通过桩编码查电站id
	queryBikeUser, //判断用户需不需要登录
	chargeUserManagerAdd, //添加用户
	getCommand, //获取设备指令ID
	recursionFetch, //根据指令循环查结果
	getReslut, //根据指令查结果
	navitagetoMicro, // app跳转小程序支付
	afterPay, //app支付完成操作
	getAuthCode, //支付宝获取用户昵称、头像
	generateNolienceByUnionId, // 获取车场信息
	queryParkOrderDetail, // 获取在场停车订单
	modify, // 权限
	queryParklotByDeviceId, // 获取充电桩绑定的车位信息
	queryPayChargeOrder, // 获取待支付充电订单
	queryorder, //单车充电中订单
	getAppletConfiguration, // 获取小程序配置
	formatSeconds, // 秒转成时间
	getMobile, //根据用户openid查询手机号码
	setUserInfo, //保存用户昵称
	getUserInfoNickname, //查询用户昵称
	getUnionId, // 获取unionid
	selectByUserId, //查询单车停车用户有效期套餐
	ParkingConfigInfo, //查询单车停车配置信息,用户信息必填项
	getRemoteUserInfo, //查询用户车场id,项目id
	getMicroConfig, // 获取微信手机号组件配置
	myPile,//判断有没手机号码，跳转我的桩页面
	parkH5Login,  // 测试
	getPayH5Appid,//获取停车h5公众号appid
}