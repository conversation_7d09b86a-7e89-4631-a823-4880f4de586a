{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../packages/plugins/painter/index.ts"], "names": [], "mappings": "AAkHA,MAAM,MAAM,GAAqB;IAC/B,IAAI,EAAE,gBAAgB;IACtB,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;QACd,EAAE,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE;YACtB,EAAE,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;YAC9B,EAAE,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;YAChC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/C,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;oBAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAA;oBAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;oBACzB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;wBACxB,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;wBAClD,GAAG,CAAC,SAAS,GAAG,UAAU,IAAI,SAAS,CAAA;wBACvC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,CAAA;qBACzD;oBACD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;wBACzB,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE;4BAC/B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,OAAO;4BACpC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC;4BACxD,iBAAiB,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;4BAC9B,aAAa,EAAE;gCACb,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,MAAM,EAAE,IAAI,CAAC,MAAM;6BACpB;4BACD,MAAM,EAAE,IAAI,CAAC,MAAM;yBACpB,CAAC,CAAA;qBACH;oBACD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;wBACxB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,CAAA;wBACvC,GAAG,CAAC,IAAI,GAAG;cACT,IAAI,CAAC,SAAS,IAAI,QAAQ;cAC1B,IAAI,CAAC,UAAU,IAAI,QAAQ;cAC3B,IAAI,CAAC,QAAQ,IAAI,EAAE;cACnB,IAAI,CAAC,UAAU,IAAI,QAAQ;aAC5B,CAAA;wBACD,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;qBAClD;oBACD,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;wBAClC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,CAAA;wBACvC,GAAG,CAAC,IAAI,GAAG;cACT,IAAI,CAAC,SAAS,IAAI,QAAQ;cAC1B,IAAI,CAAC,UAAU,IAAI,QAAQ;cAC3B,IAAI,CAAC,QAAQ,IAAI,EAAE;cACnB,IAAI,CAAC,UAAU,IAAI,QAAQ;aAC5B,CAAA;wBACD,GAAG,CAAC,YAAY,CAAC;4BACf,CAAC,EAAE,IAAI,CAAC,IAAI;4BACZ,CAAC,EAAE,IAAI,CAAC,GAAG;4BACX,KAAK,EAAE,IAAI,CAAC,SAAS;4BACrB,UAAU,EAAE,IAAI,CAAC,UAAU;4BAC3B,QAAQ,EAAE,IAAI,CAAC,KAAK;4BACpB,OAAO,EAAE,IAAI,CAAC,OAAO;yBACtB,CAAC,CAAA;qBACH;oBACD,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;wBAC3B,IAAI,OAAO,GAAG,CAAC,UAAU,KAAK,UAAU,EAAE;4BACxC,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;4BACpD,OAAM;yBACP;wBACD,GAAG,CAAC,UAAU,CAAC;4BACb,CAAC,EAAE,IAAI;4BACP,CAAC,EAAE,GAAG;4BACN,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,IAAI,EAAE,IAAI,CAAC,OAAO;4BAClB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;4BACxB,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,SAAS;4BAClD,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,SAAS;yBACnD,CAAC,CAAA;qBACH;iBACF;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;IACH,CAAC;CACF,CAAA;AAED,eAAe,GAAG,EAAE,CAAC,MAAM,CAAA"}