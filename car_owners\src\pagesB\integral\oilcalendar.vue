<template>
	<view class="oil-container">
		<view
			class="custom-back-nav"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}"
		>
			<view class="flex-row alc">
				<!-- #ifdef MP-WEIXIN -->
				<van-icon @click="goBack" name="arrow-left" color="#fff" size="36rpx" />
				<!-- #endif -->
				<!-- #ifdef MP-ALIPAY -->
				<van-icon @click="goBack" style="width: 18px;" color="#fff" size="36rpx" />
				<!-- #endif -->
				<view @click="goBack" style="margin-left: 4rpx;">油价日历</view>
			</view>
		</view>
		<view class="oil-header">
			<image src="https://image.bolink.club/yima/oil_top_bg.jpg" mode="" class="oil-to-bg"></image>
			<view class="oil-header-title">
				<text>今日油价</text>
			</view>
			<view class="oil-header-date">
				下次调价日预计在：<text>{{nextUpdateTime}}</text>
			</view>
		</view>
		<view class="calendar-container" >
			<swiper class="swiper" 
					indicator-dots="true" 
					:current="index"
					interval="5000" 
					duration="1500"	style="height: 645rpx;margin-left: 34rpx;margin-top: 60rpx;margin-right: 34rpx;">
					<swiper-item v-for="(item , index) in homeSlide" :key="index"  >
						<image :src="item.img" style="height: 654rpx;width:100%;border-radius: 30px;" ></image>
					</swiper-item>
				</swiper>
		</view>
	</view>   
</template>

<script>
	import BaseConfig from '../../common/config/index.config.js';
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	export default{
		data(){
			return{
				windowHeight: 0,
				statusBarHeight: 0,
				navHeight: 0,
				nextUpdateTime:'',
				index:1,
				homeSlide:[
						{img:'https://image.bolink.club/oilPriceJanuary.png'},
						{img:'https://image.bolink.club/oilPriceFebruary.png'},
						{img:'https://image.bolink.club/oilPriceMarch.png'},
						{img:'https://image.bolink.club/oilPriceApril.png'},	
						{img:'https://image.bolink.club/oilPriceMay.png'},	
						{img:'https://image.bolink.club/oilPriceJune.png'},	
						{img:'https://image.bolink.club/oilPriceJuly.png'},	
						{img:'https://image.bolink.club/oilPriceAugust.png'},	
						{img:'https://image.bolink.club/oilPriceSeptember.png'},	
						{img:'https://image.bolink.club/oilPriceOctober.png'},	
						{img:'https://image.bolink.club/oilPriceNovember.png'},	
						{img:'https://image.bolink.club/oilPriceDecember.png'},	
						],
			}
		},
		onLoad(){
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
			this.oiltime();
			this.images();
		},
		methods:{
			oiltime(){
				 apis.homeApis.oiltime().then((res) => {
					 this.nextUpdateTime= res.data.nextUpdateTime;//油价时间更新
				 }) 
			},
			goBack () {
				uni.navigateBack({
				    delta: 1
				});
			},
			images(){
				console.log('index',this.index)
				this.index= new Date().toISOString().slice(5, 7).replace('0','')-1;
			}	
		}
	}
</script>

<style style="less">
	.oil-container {
		position: relative;
		background-color: #fbfbfb;
		width: 100%;
		min-height: 100%;
	}
	.oil-header {
		position: relative;
		height: 556rpx;
	}
	.oil-to-bg {
		position: absolute;
		top: 0;left: 0;
		margin: 0;padding: 0;
		height: 100%;width: 100%;
	}
	.oil-header-title {
		position: absolute;
		top: 290rpx;
		right: 63rpx;
		height: 70rpx;
		font-weight: 700;
		font-size: 70rpx;
		color: #fff;
	}
	.oil-header-date {
		position: absolute;
		top: 385rpx;
		right: 63rpx;
		font-weight: 500;
		font-size: 22rpx;
		color: #fff;
	}
	.calendar-container {
		position: absolute;
		top: 442rpx;
		left: 0;
		right: 0;
	}
</style>
