<template>
	<view class="pay-container">
		<custom-nav-bar title="支付"></custom-nav-bar>
		<view class="btn">
			<button class="bl-button bl-button--default marb-40" @click="goBack">返回首页</button>
			<button class="bl-button bl-button--primary" hover-class="bl-button--primary--hover" type="info" open-type="launchApp" :app-parameter="JSON.stringify(form)" @error="launchAppError">返回APP</button>
		</view>
	</view>
</template>
<script>
	import apis from '../../common/apis/index'
	import util from "../../common/utils/util.js"
	export default {
		components: {},
		data() {
			return {
				form: {},
				params: {}
			}
		},
		onLoad(options) {
			console.log(options,'app信息')
			if(options.params){
				this.params = JSON.parse(decodeURIComponent(options.params))
			}
			this.form = {}
			if(options.backApp){
				// 返回app的参数
				let appParam = JSON.parse(decodeURIComponent(options.backApp))
				this.form = {
					isPay: false,
					...appParam,
					...this.params
				}
				console.log('app返回参数：',this.form)
			}
			this.toPay(this.params)
		},
		onShow(options){
			
		},
		onReady() {
			
		},
		methods: {
			goBack(){
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			changeActive(index){
				
			},
			launchAppError(){
				uni.showToast({icon:'none', title:e.detail.errMsg})
			},
			toPay(params){
				const that = this
				that.$set(that.form,'isPay',0)
				apis.homeApis.toRecharge(params).then((res) => {
					if (res.status != 200) {
						uni.showToast({
							title: '网络请求异常',
							icon: 'none',
							duration: 2000,
						})
						that.disabled = false
						return
					}
					let payParams = res.data && JSON.parse(res.data) || {}
					uni.requestPayment({
						nonceStr: payParams.nonceStr,
						package: payParams.package,
						paySign: payParams.paySign,
						signType: payParams.signType,
						timeStamp: payParams.timeStamp,
						success: function(res) {
							console.log(res)
							if (res.errMsg == "requestPayment:ok") {
								that.disabled = false
								setTimeout(() => {
									uni.showToast({
										title: '支付成功',
										icon: 'none'
									});
									that.$set(that.form,'isPay',1)
								}, 100)
							} else {
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								});
							}
						},
						fail: function(err) {
							that.disabled = false
							console.log(err)
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							});
							//错误提示
						},
					})
				}).catch(err => {
					console.log(err)
				})
				
			}
			
			
		}
	}
</script>
<style lang="less" scoped>
	.pay-container{
		.btn{
			position: fixed;
			width: 670rpx;
			bottom: 200rpx;
			margin: 0 40rpx;
			text-align: center;
			color: #FFF;
		}
	}
</style>
