<view class='checkbox-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-checkbox {{customClass}}'>
    <view class='van-checkbox__icon-wrap' onTap='antmoveAction' data-antmove-tap='toggle'>
      <slot a:if='{{ useIconSlot }}' name='icon'>
      </slot>
      <van-icon a:else  name='success' size='0.8em' class="{{ utils.bem('checkbox__icon', [shape, { disabled: disabled || parentDisabled, checked: value }]) }}" style='{{ computed.iconStyle(checkedColor, value, disabled, parentDisabled, iconSize) }}' custom-class='{{iconClass}}' custom-style='line-height: 1.25em;' ref='saveChildRef1'>
      </van-icon>
    </view>
    <view class="{{labelClass}} {{ utils.bem('checkbox__label', [labelPosition, { disabled: disabled || parentDisabled }]) }}" onTap='antmoveAction' data-antmove-tap='onClickLabel'>
      <slot>
      </slot>
    </view>
  </view>
</view>