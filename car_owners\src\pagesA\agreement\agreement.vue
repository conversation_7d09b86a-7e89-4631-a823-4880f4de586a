<template>
	<view class="agreement-container">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<custom-nav-bar title="账户设置"></custom-nav-bar>
		<!-- #endif -->
		<view class="agreement">
			<view class="list">
				<!-- #ifndef APP-PLUS -->
				<view class="user-info">
					<view class="user-card border">
						<view>头像</view>
						<button class="user-img-container" @click="uniGetUserProfile" v-if="mobile">
							<image :src="headImg" class="head-image"></image>
							<image src="http://image.bolink.club/FiF9V0s6yNbYXPhtlrNmB8hMmiZu" class="refresh-img">
							</image>
						</button>
						<button class="user-img-container" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
							v-else>
							<image :src="headImg" class="head-image"></image>
							<image src="http://image.bolink.club/FiF9V0s6yNbYXPhtlrNmB8hMmiZu" class="refresh-img">
							</image>
						</button>
					</view>
					<view class="user-card border">
						<view>昵称</view>
						<button class="user-img-container" @click="uniGetUserProfile" v-if="mobile">
							<view>{{ nickname }}</view>
							<image src="http://image.bolink.club/FiF9V0s6yNbYXPhtlrNmB8hMmiZu" class="refresh-img">
							</image>
						</button>
						<button class="user-img-container" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
							v-else>
							<view>{{ nickname }}</view>
							<image src="http://image.bolink.club/FiF9V0s6yNbYXPhtlrNmB8hMmiZu" class="refresh-img">
							</image>
						</button>
					</view>
					<view class="user-card">
						<view>手机号</view>
						<view style="position: relative;">
							<button class="user-img-container" @click="goLogin">
								<view style="color: #f5655d" v-if="!encryptionMobile">*</view>
								<view class="phone">
									{{
									encryptionMobile ? encryptionMobile : tip
								}}
								</view>
								<image :src="arrow" class="refresh-img"></image>
							</button>
							<text class="empower-tip" v-if="!encryptionMobile">请授权手机号登陆</text>
						</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="item" @click="toDetail(0)">
					<view class="title">用户协议</view>
					<image :src="arrow" class="refresh-img"></image>
				</view>
				<view class="item" @click="toDetail(1)">
					<view class="title">隐私政策</view>
					<image :src="arrow" class="refresh-img"></image>
				</view>
				<button v-if="envVersion!=='release'" class="logout" @click="logout"  hover-class="none">
					退出登录
				</button>
			</view>
		</view>
		<view class="copyright font-26" @click="toPay">
			版本：{{version}}
		</view>
		<userProfile :showPopup="showPopup" @onClose="onClose" ref="userProfile"></userProfile>
	</view>
</template>

<script>
	import apis from "../../common/apis/index"
	import userProfile from "../../components/user-profile/user-profile.vue"
	import util from '../../common/utils/util'
	import BaseConfig from '../../common/config/index.config'
	let app = getApp();
	export default {
		components: {
			userProfile
		},
		data() {
			return {
				mobile: uni.getStorageSync("mobile"),
				defaultAvatarUrl: '',
				headImg: "",
				nickname: "",
				encryptionMobile: "",
				tip: '',
				showPopup: false,
				version: '',
				envVersion:''//develop开发版，trial体验版，release正式版
			}
		},
		onLoad(options) {
			let option = uni.getStorageSync("agreementUser")
			if (option.nickname) {
				this.headImg = option.avatarUrl
				this.nickname =
					option.nickname? option.nickname:"用户昵称"

				this.encryptionMobile =
					option.encryptionMobile === "undefined" || !option.encryptionMobile ?
					"" :
					option.encryptionMobile
				this.defaultAvatarUrl = option.defaultAvatarUrl
			}

			// #ifdef MP-WEIXIN
			this.tip = '绑定微信手机号'
			//#endif 
			// #ifdef MP-ALIPAY
			this.tip = '绑定支付宝手机号'
			//#endif 
			const miniProgram = wx.getAccountInfoSync();
			this.version = miniProgram.miniProgram.version || BaseConfig.copyright;
			// #ifdef MP-WEIXIN
			this.envVersion=__wxConfig.envVersion
			// #endif
		
		// #ifdef MP-ALIPAY
		let envVersion = my.getAccountInfoSync()
		this.envVersion=envVersion.miniProgram.envVersion
		// #endif
		console.log('版本',this.envVersion)

		},
		onShow(){
			this.mobile=uni.getStorageSync("mobile")
			if(this.mobile){
				this.encryptionMobile = this.encryption(this.mobile)
			}else{
				this.encryptionMobile=''
			}
		},
		methods: {
			toPay(){
				uni.requestPayment({
					// timeStamp: "**********",
					// nonceStr: "229a68f6bc4743628d562eff3c3974ee",
					// package: "prepay_id=wx13181640405779c4d541648cd7c5c50000",
					// signType: "RSA",
					// paySign:  "yRBQVqkUwNcJ8Au43I+78RjZoFRWkmsfqqErL6+YzAunNpQA9uFfvm0HSRzBe550GpCTvJPAiOpiPGOF1l9/+xiRiH885ZhvLOPShijkQ+L+gUrrlf6pGDlKLz/3rVjJCIXA63rAa/LMdeMO/i9hOp4G/2uzmH5QAkQXcYvyxPwAlvqvuK98ULhmIhITdj8vv6HbtOGe4Ze4rTu+Hs1atc+uRmWkO+lpcFOAAXL5nWT383fTMMtSTmGDPTtdXVU6nSWi+43HUpHbaU6K/sQ3vqVJmEez91WoktanoFnPJ7K5kNIvXxfkk6uVxVpGYhN+aQVItRdwOgbwce5jxaZWYg==",
					orderInfo: "2023071422001423521453140194",
					success: (res) => {
						
					},
					fail: (res) => {
						
					}
				})
			},
			goLogin(){
				if(!this.encryptionMobile){
					uni.navigateTo({
						url:'/pagesA/agreementLogin/login?isEmpower=true&jumpType=4'
					})
				}else{
					uni.navigateTo({
						url:`/pagesA/agreementLogin/updatePhone?encryptionMobile=${this.encryptionMobile}`
					})
				}				
			},
			async onClose(num) {
				this.showPopup = num
				let res=await util.getUserInfoNickname()
				if(res.flag){
					let data=res.user
					this.headImg = data.headimgurl
					this.nickname = data.nickname
				}
			},			
			async uniGetUserProfile() {
				// #ifdef MP-WEIXIN
				if (this.headImg) {
					this.$refs.userProfile.avatarUrl = this.headImg
				}
				this.$refs.userProfile.nickname = this.nickname&&this.nickname!=='用户昵称' ? this.nickname : ''
				this.$refs.userProfile.error = false
				this.showPopup = true
				//#endif 


				// #ifdef MP-ALIPAY
				let userInfo = await util.getAuthCode()
				this.headImg = userInfo.headimgurl
				this.nickname = userInfo.nickname
				let params={
					nickName:userInfo.nickname,
					avatarUrl:userInfo.headimgurl
				}

				util.setUserInfo(params)
				//#endif 
			},			
			// 加密手机号
			encryption(value) {
				if (!value) return
				var reg = /^(\d{3})\d{4}(\d{4})$/
				return value.replace(reg, "$1****$2")
			},
			hintModel(text) {
				uni.showModal({
					title: '',
					showCancel: false,
					confirmText: '好的',
					content: text || '暂无此功能，敬请期待',
					success: function(res) {
			
					}
				});
			},
			logout() {
				uni.showModal({
					title: "提示",
					content: "是否退出登录",
					success:(res) => {
						console.log(res)
						if (res.confirm) {
							// #ifdef APP-PLUS
							getApp().logOut()
							return
							// #endif
							apis.homeApis.logOut().then(async (res) => {
								uni.removeStorageSync("mobile")
								uni.removeStorageSync("token")
								uni.removeStorageSync("pushManageUser")
								this.headImg = this.defaultAvatarUrl
								this.nickname = "用户昵称"
								uni.reLaunch({
									url: `../../pages/user/index`,
								})

							})
						}
					},
				})
			},
			toDetail(num) {
				let url =
					num == 0 ? "/pagesA/agreement/user" : "/pagesA/agreement/privacy"
				let startTime = new Date().getTime()
				uni.navigateTo({
					url: url,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: `协议列表`,
							clickType: "Button",
							jumpType: "本程序页面",
							jumpDesc: "查看协议明细",
							result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
							startTime: startTime,
							endTime: new Date().getTime(),
						})
					},
				})
			},
		},
	}
</script>

<style lang="less" scoped>
	page {
		background-color: #f0f2f5;
	}

	.agreement-container {
		width: 100%;
		background-color: #f0f2f5;
		//#ifdef MP-WEIXIN || APP-PLUS
		margin-top: 180rpx;
		// #endif

	}

	.logout {
		display: block;
		width: 100%;
		height: 120rpx;
		margin-top: 40rpx;
		font-weight: bold;
		line-height: 120rpx;
		background: #ffffff;
		border-radius: 20px;
		text-align: center !important;
		box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.04);
	}

	.agreement {
		width: 100%;
	}

	.list {
		margin: 28rpx;
	}

	.item {
		padding: 0 5%;
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 120rpx;
		line-height: 120rpx;
		background: #ffffff;
		border-radius: 20px;
		box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.04);
	}

	.title {
		font-size: 28rpx;
		font-family: PingFang-SC-Medium, PingFang-SC;
		font-weight: 500;
		color: #242e3e;
	}

	.user-info {
		padding: 0 5%;
		height: 360rpx;
		width: 100%;
		background: #ffffff;
		border-radius: 20px;
		box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.04);
		font-size: 28rpx;
		font-weight: 500;
		color: #121212;

		.user-card {
			height: 120rpx;
			line-height: 120rpx;
			display: flex;
			justify-content: space-between;

			.user-img-container {
				display: flex;
				height: 120rpx;
				align-items: center;

				.phone {
					color: #b0b6c1;
					font-size: 28rpx;
				}

				.head-image {
					width: 56rpx;
					height: 56rpx;
					border-radius: 30rpx;
				}

				.refresh-img {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.empower-tip {
				position: absolute;
				right: 46rpx;
				bottom: 8rpx;
				font-size: 28rpx;
				color: #f5655d;
				height: 25rpx;
				line-height: 25rpx;
			}
		}

		.border {
			border-bottom: 1rpx solid #f6f6f6;
		}
	}

	.refresh-img {
		width: 60rpx;
		height: 60rpx;
	}

	.copyright {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
	}
</style>