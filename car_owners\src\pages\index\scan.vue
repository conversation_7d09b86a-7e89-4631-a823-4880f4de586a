<template>
	<view>
		<view class="nav-item" @click="handleScanQr">
			<view class="nav-item__img scan-code-icon"></view>
			<view class="nav-item__text">扫一扫</view>
		</view>
	</view>
</template>
<script>
import power from '../../common/utils/power.js';
import util from '../../common/utils/util.js';
export default {

	data() {
		return {

		}
	},

	methods: {

		// 打开扫码功能
		async handleScanQr() {
			const that = this
			// #ifdef APP-PLUS
			// 获取权限定位信息
			let resp = await power.requestAndroidPermission('android.permission.CAMERA');
			if (resp == -1) {
				return this.modify();
			}
			// #endif
			// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
			uni.scanCode({
				success: (res) => {
					console.log(res)
					util.dealQrcode(res.result || res).then(result => {
						if (result && result.code == 200) {
							this.getPileMessage()
						}
					}).catch(err => {
						uni.showToast({
							title: err,
							duration: 3000,
							icon: 'none'
						})
						uni.hideLoading();
					})
				}
			})
			// #endif
			// #ifdef H5
			uni.chooseImage({
				sizeType: ['original'],
				count: 1,
				success(res) {
					uni.showLoading({
						title: '加载中......'
					})
					const tempFilePaths = res.tempFilePaths[0]
					qrcode.decode(tempFilePaths)
					qrcode.callback = code => {
						if (code == "error decoding QR Code") {
							uni.showToast({
								title: "无效的二维码，请重新上传!",
								duration: 2000,
								icon: 'none'
							})
							uni.hideLoading();
						} else {
							util.dealQrcode(code).then(result => {
								if (result && result.code == 200) {
									that.getPileMessage()
								}
							}).catch(err => {
								uni.showToast({
									title: err,
									duration: 3000,
									icon: 'none'
								})
								uni.hideLoading();
							})
						}
					}
				},
				fail: (err) => {

				}
			});
			// #endif
		},

		/* 获取充电桩信息 */
		getPileMessage() {
			util.getPileMessage().then().catch(err => {
				if (err && err.code == 4002) {
					uni.removeStorageSync('cloudToken')
					this.getPileMessage()
				} else {
					uni.showToast({
						title: err,
						icon: 'none',
						duration: 3000
					});
				}
			})
		},

		// // 授权提示
		modify(type) {
			let title = "请打开位置权限，否则功能将无法正常使用";
			(type == 2) && (title = "请打开相机权限，否则功能将无法正常使用")
			uni.showModal({
				title: title,
				content: this.modify_content,
				confirmText: '前往开启',
				success: function (res) {
					if (res.confirm) {
						power.gotoAppPermissionSetting(); //动态修改权限
					}
				}
			});
		},

	}
}
</script>
<style lang="less"></style>
