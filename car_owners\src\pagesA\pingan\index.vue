<template>
	<view class="pingan">
		<view style="position: relative;">
			<image style="width: 100%;height: 862rpx" src="https://image.bolink.club/yima/ping_an_bg.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
		</view>
		
		<view class="main-cotain">
			<view style="position: relative;padding: 8rpx;">
				<image style="width: 100%;height: 711rpx" src="https://image.bolink.club/yima/ping_an_bg.jpg"></image>
				
				<view class="content-box">
					<view class="content-container">
						<view class="flex-row juc">
							<view class="coupon-item flex-col flex-1 ajc">
								<view class="coupon-item-img">
									<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
									<view class="coupon-item-name">停车券</view>
									<view class="coupon-item-price flex-row ale">
										<view class="coupon-item-price-d">￥</view>
										<view class="coupon-item-price-num">3</view>
										<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
									</view>
									<view class="coupon-item-price-desc">满3.01元可用</view>
								</view>
								<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
							</view>
							<view class="coupon-item flex-col flex-1 ajc">
								<view class="coupon-item-img">
									<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 172rpx;height: 243rpx;"></image>
									<view class="coupon-item-name-c">停车券</view>
									<view class="coupon-item-price-c flex-row ale">
										<view class="coupon-item-price-d">￥</view>
										<view class="coupon-item-price-num">5</view>
										<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
									</view>
									<view class="coupon-item-price-desc-c">满5.01元可用</view>
								</view>
								<image src="../static/pingan_new/coupon-arr2.png" style="width: 55rpx;height: 56rpx;"></image>
							</view>
							<view class="coupon-item flex-col flex-1 ajc">
								<view class="coupon-item-img">
									<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
									<view class="coupon-item-name">停车券</view>
									<view class="coupon-item-price flex-row ale">
										<view class="coupon-item-price-d">￥</view>
										<view class="coupon-item-price-num">2</view>
										<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
									</view>
									<view class="coupon-item-price-desc">满2.01元可用</view>
								</view>
								<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
							</view>
						</view>
						<view class="mart-10">
							<view class="flex-row pingan-input-par-home">
								<input 
									style="width: 100%;"
									placeholder="请输入真实姓名"
									placeholder-style="color: #999"
									class="pingan-input-home"
									type="text" 
									maxlength="20" 
									v-model.trim="name"
								 />
							</view>
							<view class="flex-row pingan-input-par-home">
								<input 
									style="width: 100%;"
									placeholder="请输入身份证号"
									placeholder-style="color: #999"
									class="pingan-input-home"
									maxlength="18" 
									type="idcard" 
									v-model.trim="idcard" 
								/>
							</view>
						</view>
					</view>
				</view>
				
				<view class="content-box-btn">
					<view style="position: relative;">
						<image src="https://image.bolink.club/yima/ping_an_btn.png" style="width: 340rpx;height: 105rpx;"></image>
						<van-button
							@click="btnClick" 
							:loading="loading"
							loading-text="加载中..."
							block 
							size="small"
						>立即抢购</van-button>
					</view>
				</view>
			</view>
			
			<view class="pingan-desc">
				<view class="pingan-desc-title">活动说明:</view>
				<view class="pingan-desc-content">
					<view>1、本活动为一码YiMa与平安银行联合针对平安银行新户专享活动； </view>
					<view>2、购买停车券用户需符合平安银行新户开户的相关要求； </view>
					<view>3、停车券可在一码YiMa指定停车场支付停车费时使用，停车费需大于停车券面额； </view>
					<view>4、查询停车券可微信搜索一码YiMa小程序查询；</view>
					<view>5、本活动最终解释权归一码YiMa与平安银行；</view>
				</view>
			</view>
			
			<view class="flex-row alc juc mart-30 padb-30">
				<image src="../static/pingan_new/pingan.png" style="width: 142rpx;height: 55rpx;"></image>
				<view class="content-box-bor"></view>
				<image src="../static/pingan_new/chezhu.png" style="width: 142rpx;height: 55rpx;"></image>
			</view>
		</view>
	
		
		<van-dialog id="van-dialog" />
		
		<van-dialog
		  use-slot
		  :show="showDialog"
		  :show-confirm-button="false"
		  @close="showDialog=false"
		>
		  <view class="pad-30">
			  <view class="flex-row juc mart-20">
				  <image src="../static/pingan_new/notice.png" style="width: 300rpx;height: 300rpx;"></image>
			  </view>
			  <view  style="color: #333;font-size: 32rpx;text-align: center;" class="mart-30 marb-30">您未符合此次活动资格</view>
			  <view class="my-btn">
				  <van-button
				  	@click="showDialog=false" 
				  	block 
				  	round 
				  	color="#F15A24"
				  >关闭</van-button>
			  </view>
		  </view>
		</van-dialog>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	
	export default {
		data() {
			return {
				name: '', // 姓名
				idcard: '', // 身份证号
				thirdId: '', // 查询资格时返回的id
				loading: false,
				showDialog: false,
			}
		},
		// 转发到朋友
		onShareAppMessage: function (res) {
		    return {
		      title: '3元购10元停车券 立即可用！',
		      path: `/pagesA/pingan/index`
		    }
		},
		// 分享到朋友圈
		onShareTimeline: function () {
			return {
			  title: '平安银行新户专享 3元购10元停车券 立即可用！',
			  // imageUrl: this.item.picUrl + '?imageView2/0/w/300',
			  query: `/pagesA/pingan/index`
			}
		},
		onLoad () {
		},
		methods: {
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			btnClick () {
				if (!this.name) {
					uni.showToast({
						title: '请先输入姓名',
						icon: "none"
					})
					return;
				}
				if (!this.idcard) {
					uni.showToast({
						title: '请先输入身份证号',
						icon: "none"
					})
					return;
				}
				this.loading = true;
				let params = {
					customerName: this.name,
					cardId: this.idcard,
				}
				apis.homeApis.isnewuser(params).then((res) => {
					this.loading = false;
					console.log(res);
					if (res.status === 200 && res.data.isNewUser === 'Y') {
						uni.showToast({
							title: '操作成功',
							icon: "success"
						})
						this.thirdId = res.data.thirdId;
						uni.navigateTo({
							url: `/pagesA/pingan/open?name=${this.name}&idcard=${this.idcard}&thirdId=${this.thirdId}`,
						})
					} else {
						this.showDialog = true;
					}
				}).catch(() => {
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		height: 1880rpx;
		background-color: #DE572D;
		/* background: linear-gradient(to bottom, #FD6630, #FD6630, #FD6630, #FD6630, #F95B23, #F65015, #F24507); */
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 580rpx;
	}
	
	.pingan-desc {
		color: #fcd7b2;
		padding: 32rpx;
		margin-top: 30rpx;
		font-size: 24rpx;
	}
	.pingan-desc-title {
		font-size: 28rpx;
		margin-bottom: 20rpx;
	}
	.pingan-desc-content {
		line-height: 150%;
	}
</style>
