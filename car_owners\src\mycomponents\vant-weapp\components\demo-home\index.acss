.demo-home-index {
    display: block;
    height: initial;
}
.demo-home-index .demo-home {
    box-sizing: border-box;
    width: 100%;
    min-height: 100vh;
    padding: 46px 20px 20px;
    background: #fff;
}

.demo-home-index .demo-home__title,
.demo-home-index .demo-home__desc {
    padding-left: 16px;
    font-weight: normal;
    line-height: 1;
    user-select: none;
}

.demo-home-index .demo-home__title {
    margin: 0 0 16px;
    font-size: 32px;
}

.demo-home-index .demo-home__image,
.demo-home-index .demo-home__text {
    display: inline-block;
    vertical-align: middle;
}

.demo-home-index .demo-home__image {
    width: 32px;
    height: 32px;
}

.demo-home-index .demo-home__text {
    margin-left: 16px;
    font-weight: 500;
}

.demo-home-index .demo-home__title .demo-home--small {
    font-size: 24px;
}

.demo-home-index .demo-home__desc {
    margin: 0 0 40px;
    color: rgba(69, 90, 100, 0.6);
    font-size: 14px;
}
