<view class='tabbar-item-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{ utils.bem('tabbar-item', { active }) }} {{customClass}}" style='color: {{ active ? activeColor : inactiveColor }}' onTap='antmoveAction' data-antmove-tap='onClick'>
    <view class='van-tabbar-item__icon'>
      <van-icon a:if='{{ icon }}' name='{{ icon }}' class-prefix='{{ iconPrefix }}' custom-class='van-tabbar-item__icon__inner' ref='saveChildRef1'>
      </van-icon>
      <block a:else >
        <slot a:if='{{ active }}' name='icon-active'>
        </slot>
        <slot a:else  name='icon'>
        </slot>
      </block>
      <van-info dot='{{ dot }}' info='{{ info }}' custom-class='van-tabbar-item__info' ref='saveChildRef2'>
      </van-info>
    </view>
    <view class='van-tabbar-item__text'>
      <slot>
      </slot>
    </view>
  </view>
</view>