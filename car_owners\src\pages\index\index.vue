<template>
	<view class="front-page-container">
		<scroll-view :scroll-y="activeName !== 'talkCar'" @scroll="startScroll"  refresherrefresh="true" @scrolltolower="scrolltolower" class="front-page-container">
			<view class="custom-back-nav front-page-top"
				:style="{ top: statusBarHeight + 'px', height: navHeight + 'px', 'line-height': navHeight + 'px' }">
				<view class="top-location flex-row alc" @click="chooseCity" :style="{'height':menuButtonHeight + 'px'}">
					<image src="http://image.bolink.club/FmPdgoj7o0zl4teTWZ5bJRPpT1ne" class="location_icon" :style="{'border-radius':(menuButtonHeight/2) + 'px'}"></image>
					<view v-if="!userLocation" class="location-tip">暂无定位,默认 </view>
					<view v-else class="marr-10">{{ strAreaName }}</view>
					<view class="front_page_arrow"></view>
				</view>
				<view class="news-container" :style="{ height: navHeight + 'px' }" @click="goNews">
					<!-- <view class="news-count">6</view> -->
					<image src="http://image.bolink.club/FjFa_P9hlbvApRWhMJPOmoJg9mjF" class="location_news" :style="{'height':menuButtonHeight + 'px','width':menuButtonHeight + 'px'}"></image>
				</view>


			</view>
			<view :class="backgroundImageName"  :style="{ height: navHeight + statusBarHeight + 120 + 'px' }" >
				<view class="advertising-language-container">
					<view class="advertising-language"></view>
				</view>
			</view>
			<view class="page-nav-content">
				<view class="nav-title">
					<view :class="activeName === 'charge'? 'nav-item nav-item-active':'nav-item'" @click="handleNavClick('charge')">
						<view class="nav-item__text">充电</view>
					</view>
					<view  :class="activeName === 'parking'? 'nav-item nav-item-active':'nav-item'" v-if="!appletConfiguration.hiddenCommunity" @click="handleNavClick('parking')">
						<view  class="nav-item__text parking_text">停车</view>
					</view>
					<view class="nav-item " v-else></view>
					<!-- #ifdef MP-WEIXIN || APP-PLUS  -->
					<view :class="activeName === 'control'? 'nav-item nav-item-active':'nav-item'" v-if="!appletConfiguration.hiddenPark" @click="handleNavClick('control')">
						<view class="nav-item__text">社区</view>
					</view>
					<view class="nav-item " v-else></view>
					<!-- #endif -->
					<view :class="activeName === 'talkCar'? 'nav-item nav-item-active':'nav-item'" v-if="!appletConfiguration.hiddenPark" @click="handleNavClick('talkCar')">
						<view class="nav-item__text">说车</view>
					</view>
					<view class="nav-item " v-else></view>
					
				</view>

				<!-- 充电 -->
				<view class="charge-page-container" v-show="activeName === 'charge'">
					<view class="search">
						<view v-if="activeName !== 'control'" @click="clickSearch(activeName)" class="search-container">
							<van-icon size="30rpx" name="http://image.bolink.club/FhI1Sn1b1nDfFriwFDxE2dOmg2J7" class="searchIcon" />
							<input class="input" disabled placeholder-style="font-size: 26rpx;font-weight: 400;color: #828EA6;"
								:placeholder="activeName == 'charge' ? '搜索目的地或电站' : '请输入目的地'" />
		
						</view>
		<!-- 				<button v-if="!mobile" style="padding: 20px;position: absolute;top: 0;width: 100%;z-index: 999;"
							 @click="getPhoneNumber"></button> -->
							<view class="search-text" @click="clickSearch(activeName)" >搜索</view>
					</view>
					<view>
					<chargePage :isAllowShow='isAllowShow' :appletConfiguration='appletConfiguration' ref="chargePage" :longitude='longitude' :latitude='latitude'
						:hasChargingOrder="hasChargingOrder" :isScrollTop="isScrollTop" :navHeight="navHeight"
						:statusBarHeight="statusBarHeight" :areaName="areaName" :positionAreaName="positionAreaName"
						:areaCode="areaCode" :mobile="mobile">
					</chargePage>
					<view class="pile-code" @click="handleGunCode">
						<van-icon name="http://image.bolink.club/FloehCbrreVK4hqNvHzzoQPoGS-x" size="64rpx" />
						<view style="font-size:18rpx">枪编码</view>
					</view>
				</view>

				</view>
				<!-- 停车 -->
				<view v-show="activeName == 'parking'" class="parking-page-container">
					<view class="search">
						<view v-if="activeName !== 'control'" @click="clickSearch(activeName)" class="search-container">
							<van-icon size="30rpx" name="http://image.bolink.club/FhI1Sn1b1nDfFriwFDxE2dOmg2J7" class="searchIcon" />
							<input class="input" disabled placeholder-style="font-size: 26rpx;font-weight: 400;color: #828EA6;"
								:placeholder="activeName == 'charge' ? '搜索目的地或电站' : '请输入目的地'" />		
						</view>
		<!-- 				<button v-if="!mobile" style="padding: 20px;position: absolute;top: 0;width: 100%;z-index: 999;"
							 @click="getPhoneNumber"></button> -->
							<view class="search-text" @click="clickSearch(activeName)" >搜索</view>
					</view>
					<parkingPage   ref="parkingPage"  :isAllowShow='isAllowShow' :longitude='longitude' :latitude='latitude'
						:hasChargingOrder="hasChargingOrder" :isScrollTop="isScrollTop" :navHeight="navHeight"
						:statusBarHeight="statusBarHeight" :activeName='activeName' :mobile="mobile">
					</parkingPage>
				</view>
				<!-- 社区 -->
				<!-- #ifdef MP-WEIXIN -->
				<view v-show="activeName == 'control'" class="control-page-container">
					<view class="search">
						<view v-if="activeName === 'control'" @click="clickControlSelect" class="search-container input1-container">
							<van-icon size="30rpx" name="http://image.bolink.club/Fmwg4NnU4jy9AlbkgCkpGEKDObzq" class="searchIcon" />
							<image src="http://image.bolink.club/Fr-jnyXC9VmTDM4sz05TpdmjaSpM" class="arrow-downIcon">  </image>
							<input class="input1" disabled placeholder-style="font-size: 26rpx;font-weight: 400;color: #828EA6;"
								:placeholder="controlSelect" />
						</view>
						<button v-if="!mobile" style="padding: 20px;position: absolute;top: 0;width: 100%;z-index: 999;"
							 @click="getPhoneNumber"></button>
					</view>
					<controlPage ref="controlPage" :mobile="mobile"></controlPage>
					<view :class="isShowSet ? 'pass-code code' : 'pass-code'" v-if="set" @click="handleClick">
						<view>通行码</view><van-icon size="32rpx" :class="isShowSet? 'switch':'switch close-switch'"
							name="http://image.bolink.club/FqnlJhTji5GqIA0i5p_UQzfoaNI4" />
					</view>
					<controlCode v-if="set && isShowSet" :activeName="activeName" :isShowSet="isShowSet" ref="controlCode">
					</controlCode>
				</view>
				<!-- #endif -->
				<!-- 说车 -->
				<view v-show="activeName == 'talkCar'" class="talkCar-page-container">
					<talkCarPage ref="talkCarPage"  @talkCarScroll="talkCarScroll" :infoData="infoData" :isAllowShow='isAllowShow' @praiseClick="praiseClick" :isEnd="isEnd" :navHeight ="navHeight" :statusBarHeight="statusBarHeight"></talkCarPage>
	<!-- 				<view class="list-end">{{ isEnd ? '亲，到底了' : '加载中...' }}</view> -->
				</view>
			</view>
		</scroll-view>
		<!-- #ifdef MP-ALIPAY || APP-PLUS -->
		<van-popup :show="showArea" position="bottom" onClick-overlay="areaCancel">
			<view class="area-list">
				<van-area :area-list="areaList" :loading="areaLoading" value="110101" :columns-num="2" title="所在城市"
					onConfirm="areaConfirm" onCancel="areaCancel" />
			</view>
		</van-popup>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<van-popup :show="showArea" position="bottom" custom-style="height: 30%;" @click-overlay="areaCancel">
			<van-area :area-list="areaList" :loading="areaLoading" value="110101" :columns-num="2" title="所在城市"
				@confirm="areaConfirm" @cancel="areaCancel" />
		</van-popup>
		<!-- #endif -->

		<!-- 图片预加载 防止切换抖动-->
		<view style="display: none;">
			<image src="https://image.bolink.club/front_page_controlled.png"></image>
			<image src="https://image.bolink.club/front_page_park%402x.png"></image>
		</view>
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<van-dialog id="van-dialog" />
		<view v-if="showSelectCommunity">
			<van-popup :show="showSelectCommunity" position="bottom" :overlay="true" :style="{ height: '90%' }">
				<view class="community-content">
					<view class="community-head flex-row mar-10">
						<view class="title flex jus pad-10 font-36 fw-700">
							请选择项目
						</view>
						<view class="close flex jue pad-10 font-26" @click="showSelectCommunity = false">
							关闭
						</view>
					</view>
					<view class="community-body">
						<template v-if="isVisible">
							<view class="community-group">
								<view class="community-item community-item--default" v-for="(item, index) in communityList" :key="index"
									:class="{ 'community-item--actived': index === activeIndex }" @click="choiseCommunity(index)">
									<text>{{ item.projectName }}</text>
								</view>
							</view>
						</template>
						<template v-else>
							<view class="community-no-data">
								<image src="https://image.bolink.club/pm_no_data.png" alt="" class="community-no-data--img">
								</image>
								<view class="community-no-data--text">暂无项目信息</view>
							</view>
						</template>
					</view>
					<view class="community-footer">
						<view v-if="isVisible">
							<view class="community-footer--btn" @click="toStay">搜索项目</view>
						</view>
						<view v-else>
							<view class="community-footer--btn marb-40" @click="toStay">搜索项目</view>
						</view>
					</view>
				</view>
			</van-popup>
		</view>
		<!-- #endif -->
		<view v-if="showChargingTip">
			<van-popup :show="showChargingTip" position="center" :overlay="true" :style="{ height: '90%' }" round>
				<view class="flex-col order-popup">
					<view class="flex jue padr-40">
						<van-icon name="cross" @click="showChargingTip = false" color="#666" size="40rpx" />
					</view>
					<image class="order-img" src="https://image.bolink.club/FmKxp-GTS6_1eh-eY3rjpLKsxwZO"
						@click="util.reNavigateTo('/pagesF/charge/state')"></image>
					<view class="flex ajc font-24" @click="util.reNavigateTo('/pagesF/charge/state')">您有一个在充订单</view>
				</view>
			</van-popup>
		</view>
		<tab-bar :currentIndex="0"></tab-bar>
	</view>
</template>
<script>
import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
import BaseConfig from '../../common/config/index.config.js';
import apis from "../../common/apis/index";
import form from '../../common/utils/form.js';
import infolist from "../../components/infolist/infolist.vue";
import guide from '../../components/guide/guide.vue';
import CustomPanel from './CustomPanel.vue';
import chargePage from './chargePage.vue';
import controlPage from './controlPage.vue';
import parkingPage from './parkingPage.vue';
import util from '../../common/utils/util.js';
import power from '../../common/utils/power.js';
import controlCode from './controlCode.vue';
import { qrcode } from '../../pagesF/static/utils/requcode.js'
import talkCarPage from './talkCarPage.vue'

let log = require('../../log.js');
let app = getApp();
export default {
	components: {
		infolist,
		guide,
		CustomPanel,
		chargePage,
		controlPage,
		parkingPage,
		controlCode,
		talkCarPage
	},
	data() {
		return {
			controlSelect: '搜索项目',
			controlList: [],
			form: form,
			activeName: 'charge',
			// activeName: 'control',
			// showGuide: false,
			guideTips: "点击右上角 “···”按钮，可以直接分享到微信朋友圈",
			gasData: [], // 加油站数据
			dis: 3000, // 附近距离
			num: 5, // 获取的加油站数量
			service: {
				car: "车险服务",
				oil: "加油",
				etc: "ETC",
				charge: "充电",
				violation: "违章代缴",
				inspection: "年检提醒"
			},
			isScrollTop: false,
			flag: false, // false 表示首次进入，控制 onLoad 和onShow 不重复触发
			set: true,//显示通行码
			token: '',
			userInfo: app.globalData.userInfo, // 用户信息
			mobile: uni.getStorageSync('mobile'), // 用户手机号
			imgUrl: null, // 广告数组
			weather: {}, // 天气数据
			latitude: BaseConfig.latitude,
			longitude: BaseConfig.longitude,
			showLocationTip: true, // 没获取到定位显示'暂无定位，默认',
			userLocation: false, // 用户是否开启定位授权，用户未授权时，提示显示"暂无授权，默认"
			showArea: false, // 显示城市选择
			areaLoading: true,
			areaList: {
				province_list: {},
				city_list: {},
				short_list: {
					110000: "京",
					120000: "津",
				}
			},
			current: 0, // 当前车牌下标
			areaName: '北京市', // 当前城市
			areaCode: '110100', // 当前城市code
			positionAreaName: '北京市',//初始化定位城市
			infoData: [], // 热点资讯数据
			isEnd: false,
			scrollTopDistance: "",
			pageNum: 1,
			pageSize: 30,
			windowHeight: null,
			statusBarHeight: 47,
			navHeight: 40,
			menuButtonHeight:32,//胶囊高度
			once: true,
			isProject: '',
			showSelectCommunity: false,
			activeIndex: '',
			communityList: [],
			hasChargingOrder: false,
			showChargingTip: false,
			util,
			isSupportAppointment: 0,
			isAllowShow: false,
			loginType:false,
			id: 0,
			faceUserId: -1,
			isShowSet: true,
			ladderControlForm: {
				currentLadderText: '',
				expirationTime: 5
			},
			appletConfiguration: null //小程序配
		}
	},
	computed: {
		isVisible() {
			return this.communityList.length > 0
		},
		strAreaName() {
			return this.areaName.slice(0, this.areaName.length - 1)

		},
		backgroundImageName(){
			let {activeName,isScrollTop}=this
			let str='box-size-bg'
			let name={
				'charge':isScrollTop?'charge-box-size-bg charge-box-size-bg-fixed box-size-bg-fixed box-size-bg':'charge-box-size-bg box-size-bg',
				'parking':isScrollTop?'parking-box-size-bg parking-box-size-bg-fixed box-size-bg-fixed box-size-bg':'parking-box-size-bg box-size-bg',
				'control':'control-box-size-bg  box-size-bg',
				'talkCar':'talkCar-box-size-bg  box-size-bg',

			}
			/* box-size-bg-fixed */
			// if(activeName==='charge'){
			// 	//充电


			// }else if(activeName==='parking'){
			// 	//停车

			// }else if(activeName==='control'){
			// 	//社区
			// }
			str=name[activeName]
			return str

		}
	},
	watch: {
		activeName: {
			handler(newVal) {
				if (newVal === "charge") {
					this.getPayOrder()
					this.getChargingOrder()
				} else if (newVal === "control") {
					// 选择梯控
					this.getSettingByUserId()


				}
			},
			immediate: true
		}
	},
	async onLoad(option) {
		console.log('option-->', option);
		// #ifdef MP-WEIXIN
		this.appletConfiguration = util.getAppletConfiguration();
		// #endif
		if ('isProject' in option) {
			this.isProject = option.isProject
		}
		if ('plateNumber' in option) { // 订阅消息带车牌号进来的情况
			uni.setStorageSync('lastCarNumber', option.plateNumber)
		}

		await app.initData()
		getApp().getSystemInfo().then(res => {
			this.windowHeight = res.windowHeight;
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight || res.titleBarHeight;
		});
		let menuButton=wx.getMenuButtonBoundingClientRect() 
		this.menuButtonHeight=menuButton.height

		// #ifdef MP-WEIXIN
		wx.getBackgroundFetchData({
			fetchType: 'periodic',
			success: (res) => {
				let periodicData = JSON.parse(res.fetchedData);
				if (periodicData && periodicData.infoList) {
					this.infoData = periodicData.infoList;
				}
			}
		})
		// #endif
		// #ifdef MP-WEIXIN
		uni.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline']
		})
		// #endif
		// #ifdef APP-PLUS
		this.activeName = 'charge'
		// #endif
		// #ifdef MP-WEIXIN
		apis.homeApis.getIsProject({
			openid: uni.getStorageSync('openId')
		}).then(res => {
			uni.setStorageSync('isProject', res.data.isProject)
			this.once = true
		})
		// #endif

		// #ifdef MP-ALIPAY
		this.$scope.areaCancel = this.areaCancel.bind(this)
		this.$scope.areaConfirm = this.areaConfirm.bind(this)
		this.$scope.areaCancel = this.areaCancel.bind(this)
		my.setNavigationBar({
		  frontColor: '#000000',
		  backgroundColor: '#ff0000',
		})
		// #endif
		this.getLocation()
		if(option.activeName){
			this.activeName=option.activeName
			this.getSettingByUserId()
			setTimeout(() => {
				this.$refs['controlPage'].getElementIcon()
			}, 200)
		}
	},
	// #ifdef MP-WEIXIN
	onShareAppMessage(res) {
		return {
			title: this.appletConfiguration && this.ppletConfiguration.sharName || '一码App',
			imageUrl: app.globalData.shareImg[0]
		}
	},
	// #endif
	onShow(options) {
		// let loginAgreement =uni.getStorageSync('loginAgreement')
		// if(!loginAgreement){
		// 	uni.redirectTo({
		// 		url:'/pagesA/agreementLogin/loginAgreement'
		// 	})
		// }else{
		// 	this.loginType=true
		// }
		uni.hideTabBar()
		this.hasChargingOrder = false
		this.showChargingTip = false
		this.$nextTick(() => {
			this.isAllowShow = Math.round(new Date()) > BaseConfig.releaseTime
		})
		let params = {
			longitude: this.longitude,
			latitude: this.latitude,
			page: 1,
			isSupportAppointment: this.isSupportAppointment
		}
		// #ifdef APP-PLUS
		this.activeName = 'charge'
		let mobile = uni.getStorageSync('mobile')
		if (!mobile) {
			return getApp().logOut()
		}
		this.getLocation();
		if (this.activeName == 'charge') {
			this.$refs['chargePage'].pageCharge = 0
			this.$refs['chargePage'].scrolltolower()
			this.getPayOrder()
			this.getChargingOrder()
		}
		// #endif
		// #ifndef APP-PLUS
		app.login().then(res => {
			if(this.appletConfiguration && !this.appletConfiguration.hiddenCharge){
				this.activeName = 'charge'
			}else{
				if (this.once) {
					this.once = false
					let isProject = uni.getStorageSync('isProject')
					this.activeName = isProject == 2 ? 'charge' : isProject == 1 ? 'control' : 'parking'
				}
				this.mobile = uni.getStorageSync('mobile');
				if (uni.getStorageSync('activeName')) {
					this.activeName = uni.getStorageSync('activeName')
					uni.removeStorageSync('activeName')
				}
				if (this.isProject) {
					this.activeName = this.isProject == 2 ? 'charge' : this.isProject == 1 ? 'control' : 'parking'
					this.isProject = ''
				}
			}
			
			this.initData();
			// this.getLocation();
			app.getRemoteUserInfo();
			app.userInfo().then(async res => {
				this.userInfo = app.globalData.userInfo;
				if (this.activeName === 'parking') {
					this.pageCharge = 1
					setTimeout(() => {
						// this.$refs['parkingPage'].existorder()
						this.$refs['parkingPage'].getOrderList()
						// this.$refs['parkingPage'].getUserPlateNumber()
						this.$refs['parkingPage'].getElementIcon()
						this.$refs['parkingPage'].chargeChooseChange(0)

					}, 200)
				} else if (this.activeName === 'charge') {
				} else {
					await this.getSettingByUserId()
					this.$refs && (this.$refs['controlCode'].currentDoorAuth = uni.getStorageSync('bl_door_control_group') || '')
					let children = this.$children
					let list = children.filter((item) => {
						if (item.selectEr) return item
					})
					list && list[0] && list[0].selectEr()
					setTimeout(() => {
						this.$refs['controlPage'].getElementIcon()
					}, 200)
				}
			}).catch((err) => {
				 console.log(err);
			})
			this.initControlData()
			if (this.activeName == 'charge') {
				if (!this.$refs['chargePage'].chargeContentList || this.$refs['chargePage'].chargeContentList.length == 0) {
					this.$refs['chargePage'].pageCharge = 0
					this.$refs['chargePage'].scrolltolower()
				}
				this.getPayOrder()
				this.getChargingOrder()
			}
		})
		// 判断是否有token和session_key
		getApp().isloginSuccess();
		this.controlSelect = uni.getStorageSync('bl_community_name') || '搜索项目'
		// console.log(uni.getStorageSync('bl_community_name'), '当前项目')
		// #endif
		
	},
	onReady() {
		getApp().pagePathRecord();
	},
	onUnLoad(){		
		clearTimeout(this.deferTimer)
	},
	onPullDownRefresh() {
		if (this.activeName !== 'control' && this.activeName !== 'charge') {
			this.pageNum = 1;
			setTimeout(() => {
				if (this.activeName === 'parking') {
					this.$refs['parkingPage'].getHotArea()
				}
			}, 200)
			this.getInfo();
		} else {

		}
	},

	methods: {
		// 跳转到消息通知页面
		goNews(){
			uni.navigateTo({
				url: '/pagesD/car/news'
			});
			
		},
		// 说车页面滚动到底部
		talkCarScroll(){
			this.pageNum = this.pageNum + 1;
			if (this.activeName !== 'control' && this.activeName !== 'charge') {
				uni.showLoading({
					title: "请稍候...",
					mask: true
				})
				this.getInfo();
			} else {
					
			}
			
		},
		handleGunCode() {
			uni.navigateTo({
				url: '/pagesB/charge/gunCode',
				fail: (err) => {
					console.log(err)
				}

			})

		},
		scrolltolower() {

			if(this.activeName==='charge'){
				this.$refs['chargePage'].scrolltolower()
			}else if(this.activeName==='parking'){
				this.$refs['parkingPage'].scrolltolower()
			}

		},
		startScroll(e) {
			let that=this
			let scrollTop = e.detail.scrollTop	
			/* 如果不是停车或者充电不用吸顶效果 */
			if(that.activeName==='charge'||that.activeName==='parking'){
				if (scrollTop > 260) {
					that.isScrollTop = true
					that.scrollTopDistance = scrollTop
				} else {
						that.isScrollTop = false
				}	
			}
		},
		toggleLadderControl(params) {
			// this.$set(this, 'ladderControlForm', params);
			this.$refs.controlCode.ladderControlForm = params

		},
		// 获取设置信息
		getSettingByUserId() {
			const bl_user_info = uni.getStorageSync('bl_user_info')
			let comid = uni.getStorageSync('bl_community_id')
			apis.smartCommunityApis.getSettingByUserId({
				userId: bl_user_info.id || -1
			}).then(res => {
				if (res.code == 200) {
					this.set = !!res.data.isIndexShowCode
					this.id = res.data.id
					this.faceUserId = res.data.faceUserId
				}
			})
		},
		handleClick() {
			this.isShowSet = !this.isShowSet
		},
		clickControlSelect() {
			this.showSelectCommunity = true
			this.getCommunityList()
		},
		// chooseControl(item) {
		// 	this.controlSelect = item.projectName
		// 	this.showControlSelect = false
		// 	this.currentProjectId = item.projectName || 0;
		// 	this.projectName = item.projectNumber || '请选择项目';
		// },
		clickSearch(activeName) {
			if (activeName == 'parking') {
				uni.navigateTo({
					url: '/pagesC/park/nearpark?type=search'
				})
			} else {
				uni.navigateTo({
					url: '/pagesB/charge/search?activeName=' + activeName
				})
			}
		},
		/* token失效处理 */
		async reRequestMessage(type) {
			uni.setStorageSync('cloudToken', '')
			let cloudToken = await this.getCloudToken()
			if (!cloudToken.token) return
			if (type == 1) {
				this.getPileMessage(cloudToken)
			}
		},

		handleNavClick(name) {
			let that=this
			// #ifdef APP-PLUS
			if (name !== 'charge') {
				uni.showToast({
					title: '功能暂未开放，敬请期待',
					icon: 'none',
					duration: 3000
				});
				return false
			}
			// #endif
			// #ifdef MP-ALIPAY
			if (name == 'control') {
				uni.showToast({
					title: '功能暂未开放，敬请期待',
					icon: 'none',
					duration: 3000
				});
				return false
			} else if (name == 'charge') {
				setTimeout(() => {
					that.$refs['chargePage'].pageCharge = 1
					that.$refs['chargePage'].queryPowerStationList()
				}, 200)
			} else if(name == 'parking'){				
				setTimeout(() => {
					that.$refs['parkingPage'].chargeChooseChange(0)
				}, 200)
			}else{
				// uni.showToast({
				// 	title: '请输入车牌号',
				// 	icon: 'none',
				// 	duration: 3000
				// });
				this.infoData = [],
				this.pageNum = 1
				this.getInfo();
			}
			this.activeName = name;
			return false
			// #endif
			this.activeName = name;
			if (name == 'parking') {
				setTimeout(() => {
					// this.$refs['parkingPage'].existorder()
					that.$refs['parkingPage'].getOrderList()
					// this.$refs['parkingPage'].getUserPlateNumber()
					that.$refs['parkingPage'].getHotArea()
					that.$refs['parkingPage'].getElementIcon()
					that.$refs['parkingPage'].chargeChooseChange(0)

				}, 500)

			} else if (name == 'charge') {
				setTimeout(() => {
					this.$refs['chargePage'].pageCharge = 1
					this.$refs['chargePage'].queryPowerStationList()
				}, 200)
			} else if(name == 'control'){
				setTimeout(() => {
					this.$refs['controlPage'].getElementIcon()
				}, 200)
				return false
			}else{
				this.infoData = [],
				this.pageNum = 1
				this.getInfo();
			}
			
			
			
		},

		// 资讯点赞
		praiseClick({ item, index }) {
			let state = item.praise;
			let praiseNum = this.infoData[index].praiseNum || 0;
			let praise = state == 1 ? 0 : 1;
			state == 1 ? --praiseNum : ++praiseNum;
			if (praiseNum < 0) praiseNum = 0;

			this.$set(this.infoData[index], 'praise', praise);
			this.$set(this.infoData[index], 'praiseNum', praiseNum);

			let params = {
				articalId: item.id,
				state: state,
			}
			apis.homeApis.praiseArtical(params).then((res) => {

			})
		},
		// 通过经纬度获取停车场信息
		getnearstation() {
			let params = {
				lat: this.latitude,
				lon: this.longitude,
				dis: this.dis,
				num: this.num,
			};
			apis.homeApis.getnearstation(params).then((res) => {
				this.gasData = [];
				let data = res.data || [];
				let gasData = [];
				if (data.length > 0) {
					let temp = [];
					for (let n = 0; n < data.length; n++) {
						if (data[n].detail && data[n].status == 'true') {
							let oilPriceList = data[n].detail.oilPriceList || [];
							temp = [];
							let len = oilPriceList.length;
							for (let i = 0; i < len; i++) {
								if (len == 1) {
									temp.push(oilPriceList[i]);
								} else if (i < len - 1) {
									if (oilPriceList[i].oilCode != oilPriceList[i + 1].oilCode) {
										temp.push(oilPriceList[i]);
									}
								} else { // 数组最后一个和前一个比较
									if (oilPriceList[i].oilCode != oilPriceList[i - 1].oilCode) {
										temp.push(oilPriceList[i]);
									}
								}
							}
							if (len > 0 && temp.length == 0) {
								temp.push(oilPriceList[0]);
							}
							data[n].detail.oilCodeList = temp;
							gasData.push(data[n]);
						}
					}
					this.gasData = gasData;
				}
			})
		},
		getLocation() {
			// 获取经纬度，通过高德获取城市名
			uni.getLocation({
				type: 'gcj02',
				success: (r) => {
					this.userLocation = true;
					uni.setStorageSync("longitude", r.longitude);
					uni.setStorageSync("latitude", r.latitude);
					this.latitude = r.latitude;
					this.longitude = r.longitude;
					// this.$refs.chargePage.queryPowerStationList()
					apis.homeApis.getcode({
						"lon": r.longitude,
						"lat": r.latitude
					}).then((res) => {
						this.areaName = res.data.cityName;
						this.areaCode = res.data.areaCode;
						this.positionAreaName = res.data.cityName;
						this.$refs.chargePage.queryPowerStationList()
						let code = String(this.areaCode).slice(0, 2) + "0000";
						uni.setStorageSync("shortName", this.areaList.short_list[code]);
						uni.setStorageSync("areaCode", code);
						if (this.flag) { }
					})
				},
				fail: (err) => {
					 console.log(err)
					this.userLocation = false;
				},
				complete: () => {
					// this.getnearstation();
				}
			});
		},
		// 显示授权弹框
		showUserLocation() {
			uni.showModal({
				title: '打开设置授权',
				showCancel: false,
				confirmText: '好的',
				content: "获取定位失败，请检查是否开启定位授权。",
				success: function (res) {
					uni.openSetting({
						success: (res) => {
							if (res.authSetting['scope.userLocation']) {
								this.getLocation();
							}
						}
					})
				}
			});
		},
		// 跳转热点资讯页
		jumpInfo() {
			let startTime = new Date().getTime();
			uni.navigateTo({
				url: '/pagesA/index/info',
				complete: (res) => {
					getApp().eventRecord({
						keyWord: '首页-点击全部资讯',
						clickType: 'Button',
						jumpType: '本程序页面',
						jumpDesc: '资讯列表页面',
						result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
						startTime: startTime,
						endTime: new Date().getTime()
					});
				}
			})
		},
		// // 授权提示
		// modify() { 
		// 	uni.showModal({ 
		// 		title: '请打开相机权限，否则功能将无法正常使用',
		// 		content: this.modify_content,
		// 		confirmText: '前往开启',
		// 		success: function(res) { 
		// 			if (res.confirm) { 
		// 				power.gotoAppPermissionSetting(); //动态修改权限
		// 			}
		// 		}
		// 	});
		// },
		// // 授权提示
		modify(type) {
			let title = "请打开位置权限，否则功能将无法正常使用";
			(type == 2) && (title = "请打开相机权限，否则功能将无法正常使用")
			uni.showModal({
				title: title,
				content: this.modify_content,
				confirmText: '前往开启',
				success: function (res) {
					if (res.confirm) {
						power.gotoAppPermissionSetting(); //动态修改权限
					}
				}
			});
		},
		// 点击切换城市
		async chooseCity() {
			if (this.userLocation) {
				// #ifndef APP-PLUS
				this.showArea = true;
				// #endif
			} else {
				// #ifdef APP-PLUS
				// 获取权限定位信息
				// let resp = await power.requestAndroidPermission('android.permission.CAMERA');
				let resp = await power.requestAndroidPermission('android.permission.ACCESS_COARSE_LOCATION');
				if (resp == -1) {
					this.modify();
				} else if (resp == 1) {
					this.getLocation();
				}
				// #endif
				// #ifndef APP-PLUS
				this.showUserLocation();
				// #endif
			}

		},
		// 点击确定
		areaConfirm(e) {
			let that=this
			try {
				this.areaName = e.detail.values[1].name;
				this.areaCode = e.detail.values[1].code;
				uni.setStorageSync("shortName", this.areaList.short_list[e.detail.values[0].code]);
			} catch (err) {
				let name = e.detail.values && e.detail.values[0].name
				if (name.includes('香港') || name.includes("澳门")) {
					this.areaName = e.detail.values[0].name
					this.areaCode = e.detail.values[0].code;
					uni.setStorageSync("shortName", this.areaList.short_list[e.detail.values[0].code]);
				}
				app.globalData.log.error('--areaConfirm--this.areaName->', err, this.areaName);
			}
			this.showArea = false;
			if (this.activeName !== 'control') {
				setTimeout(() => {
					if (that.activeName === 'parking') {
						that.$refs['parkingPage'].getHotArea()
						that.$refs['parkingPage'].chargeChooseChange(0)
					} else if(that.activeName === 'charge'){
						that.$refs['chargePage'].pageCharge = 0
						that.$refs['chargePage'].queryPowerStationList()
						that.$refs['chargePage'].getHotArea()
					}else{
						that.getInfo();
					}
				}, 200)
				
			}
		},
		// 点击取消
		areaCancel() {
			this.showArea = false;
		},
		// 获取用户信息的回调
		getUserProfile(e) {
			app.updataUserInfo().then(() => {
				this.userInfo = app.globalData.userInfo;
			});
		},
		// 获取手机号
		async getPhoneNumber(e, name) {
			let res=await util.getMobile()
			if(res.flag===0){
				uni.navigateTo({
					url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=2&activeName=${this.activeName}`,
				})
			}else{
				this.mobile = res.mobile
			}

		},
		// 初始化城市和车牌信息
		initData() {
			let areaList = uni.getStorageSync("areaList");
			if (areaList && JSON.stringify(areaList.province_list) != '{}') {
				this.areaList = areaList;
				this.areaLoading = false;
			} else {
				apis.homeApis.getcitytree().then((res) => {
					let province_list = {};
					let city_list = {};
					let short_list = {};
					let arr = res.data.cityTreeNodes;
					let pro = res.data.pro;
					let len = res.data.cityTreeNodes.length;
					for (let i = 0; i < len; i++) {
						province_list[arr[i].areaCode] = arr[i].cityName;
						let arr2 = arr[i].children;
						let len2 = arr2.length;
						if (len2 > 0) {
							for (let n = 0; n < len2; n++) {
								city_list[arr2[n].areaCode] = arr2[n].cityName;
							}
						}
					}
					for (let n = 0; n < pro.length; n++) {
						short_list[pro[n].code] = pro[n].name;
					}
					this.areaList.province_list = province_list;
					this.areaList.city_list = city_list;
					this.areaList.short_list = short_list;
					this.areaLoading = false;

					let code = this.areaCode.slice(0, 2) + "0000";
					uni.setStorageSync("shortName", this.areaList.short_list[code]);
					uni.setStorage({ // 缓存城市信息
						key: "areaList",
						data: this.areaList
					})
				});
			}
			if (!this.flag) {

			}

			if (this.activeName !== 'control' && this.activeName !== 'charge') {
				setTimeout(() => {
					if (this.activeName === 'parking') {
						this.$refs['parkingPage'].getHotArea()
					} else {
						this.$refs['chargePage'].getHotArea()
					}
				}, 200)

			}else if(this.activeName == 'talkCar'){
				this.pageNum = 1
				this.getInfo();
			}
		},
		// 获取资讯
		getInfo() {
			// #ifdef APP-PLUS
			return false
			// #endif
			let params = {
				areaCode: this.areaCode,
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				// type: this.activeName == 'charge' ? 7 : '',
			}
			apis.homeApis.getInformation(params).then((res) => {
				uni.stopPullDownRefresh();
				uni.hideLoading();
				if (this.pageNum === 1) {
					this.infoData = res.data.rows;
				} else {
					this.infoData = this.infoData.concat(res.data.rows);
				}
				if (res.data.total > 0 && res.data.rows.length == 0) {
					this.isEnd = true;
				}
			}).catch(err => {
				uni.hideLoading();
			});
		},
		initControlData() {
			this.controlList = uni.getStorageSync('projectList')
			if (this.controlList && this.controlList.length > 0) {
				let projectNumber = uni.getStorageSync('bl_community_ladder_id')
				if (projectNumber) {
					let index = this.controlList.findIndex(item => item.projectNumber == projectNumber)
					this.controlSelect = (index != -1 && index) || index === 0 ? this.controlList[index].projectName :
						this.controlList[0].projectName
				} else {
					this.controlSelect = this.controlList[0].projectName
				}
			}
		},
		choiseCommunity(index) {
			this.activeIndex = index;
			this.saveCommunity()
		},
		getCommunityList() {
			apis.smartCommunityApis.getAllProjectInfoByPhone({
				phone: this.mobile||uni.getStorageSync('mobile')
			}).then(res => {
				if (res.code === 200) {
					let data = res.data;
					if (data && data.length > 0) {
						this.communityList = data;
						let comid = uni.getStorageSync('bl_community_id')
						if (comid) {
							let index = data.findIndex(item => item.comid == comid)
							this.activeIndex = index != -1 && index || 0
						} else {
							this.activeIndex = 0;
						}
						this.setCommunityData()
					} else {
						this.communityList = [];
						this.activeIndex = 0;
						// uni.showModal({
						// 	content: res.message,
						// 	showCancel: false,
						// 	confirmText: '确定',
						// 	success: function(res) {

						// 	}
						// });
					}
				} else {
					this.communityList = [];
					this.activeIndex = 0;
				}

			}).catch(err => {
				 console.log(err)
				this.communityList = [];
				this.activeIndex = 0;
			})
		},
		setCommunityData() {
			let {
				id,
				comid,
				projectName
			} = this.communityList[this.activeIndex];
			this.controlSelect = projectName
			uni.setStorageSync('bl_community_id', comid);
			uni.setStorageSync('bl_community_ladder_id', id);
			uni.setStorageSync('bl_community_name', projectName);
		},
		async saveCommunity() {
			if (this.isVisible) {
				this.setCommunityData()
				uni.removeStorageSync('bl_door_access_group')
				uni.removeStorageSync('bl_user_info')
				uni.removeStorageSync('bl_door_control_array')
				uni.removeStorageSync('bl_door_control_group')
				uni.removeStorageSync('bl_ladder_control_info')
				this.showSelectCommunity = false
				await getApp().getRemoteUserInfo();

			}
		},
		// 搜索项目
		toStay() {
			uni.redirectTo({
				url: '/pagesG/collect_user_info/communitySearch?type=jump'
			});
		},
		/* 获取云平台token */
		async getCloudToken() {
			return new Promise((resolve, reject) => {
				util.getCloudToken()
					.then((res) => {
						resolve(res)
					})
					.catch((err) => {
						 console.log(err)
					})
			})
		},
		/* 获取订单信息 */
		async getChargingOrder() {
			let cloudToken = await this.getCloudToken()
			if (!cloudToken.token) return
			let params = {
				...cloudToken,
				state: 0,
				page: 1,
				rp: 1,
				type: 1,
				// orderNo: uni.getStorageSync('orderId') || '',
			}
			apis.chargingPile.queryorder(params).then((res) => {
				if (res.code == 200) {
					if (res.data.list && res.data.list.length > 0) {
						const data = res.data.list[0]
						uni.setStorageSync('gunNo', data.gunNum)
						uni.setStorageSync('orderId', data.businessNum)
						uni.setStorageSync('pileCode', data.pileCode)
						this.hasChargingOrder = true
						// this.showChargingTip = true
					} else {
						this.hasChargingOrder = false
						// this.showChargingTip = false
					}
				} else if (res.code == 4002) {
					uni.setStorageSync('cloudToken', '')
					setTimeout(() => {
						this.getChargingOrder()
					}, 1000)
				} else {
					uni.showToast({
						title: res.message,
						icon: 'none',
						duration: 2000,
					})
				}
			})
		},
		/* 获取待支付订单 */
		getPayOrder() {
			let openId = uni.getStorageSync('openId')
			apis.chargingPile.getPaidOrder({ openId }).then(res => {
				let { data, code } = res
				if (code == 200) {
					if (data && data.length > 0) {
						util.reNavigateTo('/pagesF/order/payOrder?tarbar="pages/index/index"')
					}
				}
			})

		},
	}
}
</script>
<style lang="less" scoped>
/deep/.van-cell__left-icon-wrap {
	color: #fff;
}

/deep/.van-field__input {
	color: #fff !important;
}

/deep/.van-icon {
	color: #fff;
}


/deep/.van-dropdown-menu {
	color: #fff;
	height: 68rpx !important;
	width: 378rpx !important;
	background: #A1C1FF !important;
	border: 2px solid #A1C1FF !important;
	border-radius: 20px !important;
}

/deep/.van-search__content {
	background: #A1C1FF !important;
}

.search {
	position: relative;
	top: 3px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	width:100%;
	padding:0 40rpx;
	.search-container{
		display: flex;
		width: 580rpx;
		padding-left: 10px;
		height: 64rpx;
		flex-direction: column;

	}
	.input1-container{
		width:100%;
		padding-right: 16rpx;
	}

	.input {
		background-color: #F1F2F6;
		border-radius: 28rpx;
		border: 2px solid #F1F2F6;
		height: 64rpx;
		padding-left: 60rpx;
	}

	.input1 {
		background-color: #F1F2F6;
		border-radius: 28rpx;
		border: 2px solid #F1F2F6;
		height: 64rpx;
		padding-left: 50rpx;
		width: 100%;
		overflow:hidden; //超出的文本隐藏
		text-overflow:ellipsis; //溢出用省略号显示
		white-space:nowrap; //溢出不换行
	}
	.search-text{
		width: 82rpx;
		height: 26rpx;
		font-size: 26rpx;
		font-weight: 400;
		color: #049558;

	}

	.searchIcon {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		left: 10%;
	}

	.arrow-downIcon {
		width:63rpx;
		height:63rpx;
		position: absolute;
		color: #fff;
		// #ifdef MP-ALIPAY
		top: 0;
		// #endif
		// #ifdef MP-WEIXIN
		top: 2rpx;
		// #endif
		right: 50rpx;
		z-index: 99;
	}
}

.search-content {
	width: 378rpx;
	height: 268rpx;
	padding: 10px;
	border-radius: 28rpx;
	margin-top: 8rpx;
	border: 2px solid #A1C1FF;
	background: #fff;
	color: #000;
	flex: 1;

	.searchParkItem {
		font-size: 26rpx;
		font-weight: 400;
		color: #242E3E;
		height: 48rpx;
		line-height: 48rpx;
		text-overflow: ellipsis;
		border-bottom: 1px solid #ccc;
		width: 100%;
		overflow: hidden;
		white-space: nowrap;
	}
}

/deep/ .van-search {
	padding: 0 !important;
	background: transparent !important;

}



.front-page-top {
	position: fixed;
	display: flex;
	font-weight: 700;
	font-size: 32rpx;
	width: 500rpx;
	justify-content: space-between;
	align-items: center;
	// #ifdef MP-ALIPAY
	width: 380rpx;
	transform: translateX(10vw);
	// #endif

	.top-location {
		min-width: 136rpx;
		max-width: 270rpx;
		height: 40rpx;
		background: #000000;
		border-radius: 24rpx;
		opacity: 0.6;
		font-weight: 500;
		color: #FFFFFF;
		font-size: 26rpx;
		display: flex;
		justify-content: space-around;
		padding: 0 10rpx;

		.location_icon {
			width: 20rpx;
			height: 24rpx;
			margin: 0 5rpx;
		}

		.front_page_arrow {
			position: relative;
			top: 2rpx;
			width: 12rpx;
			height: 8rpx;
			background-image: url('http://image.bolink.club/FjEG3L-iohdH6TsqKtx2iaGtGLAt');
			background-size: 12rpx 8rpx;
			background-position: center center;
			background-repeat: no-repeat;
			margin: 0 5rpx;
		}
	}

	.news-container {
		position: relative;
		display: flex;
		align-items: center;
		margin-right: 10rpx;

		.news-count {
			position: absolute;
			top: 5rpx;
			right: 0;
			width: 20rpx;
			height: 20rpx;
			font-size: 18rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 19rpx;
			background-color: #f5655d;
			border-radius: 10rpx;
			text-align: center;

		}

		.location_news {
			width: 48rpx;
			height: 48rpx;
			// transform: translateY(50%);
		}

	}

}


.box-size-bg {
	background-image: url('http://image.bolink.club/Fn_aSWyWDE_yPFy5c6Kq5trhcrbP');
	background-position: center center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	z-index: 1;

	.advertising-language-container {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.advertising-language {
		width: 268rpx;
		height: 29rpx;
		background-image: url('http://image.bolink.club/FhZYP1fSc9cvWJ8G4w0mzUGYSpAL');
		background-size: 268rpx 29rpx;
		position: absolute;
		bottom: 160rpx;
		left: 42rpx;
	}
}
.box-size-bg-fixed{
	width: 100%;
  position: fixed;
  top: 0rpx;
	background-image: url('http://image.bolink.club/FowLSn4o88e_oC1xYmiIt_LuIJ_K');
	background-position: center center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	height:175rpx !important;
}
/* 充电背景图 */
.charge-box-size-bg{
	background-image: url('http://image.bolink.club/Fn_aSWyWDE_yPFy5c6Kq5trhcrbP');
}
.charge-box-size-bg-fixed{
	background-image: url('http://image.bolink.club/FowLSn4o88e_oC1xYmiIt_LuIJ_K');
}
/* 停车 */
.parking-box-size-bg{
	background-image: url('http://image.bolink.club/Fjc9OgfmSwIspBCjatVh00kzNh8k');
}
.parking-box-size-bg-fixed{
	background-image: url('http://image.bolink.club/FhgjArMlXZ30x23dVx19UvJKN8gr');
}
// 社区
.control-box-size-bg{
	background-image: url('http://image.bolink.club/FodEbsvyusGaUl4_xzU4qxEE6LDK');
}

// 说车
.talkCar-box-size-bg{
	background-image: url('http://image.bolink.club/Fs0dw9htT7D7OlNCROQYghd8DnTE');
}

	

.front-page-container {
	position: relative;
	width: 100%;
	overflow: hidden;
	height:100vh;
}


.page-nav-content {
	position:relative;
	.nav-title {
		display: flex;
		justify-content: space-around;
		text-align: center;
		height: 120rpx;
		background-image: url('http://image.bolink.club/FvFPZ8poPPkfMDKr4axijTIBEe5Q');
		background-size: 100% 100%;
		margin-top: -105rpx;
		.nav-item {
			width: 68rpx;
			height: 90rpx;
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 90rpx;
			// #ifdef MP-ALIPAY
			min-width:20%;
			.parking_text{
				margin-right:20rpx;
			}
			// //#endif 

		}

		.nav-item-active {
			font-weight: 600;
			color: #000000;
			font-size: 40rpx;
			width: 80rpx;

		}
	}

	.charge-page-container {
		background-image: url('http://image.bolink.club/FgFr8KGahTkk_gPe_h6VMWDkcV4o');
		width: 100%;
		height: 300rpx;
		background-size: 100% 450rpx;
		background-repeat: no-repeat;
		margin-top: -145rpx;
		padding-top: 130rpx;
		// #ifdef MP-ALIPAY
		margin-top: -147rpx;
		background-size: 140% 470rpx;
		// //#endif 

	}
	.parking-page-container{
		background-image: url('http://image.bolink.club/FvWIiIt7liBwm5qfcfcjB_tebvBK');
		width: 100%;
		height: 300rpx;
		background-size: 100% 438rpx;
		margin-top: -133rpx;
		padding-top: 130rpx;
		// #ifdef MP-ALIPAY
		margin-top: -135rpx;
		background-size: 140% 448rpx;
		background-position: -40rpx 0;
		// //#endif 

	}

	.control-page-container{
		background-image: url('http://image.bolink.club/FtBhyhrrT62X7fs4YzdZljNXD4HC');
		width: 100%;
		height: 582rpx;
		background-size: 100% 590rpx;
		margin-top: -133rpx;
		padding-top: 130rpx;		

	}
	.talkCar-page-container{
		background-image: url('http://image.bolink.club/FvXXWSYW-KgJsGPR4IQVRwlV-n9Y');
		width: 100%;
		background-size: 100% 1340rpx;
		margin-top: -133rpx;
		padding: 130rpx 0;	
		// #ifdef MP-ALIPAY
		background-size: 140% 1420rpx;
		background-position: -290rpx 0rpx;
		// //#endif 
	
	}





}
.content {
	padding: 16px 16px 0px;
	background-color: red;

}

.button {
	color: #5571DB
}

.community-content {
	.community-head {
		.title {
			color: #2A2F38;
		}

		.close {
			color: #5571DB;
			font-weight: 600;
		}
	}

	.community-body {
		height: calc(100vh - 400rpx);
		padding: 40rpx;

		.community-group {
			.community-item {
				padding: 18rpx 49rpx;
				display: inline-block;
				margin: 0 25rpx 24rpx 0;
				font-size: 28rpx;
				font-weight: 700;
				border-radius: 40rpx;
				border: 2rpx solid;
			}

			.community-item--default {
				border-color: #f8f8f8;
				color: #828EA6;
			}

			.community-item--actived {
				color: #FFFFFF;
				background: #3970f0;
				border-color: #3970f0;
				box-shadow: 0 12rpx 32rpx 0 rgba(57, 112, 240, 0.28);
			}
		}

	}
}

.community-no-data {
	height: 50vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	&--img {
		margin: 0 0 53rpx 0;
		width: 326rpx;
		height: 326rpx;
	}

	&--text {
		font-size: 28rpx;
		font-weight: 700;
		color: #828ea6;
	}
}

.community-footer {
	position: absolute;
	bottom: 30rpx;
	left: 40rpx;
	right: 40rpx;

	&--btn {
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		font-size: 34rpx;
		font-weight: 800;
		text-shadow: 0 12rpx 32rpx 0 rgba(57, 112, 240, 0.28);
		background: linear-gradient(277deg, #3663f4 0%, #468cfe);
		border-radius: 50rpx;
		box-shadow: 0 12rpx 32rpx 0 rgba(57, 112, 240, 0.28);
		color: #fff;
	}

	&--back {
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		font-size: 34rpx;
		font-weight: 800;
		color: #3663f4;
		background: #f2f7ff;
		border: 2rpx solid #3970f0;
		border-radius: 80rpx;
	}
}

.order-popup {
	padding: 20rpx 0 40rpx;
	color: #3663f4;

	.order-img {
		height: 200rpx;
		width: 200rpx;
		margin: 10rpx 100rpx 60rpx;
	}
}

.pass-code {
	width: 702rpx;
	height: 120rpx;
	background: #FFFFFF;
	box-shadow: 0 4rpx 34rpx 0 rgba(0, 0, 0, 0.1);
	padding: 0 60rpx;
	margin: 0 auto;
	line-height: 120rpx;
	border-radius: 38rpx;
	display: flex;
	justify-content: space-between;
	z-index: 2;
	margin-top:50rpx;

	.switch {
		width: 68rpx;
		height: 68rpx;
	}
	.close-switch{
		transform:rotate(180deg);
		margin-top: 45rpx;
		margin-right: -37rpx;
	}
}

.code {
	border-radius: 38rpx 38rpx 0 0;
	z-index: 22;
	// border-top-right-radius: 38rpx;
	// border-top-left-radius: 38rpx;
}

::v-deep .pass-code .van-icon__image {
	width: 68rpx !important;
	height: 68rpx !important;

}

.area-list {
	height: 750rpx;
	overflow-y: auto;
}

.charge-page-overflow {
	height: calc(100vh - 540rpx);
	overflow-y: hideen;
}

.tip-arrow {
	padding-left: 8rpx;
}

.pile-code {
	position: fixed;
	bottom: 160rpx;
	right: 40rpx;
	z-index: 9;
	width: 80rpx;
	height: 120rpx;
	border-radius: 70rpx;
	background: #ffffff;
	box-shadow: 0 4rpx 34rpx 0 rgba(0, 0, 0, 0.3);
	text-align: center;
	color: #2c2c2c;
	font-size: 24rpx;
	padding: 10rpx 0;

}

// #ifdef MP-WEIXIN || APP-PLUS || MP-WEIXIN
::v-deep .van-popup--bottom {
	bottom: 100rpx !important;
}

//#endif 
</style>
