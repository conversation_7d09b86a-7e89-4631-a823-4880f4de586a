.share-sheet-options {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.share-sheet-options .van-share-sheet__options {
    position: relative;
    display: -webkit-flex;
    display: flex;
    padding: 16px 0 16px 8px;
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
}

.share-sheet-options .van-share-sheet__options--border:before {
    position: absolute;
    box-sizing: border-box;
    -webkit-transform-origin: center;
    transform-origin: center;
    content: " ";
    pointer-events: none;
    top: 0;
    right: 0;
    left: 16px;
    border-top: 1px solid #ebedf0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.share-sheet-options .van-share-sheet__options::-webkit-scrollbar {
    height: 0;
}

.share-sheet-options .van-share-sheet__option {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    align-items: center;
    -webkit-user-select: none;
    user-select: none;
}

.share-sheet-options .van-share-sheet__option:active {
    opacity: 0.7;
}

.share-sheet-options .van-share-sheet__button {
    height: auto;
    padding: 0;
    line-height: inherit;
    background-color: initial;
    border: 0;
}

.share-sheet-options .van-share-sheet__button:after {
    border: 0;
}

.share-sheet-options .van-share-sheet__icon {
    width: 48px;
    height: 48px;
    margin: 0 16px;
}

.share-sheet-options .van-share-sheet__name {
    margin-top: 8px;
    padding: 0 4px;
    color: #646566;
    font-size: 12px;
}

.share-sheet-options .van-share-sheet__option-description {
    padding: 0 4px;
    color: #c8c9cc;
    font-size: 12px;
}
