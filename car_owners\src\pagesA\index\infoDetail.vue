<template>
	<view class="detail">
		<view
			class="custom-back-nav"
			:style="{position: 'fixed',top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}"
		>
			<view class="flex-row alc">
				<!-- #ifdef MP-WEIXIN -->
				<van-icon @click="goBack" name="arrow-left" color="#fff" size="36rpx" />
				<!-- #endif -->
				<!-- #ifdef MP-ALIPAY -->
				<van-icon @click="goBack" style="width: 18px;" color="#fff" size="36rpx" />
				<!-- #endif -->
				<view @click="goBack" style="margin-left: 4rpx;" class="return-text">返回</view>
			</view>
		</view>
		 <!-- #ifdef MP-WEIXIN -->
			<template v-if="item.artUrl">
				<web-view :src="item.artUrl"></web-view>
				<!-- <web-view src="https://mp.weixin.qq.com/s/oTUz8CtYsQc7lIWgmAHnKA"></web-view> -->
			</template>
		<!-- #endif -->
		<view v-if="item.picUrl" style="position: relative;">
			<view >
				<image :src="item.picUrl+'?imageView2/0/w/600'" style="width: 100%;"  @load="imgLoad">
			</view>
			<view class="abso-box" style="margin-top: -40rpx;">
				
				<view class="flex-col title-box">
					<view style="font-size: 36rpx;color: #1e83fa;font-weight: 700;">
						<view class="of-clamp2">{{item.title}}</view>
					</view>
					<view class="flex-row mart-20 title-box-text">
						<view style="width: 460rpx;" class="of-clamp1">{{item.typeName || ''}} {{item.source || ''}}</view>
						<view class="flex-1 jue">{{item.createTime}}</view>
					</view>
					
				</view>
				
				<view class="box-content">
					<view>
						<rich-text :nodes="item.content" space="npsp"></rich-text>
					</view>
					<view class="share-view">
						<view class="flex-row alc flex-1" style="width: 100%; height: 145rpx;margin-right: 40rpx;" >
							<view v-if="show" style="border-radius: 50%;" >
									<view style="height: 100rpx;opacity: 0;">&nbsp;</view>
									<view class="bottom-input">
										<view class="flex-row alc " style="padding-left: 45rpx; padding-top: 40rpx;">
											<view class="dis-input flex-row alc ">
											<input type="text" :maxlength="maxlength" :value="msg" @input="inputChange" :cursor-spacing="12" placeholder="我也要说点什么..." />
											</view>
											<view class="marr-10" style="color: #999;width: 100rpx;"></view>
											<!-- #ifdef MP-ALIPAY -->
											<van-button custom-class="sub-btn" color="linear-gradient(100deg,#4fa4ff 0%, #0e78ff 100%)" :loading="loading" size="small" onClick="disAdd">发送</van-button>
											<!-- #endif -->
											<!-- #ifdef MP-WEIXIN -->
											<van-button custom-class="sub-btn" color="linear-gradient(100deg,#4fa4ff 0%, #0e78ff 100%)" :loading="loading" size="small" @click="disAdd">发送</van-button>
											<!-- #endif -->  
											
										</view>
									</view>
							</view>
							<view class="flex" style="position: absolute;bottom: 36rpx;right: 0rpx;">
								<view type="default" @click="send"  class="ajc" style="position: relative;">
									<image src="https://image.bolink.club/yima/index-msg.png" mode="" style="width: 52px;height: 50px;"></image>
									<span v-if="total" class="cor-9 font-28" style="position: absolute;top:8rpx;right: 40rpx;">{{total}}</span>
								</view>
								<button open-type="share" v-if="item" class="ajc">
									<image  src="https://image.bolink.club/yima/index-share.png" mode="" style="width: 68px;height: 56px;"></image>
								</button>
								<!-- #ifdef MP-WEIXIN -->
								
								<view class="ajc add" @click="shareWx">
									<image src="https://image.bolink.club/yima/index-share2.png" mode="" style="width: 54px;height: 54px;">
									</image>
								</view>
								<!-- #endif -->  
							</view>
						</view>
						<!-- <view class="flex-row juc marb-30 mart-20">
							<button open-type="share" v-if="item">
								<view class="flex-row alc juc">
									<image src="https://image.bolink.club/yima/index-weixin.png" mode=""></image>
									<view class="padl-20">分享给朋友</view>
								</view>
							</button>
						</view> -->
					</view>
				</view>
				<view class="dis-par" style="position: position;bottom: 0rpx;background-color: #fff;padding-bottom: 200px;">
					<view class="dis-title" style="font-size: 34rpx;margin-left: 34rpx;">全部评论</view>
					<view class="msg-content flex-col flex-1">
					<template v-if="newList.length>0">
						<view v-for="(item,index) in newList" :key="index" class="flex-col msg-item">
							<view class="flex-row alc">
								<view class="item-ava">
									<image :src="item.headPhoto"></image>
								</view>
								<view class="flex-col marl-20 fg1">
									<!-- <view>
										<span class="of-clamp1">{{item.nickName}}</span>
									</view> -->
									<view>
										<span class="of-clamp1">{{item.mobile}}</span>
									</view>
									<view class="item-time" style="margin-top: 7rpx;">{{item.ctime}}</view>
								</view>
								<view class="flex-row alc jue heart-img" style="margin-right: 33rpx;">
									<image @click="praiseClick(item, index)" v-if="item.state==1" src="https://image.bolink.club/yima/heart1.png"></image>
									<image @click="praiseClick(item, index)" v-else src="https://image.bolink.club/yima/heart.png"></image>
									<view class="marl-10">{{item.praisenum || 0}}</view>
									
								</view>
							</view>
							<view class="item-content">
								<span>{{item.content}}</span>
							</view>
						</view>
					</template>
						<view class="list-end" v-if="newList.length>0">{{isEnd ? '亲，到底了' : '加载中...'}}</view>
						<view class="list-end" v-else>还没人发表评论</view>
					</view>
					</view>
				</view>
			</view>
			
			<van-transition :show="showGuide" custom-class="block" >
				<guide :content="guideTips" @close="showGuide=false" />
			</van-transition>
			
		</view>
		
	</view>
	
</template>

<script>
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	import empty from '../../components/empty/empty.vue';
	import guide from '../../components/guide/guide.vue';
	const app = getApp();
	
	export default {
		components: {
			empty,
			guide
		},
		data() {
			return {
				showGuide: false,
				guideTips: "点击右上角 “···”按钮，可以直接分享到微信朋友圈",
				item: null,
				id: null,
				imgHeight: 240,
				windowHeight: null,
				statusBarHeight: null,
				navHeight: null,
				screenWidth: null,
				show:false,
				maxlength: 30,
				userInfo: app.globalData.userInfo, // 用户信息
				loading: false,
				actionsShow: false,
				sendshow:false,
				isEnd: false,
				pageNum: 1,
				pageSize: 20,
				newList:[],
				disList:[],
				msg: '',
				total: '',
				content:'height:145rpx'
			};
		},
		// 转发到朋友
		onShareAppMessage: function (res) {
		    return {
		      title: this.item.title,
			  imageUrl: this.item.picUrl + '?imageView2/0/w/300',
		      path: `/pagesA/index/infoDetail?id=${this.item.id}`
		    }
		},
		// 分享到朋友圈
		onShareTimeline: function () {
			return {
			  title: this.item.title,
			  imageUrl: this.item.picUrl + '?imageView2/0/w/300',
			  query: `/pagesA/index/infoDetail?id=${this.item.id}`
			}
		},
		onLoad (options) {
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
				this.screenWidth = res.screenWidth;
			});
			app.userInfo().then(res => {
				this.userInfo = app.globalData.userInfo;
			}).catch((err) => {
				console.log(err);
			});
			if ('id' in options) {
				this.id = options.id;
				getApp().login().then(res => {
					this.informationviews();
					this.initData();
				})
			}
			// #ifdef MP-ALIPAY
			this.$scope.disAdd = this.disAdd.bind(this)
			// #endif
		},
		onReady () {
			getApp().pagePathRecord();
			
		},
		onReachBottom () {
			this.pageNum = this.pageNum + 1;
			uni.showLoading({
				title: "请稍候",
				mask: true
			})
			this.initData();
		},
		methods: {
			shareWx () {
				if (app.globalData.platform === 'android') {
					this.showGuide = !this.showGuide;
				} else {
					uni.showToast({
						title: "该功能目前只对安卓系统用户开放哦~",
						icon: "none"
					});
				}
			},
			inputChange (e) {
				this.msg = e.detail.value.trim();
			},
			// 文章点赞
			praiseClick (item, index) {
				let statee = item.state;
				let praisenum = this.newList[index].praisenum || 0;
				let state = statee == 1 ? 0 : 1;
				statee== 1 ? --praisenum : ++praisenum;
				if (praisenum<0) praisenum = 0;
				
				this.$set(this.newList[index], 'state', state);
				this.$set(this.newList[index], 'praisenum', praisenum);
				
				let params = {
					 commId: item.id,
					state,
					articleId:this.id
				}
				apis.homeApis. articlecommentpraise(params).then((res) => {
					this.loading = false;
					this.initData();
				})
			},
			send(index){
				this.show=!this.show;//发送显示与隐藏
			},
			encryptionMobile(mobile){
				if (!mobile) return '***********'
				var reg = /^(\d{3})\d{4}(\d{4})$/;
				return mobile.replace(reg, "$1****$2");
			},
			// 文章列表
			initData () {
				let params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					articleId: this.id,
				}
				apis.homeApis.articlereview(params).then((res) => {
					uni.hideLoading();
					this.total = res.data.total;
					let temp = res.data.rows;
					for (let i = 0; i < temp.length; i++) {
						temp[i].ctime = form.timestampToTime(temp[i].ctime)
						temp[i].mobile = this.encryptionMobile(temp[i].mobile)
					}
					if (this.pageNum === 1) {
						this.newList = temp;
					} else {
						this.newList = this.newList.concat(temp);
					}
					if (this.total && temp.length === 0) {
						this.isEnd = true;
					}
					console.log(this.newList);
				})
			},
			disAdd () {
				if (!this.msg) {
					uni.showToast({
						title: "请输入内容",
						icon: "none"
					});
					return false
				}
				this.loading = true;
				this.show=true;
				let params = {
					nickName: this.userInfo.nickName,
					headPhoto: this.userInfo.avatarUrl,
					content: this.msg,
					articleId: this.id,
				};
				apis.homeApis.articlecomment(params).then((res) => {
					this.loading = false;
					if (res.status === 200) {
						this.msg = '';
						
						this.pageNum = 1;
						this.initData();
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch((err) => {
					this.loading = false;
				});
				setTimeout(()=>{
					uni.showToast({
						title: "发表成功",
						icon: "sucess"
					});
				},1000)
				getApp().eventRecord({
					keyWord: '吐槽页面-点击吐槽按钮',
					clickType: 'Button',
					jumpType: '其他',
					jumpDesc: '发送吐槽',
					result: '成功',
					startTime: new Date().getTime(),
					endTime: new Date().getTime()
				});
			},
			// 获取用户信息的回调
			getUserProfile (e) {
				app.updataUserInfo().then(()=>{
					this.userInfo = app.globalData.userInfo;
					this.disAdd();
				});
			},
			// 计算图片在宽度100%情况下，等比例的高
			imgLoad (e) {
				this.imgHeight = this.screenWidth * (e.detail.height / e.detail.width);
				console.log('e.detail', this.imgHeight);
			},
			goBack () {
				uni.navigateBack({
					fail: (err)=> {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				});
			},
			// 通过id获取单篇文章信息 // 记录文章访问量
			informationviews () {
				apis.homeApis.informationviews({ id: this.id }).then((res) => {
					this.item = res.data;
					this.item.createTime = form.timestampToTime(this.item.createTime, "date");
					this.item.content = form.richText(this.item.content);
					// #ifdef MP-ALIPAY
					this.item.content =form.parseHtml(this.item.content)
					// #endif
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.detail {
		width: 100%;
		background-color: #fff;
	}
	.abso-box {
		box-sizing: border-box;
		position: absolute;
		width: 100%;
		background-color: #fff;
		border-radius: 50rpx 50rpx 0 0;
		box-shadow: 0px 4rpx 25rpx 0px rgba(18,121,240,0.40); 
	}
	.title-box {
		padding: 50rpx 34rpx 20rpx 34rpx;
	}
	.title-box-text {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 20rpx;
	}
	.box-content {
		padding: 0rpx 34rpx 150rpx 34rpx;
	}
	rich-text {
	    min-height: 500px;
	    width: calc(100% - 70rpx);
	    overflow-wrap: break-word;
		word-break: break-all;
	}
	.msg-content {
		margin: 40rpx 34rpx 20rpx 34rpx;
	}
	.msg-item {
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333333;
		padding-bottom: 34rpx;
		border-bottom: 2rpx solid #eee;
	}
	.item-time {
		font-size: 24rpx;
		color: #C7C7C7;
	}
	.item-content {
		margin-top: 16rpx;
		word-break: break-all;
		color:#444444;
		font-size: 28rpx;
	}
	
	.item-ava {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		box-shadow: 0px 4rpx 4rpx 0px rgba(0,0,0,0.1); 
	}
	.item-ava image{
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
	}
	.share-view {
		position: fixed;
		width: calc(100% - 68rpx);
		left: 0;
		bottom: 37rpx;
		border-radius: 64rpx;
		text-align: center;
		background-color: #fff;
		margin: 0 34rpx;
		box-shadow: 0px 10rpx 28rpx 0px rgba(0,0,0,0.06);
	}
	.share-view .wbk{
		position: relative;
		left: 45rpx;
		top: 42rpx;
	}
	.bottom-input {
		position: fixed;
		bottom: 37rpx;
		width: 682rpx;
		border-radius: 64rpx;
		box-sizing: border-box;
		height: 290rpx;
		background-color: #ffffff;
		left:34rpx;
		box-shadow: 0px 10rpx 28rpx 0px rgba(0,0,0,0.06);
	}
	.dis-input {
		width: 420rpx;
		height: 80rpx;
		padding: 10rpx 8rpx;
		margin-right: 20rpx;
		background-color: #f5f5f5;
		border-radius: 50px;
	}
	.dis-input input{
		width: 420rpx;
	}
	.share-view image {
		width: 54rpx;
		height: 54rpx;
	}
	.return-text {
		text-shadow: 2rpx 2rpx 2rpx rgba(0,0,0, 0.5);
	}
	
</style>
<style>
	.sub-btn {
		width: 152rpx!important;
		height: 80rpx!important;
		border-radius: 50rpx!important;
		font-size: 32rpx!important;
		font-weight: 600!important;
		background: linear-gradient(109deg,#4fa4ff 0%, #0e78ff 100%);
		box-shadow: 0px 4rpx 10rpx 0px rgba(65,153,255,0.30); 
	}
	.custom button {
		display: block!important;
		text-align: center!important;
	}
</style>