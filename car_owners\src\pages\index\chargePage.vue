<template>
	<view class="">
		<view class="front-page-nav--panel one">
			<view class="nav-panel-item"
			v-for="(item,index) in navList"
			:key="index">
			<!-- #ifdef APP-PLUS -->
			<view @click="handleNavItemClick(item)">
				<image :src="item.icon" class="nav-panel-item__icon"></image>
				<view class="nav-panel-item__name">
					<text v-text="item.name"></text>
				</view>
			</view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS -->
			<template v-if="item.type ==='getphonenumber' && verifyPhone">
				<button
				style="line-height: 100%;"				
				 @click="handleJump($event,item)">
					<image :src="item.icon" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						<text v-text="item.name"></text>
					</view>
				</button>
			</template>
			<template v-else>
				<view @click="handleNavItemClick(item)">
					<image :src="item.icon" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						<text v-text="item.name"></text>
					</view>
				</view>
			</template>
			<!-- #endif -->
			</view>
		</view>
		<view :class="{'chargeList':true,}" :active="chargeChoose">
			<view style="display: flex;justify-content:flex-start;padding: 7rpx;">
			</view>
			
			<view :class="{'flex-row':true,'scroll-top-fixed':isTop}" :style="{top: navHeight+statusBarHeight+'px'}">
				<view @click="chargeChooseChange(0)" :class="{'title':true,'active':chargeChoose==0}">
					距离最近
					<view class="selected" v-show="chargeChoose===0"></view>
				</view>
				<view @click="chargeChooseChange(1)" :class="{'title':true,'active':chargeChoose==1}">
					预约充电
					<view class="selected" v-show="chargeChoose==1"></view>
				</view>
				<view @click="chargeChooseChange(2)" :class="{'title':true,'active':chargeChoose==2}">
					收藏电站
					<view class="selected" v-show="chargeChoose==2"></view>
				</view>
			</view>
			<view v-if="chargeContentList.length>0"   :class="{'chargeContent chargeContent-top':isTop,'chargeContent':!isTop,'chargeContent-min-height':chargeContentListLength}">
				<view style="padding-bottom: 24rpx" v-for="(item,index) in chargeContentList">
					<view class="charge-item" @click="toChargeList(item)">
						<view class="top">
							<view class="parkName-container">
								<view class="parkName">{{item.name}}</view>							
								<view v-if="item.releaseStatus==1" class="station-await">
								可预约
								</view>
							</view>

							<view class="distance">
								<image src="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" mode="aspectFit"></image>
								{{item.distance | formetDistance }}
							</view>
						</view>
						<view v-if="item.companyName || item.busineHours || (item.releaseStatus==1)" class="flex-row font-24">
							<view class="grey name" v-if="!appletConfiguration.hideCompanyLabel">
								{{item.companyName || '' }}
							</view>
						</view>
						<view class="middle">
							<view class="all">
								<view class="slow" v-if="item.slowAllCount">
								<view class="slow-text"></view>	空{{item.slowSpaceCount || 0}} <view class="grey">/{{item.slowAllCount || 0}}</view>
								</view>
								<view class="fast" v-if="item.quickyAllCount">
									<view class="fast-text"></view>	空{{item.quickySpaceCount || 0}} <view class="grey">/{{item.quickyAllCount || 0}}</view>
								</view>
							</view>
							<view class="priceall">
								<view class="text">￥</view>
								<view class="price">{{item.minFee}}</view>
								<view class="text">起</view>
							</view>
						</view>
						<view class="flex-wrap">
							<view class="station-label-item">
								{{item | formetBusineHours }}
							</view>
							<view class="station-label-item" v-for="label in item.label">
								{{ label }}
							</view>
						</view>
					</view>
				</view>
				<view class="list-end">{{isEndCharge ? '亲，到底了' : '加载中...'}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	import util from '../../common/utils/util.js'
	let app = getApp();
	export default {
		name:'ChargePage',
		props: {
			longitude: {
				type: Number,
				default: 0,
			},
			latitude: {
				type: Number,
				default: 0,
			},
			isAllowShow: {
				type: Boolean,
				default: false,
			},
			hasChargingOrder: {
				type: Boolean,
				default: false,
			},
			isEnd: {
				type: Boolean,
				default: false,
			},
			isScrollTop:{
				type: Boolean,
				default: false,
			},
			navHeight:{
				type:Number,
				default: 40,
			},
			statusBarHeight:{
				type:Number,
				default: 47,
			},
			areaName:{
				type:String
			},
			positionAreaName:{
				type:String
			},
			areaCode:{
				type:String
			},
			mobile:{
				type:String
			},
			appletConfiguration:{
				type: Object
			}


		
		},
		data() {
			return {
				childName:'charge',//首页子组件标识，用于下一页调用跳转方法
				chargeContentList: [],
				chargeChoose: 0,
				form: form,
				userInfo: app.globalData.userInfo, // 用户信息
				hotAreaList: [],
				pageCharge: 1,
				isEndCharge: false,
				scrollTop: 0,
				navList: [
					{
						name: '在充订单',
						icon: 'http://image.bolink.club/FnkvIqOS6BwAiknDLOpvT3L-UFG5',
						target: 'navigateTo',// 跳转方式
						path: '/pagesF/order/chargingOrder',// 跳转路径
						type: 'getphonenumber',// 容器类型 - 需要获取手机号
						flag: 'chargingOrder',// 功能标记，一般和type配合使用
						meta: {
							keyWord: '发现页-点击进入梯控',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '梯控项目选择',
						}
					},
					{
						name: '订单记录',
						icon: 'http://image.bolink.club/Fp21SrTJwWjPiCJ65iahefyQ2oUC',
						target: 'navigateTo',// 跳转方式
						path: '/pagesF/order/records',// 跳转路径
						type: 'getphonenumber',// 容器类型 - 需要获取手机号
						flag: 'orderRecord',// 功能标记，一般和type配合使用
						meta: {
							keyWord: '发现页-点击进入一键开门',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '一键开门',
						}
					},
					{
						name: '我要开票',
						icon: 'http://image.bolink.club/FrO70gm9UIxln7J5rt1ZVAxlEARO',
						target: 'navigateTo',// 跳转方式
						path: '/pagesE/invoice/invoiceRecord?flag=1',// 跳转路径
						type: 'getphonenumber',// 容器类型 - 需要获取手机号
						flag: 'invoiceRecord',// 功能标记，一般和type配合使用
						meta: {
							keyWord: '发现页-点击进入一键乘梯',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '一键乘梯',
						}
					},
					{
						name: '常见问题',
						icon: 'http://image.bolink.club/Fk4w_rH4GkjSCP-U2YEtLoRDkGZx',
						target: 'navigateTo',// 跳转方式
						path: '/pagesF/other/question',// 跳转路径
						type: 'null',// 容器类型 - 需要获取手机号
						flag: 'question',// 功能标记，一般和type配合使用
						meta: {
							keyWord: '发现页-点击进入一键乘梯',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '一键乘梯',
						}
					},
				]
			}
		},
		computed:{
			isTop(){
				return this.isScrollTop?true:false
			},
			chargeContentListLength(){
				let {chargeContentList}=this
				let res=this.isScrollTop||chargeContentList.length>=4?true:false				
				return res
			},
			verifyPhone() {
				let boo = true
				try {
					let mobile = this.mobile					
					if (mobile && String(mobile).length === 11) {
						boo = false
					} else {
						boo = true
					}
				} catch (e) {
					boo = true
				}
				return boo
			},
			
		},
		filters: {
			formetDistance(val){
				val = Number(val).toFixed(0)
				let str = val > 10 ? '10'+'km+'
					: val < 1 ? "<1km" 
					: val+'km'
				return str
			},
			formetBusineHours(item){
				let { openingTime, closingTime } = item
				return ((openingTime && closingTime) ? openingTime + '~' + closingTime : "24小时") + '营业' 
			}
		},
		methods: {
			//滚动底部触发
			scrolltolower() {
				this.pageCharge += 1
				if (this.isEndCharge) {
					this.pageCharge = 1
					return false
				}
				this.queryPowerStationList()
			},
			queryPowerStationList(flag) {
				let data = {
					longitude:this._props.longitude,
					latitude:this._props.latitude,
					distance: 5000,
					rp: 10,
					page: this.pageCharge || 1,
					areaCode:this.areaName===this.positionAreaName?'':this.areaCode
				}
				if(this.chargeChoose == 2){
					delete data.areaCode
					data.queryType = 1,
					data.phone = uni.getStorageSync('mobile')
					data.queryAll = 1
					if(!data.phone){
						this.chargeContentList = []
						return false
					}
				}else{
					data.isSupportAppointment = this.chargeChoose
				}
				apis.homeApis.queryPowerStationList(data).then(res => {

					let list = res.data.voList || []
					list.forEach(item=>{
						item.label = item.label ? JSON.parse(item.label) : []
					})
					if (!list || list.length <10) {
						this.isEndCharge = true
					}else{
						this.isEndCharge = false
					}
					if (data.page === 1) {
						this.chargeContentList = list;
					} else {
						this.chargeContentList = this.chargeContentList.concat(list);
					}
				})
			},
			toChargeList(item) {
				uni.navigateTo({
					url: `../../pagesB/map/chargeMessage?item=${encodeURIComponent(JSON.stringify(item))}`
				})
			},
			chargeChooseChange(e) {
				this.chargeChoose = e
				this.isEndCharge = false
				this.pageCharge = 0
				this.scrolltolower()
				
				// this.$emit('changeSupportAppointment',e)
			},
			// 获取热门区域的信息
			getHotArea() {
				let data = {
					areaCode: uni.getStorageSync('areaCode') || '',
					// #ifdef MP-WEIXIN
					adPosId: 19,
					// #endif
					// #ifdef MP-ALIPAY
					adPosId: 24,
					// #endif
					carType: 2
				}
				apis.homeApis.getad(data).then(res => {
					this.hotAreaList = res.data;
				}).catch(err => {

				})
			},
			// 点击导航栏子元素  
			handleNavItemClick(row) {
				let jumpPath = row.path;
				let startTime = new Date().getTime();
				// 页内跳转
				if (row.target === 'tab') {
					uni.switchTab({
						url: jumpPath
					});
					return 
				}else if(row.flag=="chargingOrder"){
					if(this._props.hasChargingOrder){
						jumpPath = '/pagesF/charge/state'
					}
				}
				let meta = row.meta;
				uni.navigateTo({
					url: jumpPath,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: meta.keyWord,
							clickType: meta.clickType,
							jumpType: meta.jumpType,
							jumpDesc: meta.jumpDesc,
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: startTime,
							endTime: new Date().getTime()
						});
					}
				})
			},
			// 处理获取手机号后的跳转
			async handleJump(evt,row) {				
				let res=await util.getMobile()
				if(res.flag===0){
					row=JSON.stringify(row)
					uni.setStorageSync("jumpRow",row)
					uni.navigateTo({
						url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=1&activeName=charge&hasChargingOrder=${this.hasChargingOrder}`,
					})
				}else{
					this.handleNavItemClick(row);
				}			
			
			},
			
		
			// 热门区域点击 跳转
			handleBannerClick(item) {
				uni.request({
					url: item.clickUrl
				})
				if (item.url && item.jumpType === 1) {
					getApp().jumpAd({
						url: item.url,
						keyWord: '发现页-热门区域'
					});
				} else if (item.url && item.jumpType === 2) {
					uni.navigateToMiniProgram({
						appId: item.wxId,
						path: item.url || '',
						complete: (res) => {}
					});
				} else if (item.url && item.jumpType === 3) {
					let startTime = new Date().getTime();
					uni.navigateTo({
						url: item.url,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: '首页-页内跳转',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '添加车辆页面',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				} else {
					this.hintModel();
				}
			},
			/**
			 * @desc model提示
			 * @param { String } text
			 */
			hintModel(text) {
				uni.showModal({
					title: '',
					showCancel: false,
					confirmText: '好的',
					content: text || '暂无此功能，敬请期待',
					success: function(res) {

					}
				});
			},
			// 获取用户信息的回调
			getUserProfile(e) {
				app.updataUserInfo().then(() => {
					this.userInfo = app.globalData.userInfo;
				});
			},
		}
	}
</script>

<style lang="less">
	.chargeList {
		 //height: auto;
		 height: 100%;

		.title {
			margin: 0 0 24rpx 40rpx;
			height: 56rpx;
			font-size: 32rpx;
			font-weight: 600;
			color: #B0B6C1;
			padding-top: 8rpx;
			line-height: 36rpx;
		}
		.selected{
			margin:12rpx auto 0 auto;
			width:88rpx;
			border-bottom:4rpx solid #0da062;

		}

		.active {
			color: #242E3E;
			font-size: 36rpx;
		}

		.chargeContent {
			width: calc(100vw - 52rpx);
			height:100%;
			//height: calc(100vh - 720rpx);
			margin: 0 26rpx 20rpx 26rpx;
			overflow-y: auto;
			// background-color: #FCF9FC;
			.charge-item {
				border-radius: 30rpx;
				padding: 0 24rpx 4rpx;
				box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);
				background-color: #fff;
				
			}
			padding-bottom: 120rpx;
		}
		.chargeContent-top{
			min-height:100vh;
		}
		.chargeContent-min-height{
			min-height:2000rpx;
		}
	}

	.bannerList {
		margin: 0 26rpx 20rpx 26rpx;
		height: 204rpx;

		swiper-item {
			border-radius: 30rpx;
		}

		image {
			width: 100%;
			height: 100%;
		}
	}

	.scroll-top-fixed{
		width:100%;
		position: fixed;
		top:165rpx;
		background-color:#fafafa;
		z-index:99;
		height:90rpx;
		line-height: 90rpx;
    padding-top: 10rpx;
	}
	.list-end{
		margin-bottom:70rpx;

	}

</style>
