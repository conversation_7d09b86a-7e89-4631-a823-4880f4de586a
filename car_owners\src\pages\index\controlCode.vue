<template>
	<view class="ladder-control-container">
		<view class="custom-tabs-nav"
			>
		</view>
		<view class="box-size"></view>
		<view class="ladder-control-panel">
			<template v-if="isOnlyType">
				<view class="flex juc marb-10">
					<view class="active-item flex  alc only-type">
						<van-icon size="32rpx" class="marr-10 titleImg"
							:name="qrcodeType===0?'https://image.bolink.club/FgEdbrcV0UFxRcyqj8xTlO4HzROX':'https://image.bolink.club/FqBWRpZYof0bNnPwHxLFsHpKUpKb'" />
						{{qrcodeType===0?'门禁':'梯控'}}
					</view>
				</view>
			</template>
			<template v-else>
				<view class="font-32 mar-10" v-if="qrcodeType===2">
					通行码
				</view>
				<view v-else class="flex-row row">
					<view class="qrcode-item" @click="setControl(0)">
						<view class="flex jue">
							<view :class="['qrcode-tab',qrcodeType===0?'active-item':'default-item']">
								<view class="flex alc">
									<van-icon size="32rpx" class="marr-10" v-show="qrcodeType===0"
										name="http://image.bolink.club/Fl8jVvzcyir8ez_jQiMpb7WlD8qV" />
									门禁
								</view>
							</view>
						</view>
					</view>
					<view class="qrcode-item" @click="setControl(1)">
						<view class="flex jus">
							<view :class="['qrcode-tab',qrcodeType===1?'active-item':'default-item']">
								<view class="flex alc">
									<van-icon size="32rpx" class="marr-10" v-show="qrcodeType===1"
										name="http://image.bolink.club/FifgrQv8djnJL6mhc4xfCm6VtnkM" />
									梯控
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<view v-if="qrcodeStatus === 1">
				<view class="qr-container">
					<view class="qr-code-img" @click.native="changeCodeType(qrcodeType)">
						<image class="qr-code-img-box" :src="qrImgSrc" v-if="alreadyOpened"></image>
					</view>
				</view>
				<view class="qrcode-foot flex-row font-24">
					
					<view class="qrcode-foot-item reflesh" @click.native="changeCodeType(qrcodeType)">
						<van-icon name="http://image.bolink.club/FmjyVQcoO8_DUiBFCVMenp7MxaGA" 
							size="24rpx" class="tip-arrow padr-10" />
						刷新
					</view>
				</view>
			</view>
			<view class="" v-else>
				<view class="qr-code-status">
					<image src="http://image.bolink.club/Fm2E6IFOql23JImGs1ZUGoG8URvN" mode="">
					</image>
					<view>
						{{ qrcodeStatus===2 ? '- 还没有开通哦 -':'- 暂时还不支持 -' }}
					</view>
				</view>
			</view>
			<view class="lc-select-list" v-if="qrcodeStatus!==2">
				<block v-if="qrcodeType!=1&&doorList.length>0">
					<bl-cell title="门禁权限" custom-cell-class="lc-custom-cell"
						:path="'/pagesI/control/accessControl?id='+currentProjectId" @click.native="handleDoor">
						<view slot="custom" class="c-select-list-item--custom">{{doorAuthText}}</view>
					</bl-cell>
					<view style="border-top:1px solid #F2F2F2"></view>

				</block>
				<block v-if="qrcodeType!=0&&mapList.length>0">
					<bl-cell title="梯控权限" custom-cell-class="lc-custom-cell"
						:path="'/pagesI/control/ladderControl?id='+currentProjectId" @click.native="handleElevator">
						<view slot="custom" class="c-select-list-item--custom">{{elevatorText}}</view>
						
					</bl-cell>
					<view style="border-top:1px solid #F2F2F2"></view>
					<bl-cell title="更换楼层" custom-cell-class="lc-custom-cell" :path="'/pagesI/control/ladderControl?type=1'" v-if="showChangeFloor"
						>
						<view slot="custom" class="c-select-list-item--custom"></view>
					</bl-cell>
					<view style="border-top:1px solid #F2F2F2" v-if="showChangeFloor"></view>
				
				</block>

			</view>
			<view class="flex100 juc">
				<view v-show="qrcodeStatus!=2" class="defalut-btn" @click="toVister">
					访客码
				</view>
			</view>
		</view>

		<canvas id="qrCode" canvas-id="qrCode" class="qr-code"></canvas>

	</view>
</template>
<script>
	import util from "../../common/utils/util.js"
	import apis from "../../common/apis/index";
	import Draw from '../../components/sakura-canvas/js_sdk/draw'
	let draw = null;
	export default {
		props: {
			activeName:{
				type:String,
				required:true,
			},
			isShowSet:{
				type: [String,Boolean],
				required:true,
			}
		},
		data() {
			return {
				// 默认展示所有的门禁 - 梯控权限
				allList: 1,
				// 当前的门禁权限
				currentDoorAuth: '请选择',
				doorList: [],
				projectName: uni.getStorageSync('bl_community_name') || '请选择项目',
				// 当前项目ID
				currentProjectId: uni.getStorageSync('bl_community_ladder_id') || 0,
				timer: null,
				// 显示楼层选择
				showFloor: false,
				currentDate: '',
				showPopup: false,
				// 设置有效期
				isSupportSetTime: false,
				// 更换楼层
				isSupportUpdate: false,
				// 梯控开通状态
				alreadyOpened: false,
				qrImgSrc: '',
				// tab
				windowHeight: 0,
				statusBarHeight: 0,
				navHeight: 0,
				windowBottom: 0,
				mapList: [],
				expirationTime: 2,
				ladderControlForm: {
					currentLadderText: '',
					expirationTime: 5
				},
				// 二维码类型
				qrcodeType: 0, // 0:门禁,1:梯控,2:门禁梯控合一
				isOnlyType: false, // 门禁梯控只开通其一
				qrcodeStatus: 1, // 二维码状态 1正常，2未开通，3，不支持
				doorAuthText: '请选择',
				elevatorText: '请选择',
				showChangeFloor: false,
			}
		},
		watch: {
			'currentDoorAuth': {
				immediate:true,
				handler(val) {
					if (val === 'all') {
						return this.doorAuthText = '全部';
					} else if (val !== '' && val !== 'all') {
						let selectDoor = uni.getStorageSync('bl_door_control_array') || [],
							{
								doorKinds,
								doorList = []
							} = uni.getStorageSync('bl_door_access_group') || [],
							[first] = selectDoor,
							res = doorKinds == 1 && doorList[0] && doorList[0].deviceType == 6,
							other = doorKinds == 1 && doorList[0] && doorList[0].deviceType != 6,
							arr = val.split(',');
						if (arr.length > 0 && res) {
							return this.doorAuthText = arr.length + '个门'
						} else if (other) {
							uni.setStorageSync('bl_door_control_group', 'all')
							uni.setStorageSync('bl_door_control_array', doorList)
							return this.doorAuthText = '全部';
						} else if (first && first['name'] && val) {
							return this.doorAuthText = first['name']
						}
					}
					return this.doorAuthText = '请选择'
				}
			},
			'ladderControlForm': {
				handler(val) {
					let {
						deviceName,
						floorName,
						allList
					} = val;
					this.showChangeFloor = allList ? false : true
					return this.elevatorText = allList === 1 ? '全部' : `${deviceName+'号梯-'+floorName || '请选择'}`;

				}
			},
			'isShowSet':{
				immediate:true,
			async	handler(val){
					if(val){
						uni.removeStorageSync('bl_door_access_group')
						uni.removeStorageSync('bl_user_info')
						uni.removeStorageSync('bl_door_control_group')
						uni.removeStorageSync('bl_door_control_array')
						uni.removeStorageSync('bl_ladder_control_info')
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
				this.windowBottom = res.windowBottom;
			});
			await	this.setControl().then((res)=>{
				// this.currentDoorAuth = uni.getStorageSync('bl_door_control_group') || '';
				// this.selectEr()
			}).catch((err)=>{
				 console.log('errr',err)
			})
				

			}

				}
			}

		},
		onLoad(options) {
			uni.removeStorageSync('bl_door_access_group')
			uni.removeStorageSync('bl_user_info')
			uni.removeStorageSync('bl_door_control_group')
			uni.removeStorageSync('bl_door_control_array')
			uni.removeStorageSync('bl_ladder_control_info')
			this.qrcodeType = Number(options.qrcodeType) || this.qrcodeType || 0
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
				this.windowBottom = res.windowBottom;
			});
		},
		async onReady() {
			getApp().pagePathRecord();
		},
		onShow() {
			this.currentDoorAuth = uni.getStorageSync('bl_door_control_group') || ''
			this.currentProjectId = uni.getStorageSync('bl_community_ladder_id') || 0;
			this.projectName = uni.getStorageSync('bl_community_name') || '请选择项目';
		},
		methods: {
			toSet(){
				uni.redirectTo({
					url: '/pagesI/control/setUp'
				})

			},
			setControl(index) {
				return new Promise(async (resolve, reject)=>{
				this.qrcodeType = index == 1 ? 1 : index == 0 ? 0 : this.qrcodeType || 0
				let bl_door_access_group = uni.getStorageSync('bl_door_access_group')
				if(bl_door_access_group){
					this.dealPower(bl_door_access_group)
				}else{
					await this.getCityData().then((res)=>{
					resolve(res)

				}).catch((err)=>{
					reject(err)

				})

				}

				this.currentDoorAuth = uni.getStorageSync('bl_door_control_group') || '';

				})


			},
			toggleLadderControl(params) {
				this.$set(this, 'ladderControlForm', params);
			},
			handleDoor() {

			},
			handleElevator() {

			},
			handleCheck() {
				uni.navigateTo({
					url: './checkTime'
				})
			},
			handleProjectToggle() {
				uni.navigateTo({
					url: '/pagesI/index/index'
				})
			},
			// 打开更改楼层popup
			handleFloorOpen() {
				this.showFloor = true;
			},
			handlePickerCancel() {
				this.showFloor = false;
			},
			handlePickerConfirm(evt) {
				const selectedArray = evt.detail;
				if (selectedArray.length === 2) {
					this.ladderControlForm = {
						deviceId: selectedArray[0].id,
						deviceName: selectedArray[0].name,
						floorId: selectedArray[1].id,
						floorName: selectedArray[1].name,
						deviceType: selectedArray[1].deviceType,

					}
					uni.setStorageSync('bl_ladder_control_info', this.ladderControlForm);
					this.$set(this.ladderControlForm, "expirationTime", 5);
					this.selectEr();
				}
				this.showFloor = false;

			},

			//获取权限
			async getCityData() {
				let store = uni.getStorageSync('bl_user_info') || {}
				if (!store.id) {
					store = await getApp().getRemoteUserInfo() && uni
						.getStorageSync('bl_user_info')
				}
				if (store.status != 1) {
					return this.qrcodeStatus = 2
				}
				let params = {
					mobile: uni.getStorageSync('mobile'),
					projectId: this.currentProjectId,
					userId: store.id || -1
				}
				return new Promise((resolve, reject) => {
					apis.smartCommunityApis.selectbyid(params).then((res) => {
						if (res.code === 200) {

							uni.setStorageSync('bl_door_access_group', res.data); // 缓存门禁权限接口数据
							// doorList 门禁权限  tkList 梯控权限
							this.dealPower(res.data)
						} else {
							this.allList = 1;
							this.mapList = [];
							this.doorList = [];
							this.alreadyOpened = false;
							this.qrcodeStatus = 3
						}
						resolve(true)
					}).catch(err => {
						this.allList = 1;
						this.mapList = [];
						this.doorList = [];
						this.alreadyOpened = false;
						this.qrcodeStatus = 3
						reject(false)
					})
				})


			},
			/**
			 * @param {Object} data
			 * 处理权限梯控数据
			 * 当设备为倍加信是，二维码不区分类型
			 * doorTkIntegration（0：非加信,1：倍加信）
			 * 
			 */
			dealPower(data) {
				let user = uni.getStorageSync('bl_user_info'),
					qrcodeType = this.qrcodeType;
				if (user.status != 1) {
					return this.qrcodeStatus = 2
				}
				const bl_ladder_control_info = uni.getStorageSync('bl_ladder_control_info');
				this.ladderControlForm = {
					expirationTime: 5,
					...bl_ladder_control_info
				}
				let {
					doorList = [], // 门禁权限
						tkList = [], // 梯控权限
						allList, // 是否为所有权限
						doorTkIntegration, // 是否为倍加信
						tkKinds, // 梯控类型数量
						doorKinds, // 门禁类型数量
				} = data;
				// 先判断是不是只有倍加信，如果只有倍加信两码合一
				// 如果有多个设备厂商，判断原来是不是两码合一
				// 1: 两码合一： 判断是不是有门禁设备： 有门禁设备选择门禁设备：没有门禁设备选择梯控设备

				let hasTk = tkList && tkList.length > 0
				let hasDoor = doorList && doorList.length > 0
				qrcodeType = doorTkIntegration == 1 ?
					2 : !hasTk && hasDoor ? 0 : hasTk && !hasDoor ? 1 : qrcodeType
				this.qrcodeType = qrcodeType
				this.isOnlyType = (!hasTk && hasDoor) || (hasTk && !hasDoor) ? true : false
				this.mapList = tkList;
				this.doorList = doorList;
				this.allList = allList || [];
				this.alreadyOpened = true;
				if (qrcodeType != 0) {
					if (qrcodeType == 1 && tkList.length < 1) {
						return this.qrcodeStatus = 2
					}
					// 获取完权限信息，跟据缓存和allList，判断初始梯控权限
					// 当用户拥有所有权（allList == 1）：1: 梯控种类为1时：权限保留为全部；当梯控种类>1时,权限选择为第一个
					try {
						if (bl_ladder_control_info && bl_ladder_control_info.floorId) {
							// 如果缓存里有值，说明不是第一次进入，应用缓存内的信息
							let deviceId = bl_ladder_control_info.deviceId;
							// 检测缓存内的值，是否符合当前梯控权限
							for (let item of tkList) {
								if (Number(item.id) === Number(deviceId)) {
									let {
										deviceId,
										deviceName,
										floorId,
										floorName,
										deviceType
									} = bl_ladder_control_info;
									this.$set(this.ladderControlForm, 'deviceId', deviceId);
									this.$set(this.ladderControlForm, 'deviceName', deviceName);
									this.$set(this.ladderControlForm, 'floorId', floorId);
									this.$set(this.ladderControlForm, 'floorName', floorName);
									this.$set(this.ladderControlForm, 'deviceType', deviceType);
									break;
								}
							}
						} else if (tkKinds == 1 && allList === 1) {
							this.$set(this.ladderControlForm, 'deviceId', -1);
							this.$set(this.ladderControlForm, 'deviceName', '全部');
							this.$set(this.ladderControlForm, 'allList', 1);
							this.$set(this.ladderControlForm, 'deviceType', tkList[0].deviceType);
							uni.setStorageSync('bl_ladder_control_info', this.ladderControlForm);
						} else {
							if (tkList.length > 0) {
								let data = tkList[0];
								this.$set(this.ladderControlForm, 'deviceId', data.id);
								this.$set(this.ladderControlForm, 'deviceName', data.name);
								this.$set(this.ladderControlForm, 'deviceType', data.deviceType);
								if (data.children && data.children.length > 0) {
									this.$set(this.ladderControlForm, 'floorId', data.children[0].id);
									this.$set(this.ladderControlForm, 'floorName', data.children[0].name);
								}
								uni.setStorageSync('bl_ladder_control_info', this.ladderControlForm);
							}
						}
					} catch (e) {
						//TODO handle the exception
					}
				}
				if (qrcodeType != 1) {
					// 获取完权限信息，跟据缓存和allList，判断初始门禁权限
					if (qrcodeType == 0 && (!doorList || doorList.length < 1)) {
						return this.qrcodeStatus = 2
					}
					try {
						let bl_door_control_group = uni.getStorageSync('bl_door_control_group') || '';
						if(bl_door_control_group === 'all' && allList === 1){
							this.currentDoorAuth = 'all';
						}
						if (doorKinds == 1 && bl_door_control_group === 'all' && allList === 1) {
							this.currentDoorAuth = 'all';
						} else {
							if (doorList && doorList.length > 0) {
								let [doorItem] = doorList;
								if (!bl_door_control_group) {
									this.currentDoorAuth = String(doorItem.id);
									uni.setStorageSync('bl_door_control_group', this.currentDoorAuth);
									uni.setStorageSync('bl_door_control_array', [doorItem]);
								}
							}

						}
					} catch (e) {
						//TODO handle the exception
					}
				}
				if (qrcodeType == 2) {
					if (tkList.length < 1 && (!doorList || doorList.length < 1)) {
						this.qrcodeStatus = 2
					} else {
						this.qrcodeStatus = 1
					}
				}
				this.$set(this.ladderControlForm, 'expirationTime', 5);
				this.changeCodeType(this.qrcodeType);
			},
			async selectEr(index = this.qrcodeType) {
				let {
					deviceId,
					floorId,
					expirationTime,
					deviceType,
					allList
				} = this.ladderControlForm,
					api = 'selectEr',
					storage = uni.getStorageSync('bl_user_info') || await getApp().getRemoteUserInfo() && uni
					.getStorageSync('bl_user_info'),
					params = {
						mobile: uni.getStorageSync('mobile'),
						projectId: this.currentProjectId,
						currentTime: Math.floor((new Date().getTime()) / 1000),
						userId: storage.id,
					};
				params.expirationTime = 5
				// const currentDoorAuth = this.currentDoorAuth
				const currentDoorAuth = uni.getStorageSync('bl_door_control_group') || ''
				let doorList = []
				let temp = uni.getStorageSync('bl_door_access_group')
				if (currentDoorAuth == 'all') {
					doorList = temp && temp.doorList || []
				} else {
					doorList = uni.getStorageSync('bl_door_control_array') || []
				}
				if (index != 0) {
					// 梯控权限
					if (index == 1 && (!temp.tkList || temp.tkList.length < 1)) {
						return this.qrcodeStatus = 2
					}
					params.allList = allList || 0;
					params.allList != 1 ? params.deviceId = deviceId : '';
					params.floorId = floorId;
					params.deviceType = deviceType;
					api = 'selectTkEr'
				}
				if (index != 1) {
					// 门禁权限
					if (index == 0 && doorList.length < 0) {
						return this.qrcodeStatus = 2
					}
					params.doorList = JSON.stringify(doorList);
					const [first] = doorList
					params.deviceType = first && first.deviceType
					api = 'selectEr'
				}
				if (index == 2) {
					if ((!temp.tkList || temp.tkList.length < 1) && doorList.length < 0) {
						return this.qrcodeStatus = 2
					}
					api = 'selectDoorTkEr'

				}
				apis.smartCommunityApis[api](params).then((res) => {
					if (res.code === 200) {
						this.qrcodeStatus = 1
						this.drawQrCode(res.data);
						// 绘制成功后，如果在梯控页面，则开启定时器，五分钟更新一次
						this.autoUpdate();
					} else if (res.code == 500 && (res.message == 2 || res.message == 3)) {
						this.qrcodeStatus = res.message
					}
				}).catch(err => {
					uni.showModal({
						title: '提示',
						showCancel: false,
						content: '请求失败，请重试',
						success: (res) => {}
					});
					this.alreadyOpened = 3
				})
			},
			autoUpdate() {
				clearInterval(this.timer)
				if (this.menberType === 0) {
					this.timer = setInterval(() => {
						this.selectEr();
					}, 5000 * 60)
				}
			},
			goBack() {
				clearInterval(this.timer);
				uni.switchTab({
					url: '/pages/index/index'
				});
			},
			async drawQrCode(text = "123") {
				draw = new Draw({
					width: 235,
					height: 235,
					canvasId: "qrCode",
					_this: this,
				})
				// 绘制二维码
				draw.drawQrCode({
					x: 0, // x轴方向 默认 0
					y: 0, // y轴方向 默认 0
					size: 235, // 二维码的大小 默认100
					text: text, // 二维码内容 默认''
					background: '#ffffff', // 二维码背景色 默认#ffffff
					foreground: '#000000', // 二维码前景色 默认#000000
					pdground: '#000000', // 二维码角标色 默认 #000000
					lv: 3, // 容错级别(一般不需要调它) 默认值是3
					windowAlign: 'none', // 二维码在窗口(整个画布的宽度)对齐的方式 默认: none 可选 居中: center 右边: right
				})
				await draw.canvasDraw().then(res => {

					if (res.success) {
						this.qrImgSrc = res.data;
					} else {
						this.qrImgSrc = '';
					}
				}).catch(err => {
					console.error('draw err: ', err)
				})
			},
			dateFormate(n = 5) {
				let c = (new Date().getTime()) + n * 60 * 1000
				return util.formatDate(c, "yyyy-MM-dd hh:mm")
			},
			changeStrToMinutes(str) {
				let arrminutes = str;
				if (arrminutes.length == 2) {
					let minutes = parseInt(arrminutes[0]) * 60 + parseInt(arrminutes[1]);
					if (minutes === 0) {
						minutes = 5;
					}
					return minutes;
				} else {
					return 5;
				}
			},
			// 刷新重新获取权限
			reflesh() {
				this.getCityData()
				this.currentDoorAuth = uni.getStorageSync('bl_door_control_group') || '';
			},
			changeCodeType(index) {
				this.qrcodeType = index
				const {
					mapList,
					doorList
				} = this
				if (index == 1) {
					// 梯控码
					!mapList || mapList.length < 1 ? this.qrcodeStatus = 2 : this.selectEr(index)
				} else if (index == 0) {
					!doorList || doorList.length < 1 ? this.qrcodeStatus = 2 : this.selectEr(index)
					// 门禁码
				} else {
					(!doorList || doorList.length < 1) && (!mapList || mapList.length < 1) ? this.qrcodeStatus = 2: this
						.selectEr(index)
				}
			},
			getCode() {},
			toVister() {
				uni.navigateTo({
					url: `/pagesI/control/vister?qrcodeType=${this.qrcodeType}`,
				})
			},
			changeFloor() {
				uni.navigateTo({
					url: '/pagesI/control/ladderControl?type=1'
				})
			}

		}
	}
</script>
<style lang="less" scoped>
	page {
		width: 100%;
		min-height: 100%;
		background: linear-gradient(181deg, #2f5ae5 0%, #468cfe 100%);
	}
	.ladder-control-container{
		padding-bottom: 120rpx;
	}
	.defalut-btn {
		display: flex;
		justify-content: center;
		margin: 0 auto;
		padding: 20rpx 0;
		border-radius: 50rpx;
		font-size: 30rpx;
		background: #FFFFFF;
		border: 2rpx solid #049558;
		color:#049558;
		margin: 0 30rpx;

	}
	.lc-select-list {
		padding: 0 40rpx;
		display: block;
		text-align: left;
		// margin-top: 60rpx;

		.lc-custom-cell {			
			padding: 26rpx 0 !important;
			border-top: 1rpx solid #F2F2F2;
			// border-top: 1rpx solid rgba(246, 246, 246, 1);
			    /deep/ .bl-cell--text {
			        color:#000000;
			    }
		}

		.c-select-list-item--custom {
			margin-right: 24rpx;
			font-size: 28rpx;
			color:#808EA8;
		}
	}

	.refresh {
		position: fixed;
		bottom: 120rpx;
		/* #ifdef MP-WEIXIN */
		bottom: 60rpx;
		/* #endif */
		right: 40rpx;
		z-index: 9;
		display: flex;
		justify-items: center;
		align-items: center;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background-color: #fff;
		box-shadow: 0 10rpx 12rpx 2rpx rgba(0, 0, 0, 0.18);
		text-align: center;

		&-text {
			flex: 1;
			font-size: 26rpx;
			color: #468cfe;
		}
	}

	.refresh-hover {
		opacity: 0.7;
	}


	.bg-img {
		margin: 0;
		padding: 0;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		top: auto;
		z-index: 1;
		height: 708rpx;
		width: 100%;
	}

	.bottom-tips {
		position: absolute;
		bottom: 1%;
		// #ifndef H5
		bottom: 1.3%;
		// #endif
		left: 0;
		right: 0;
		top: auto;
		text-align: center;
		font-size: 26rpx;
		font-weight: 500;
		color: #FFFFFF;
	}

	.admin-check-time {
		position: absolute;
		bottom: 5.09%;
		left: 20%;
		right: 20%;
		top: auto;
		text-align: center;
	}

	.qr-code {
		position: fixed;
		left: -9999px;
		opacity: 0;
		width: 235px;
		height: 235px;
	}

	.qr-code-img {
		margin: 0;
		padding: 0;
		width: 100%;
		height: 100%;
	}

	.qr-code-status {
		font-size: 28rpx;
		color: #AAA8AC;

		.qr-code-tip {}
	}


	.ladder-control-footer {
		display: flex;
		justify-content: space-between;
		margin: 0 auto;
		width: 662rpx;
		height: 96rpx;
		line-height: 96rpx;
		font-weight: 700;
		text-align: center;

		.footer-btn {
			width: 662rpx;
			height: 96rpx;
			background: #ffffff;
			border-radius: 50rpx;
			color: #3970F0;
			transition: all 0.5s;
		}

		.footer-btn--update {
			width: 305rpx;
		}

		.footer-btn--time {
			width: 305rpx;
			border: 1rpx solid #FFFFFF;
			background: none;
			color: #fff;
		}

	}

	.ladder-control-panel {
		width: 702rpx;
		margin:-180rpx auto 0 auto;
		border-top:1rpx solid #F2F2F2;
		padding:43rpx 0 20rpx;
		display: block;
		background-color: #FFFFFF;
		border-radius: 0 0 38rpx 38rpx;
		text-align: center;
		z-index: 0;

		.qrcode-item {
			margin: 20rpx 0;
			width: 331rpx;

			.qrcode-tab {
				padding: 20rpx 40rpx;

				.qrcode-icon {
					line-height: 40rpx;
					margin-right: 5rpx;
					padding: 5rpx;
				}

				font-size: 32rpx;
			}



			.default-item {
				background-color: #FFF;
			}
		}

		.qrcode-foot {
			margin: 20rpx 90rpx 0 90rpx;
			color: #AAABBD;

			.qrcode-foot-item {
				width: 270rpx;
			}

			.change-pro {
				display: flex;
				justify-content: flex-start;
				
			}

			.reflesh {
				margin-left:115rpx;
				color:#049558;
				
			}
		}

		.lc-panel-title {
			line-height: 1;
			font-weight: 800;
			font-size: 32rpx;
			color: #000000;
			margin-bottom: 10rpx;
		}

		.lc-panel-tip {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #B0B6C1;
			margin-bottom: 10rpx;
		}


		.lc-panel-date {}

		.mar-50 {
			margin-bottom: 50rpx;
		}

		.qr-container {
			display: inline-block;
			width: 100%;
			height: 470rpx;
			margin-bottom: 0rpx;

			.qr-code-img-box {
				width: 100%;
				height: 100%;
				padding: 0 92rpx;
			}

			.qr-code-tip {
				margin-bottom: 140px;
			}
		}
	}

	.active-item {
		background: linear-gradient(153deg, #41E29D 0%, #049558 100%);
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(4,149,88,0.28);
		color: #FFF;
		border-radius: 50rpx;
	}

	.only-type {
		width: 200rpx;
		display: flex;
		justify-content: center;
		padding: 20rpx;
	}

	.custom-tabs-nav {
		display: flex;
		justify-items: center;
		justify-content: center;
		margin: 0 auto;
		font-size: 36rpx;
		font-weight: 500;
		text-align: LEFT;
		color: #abc7ff;

		.nav-item {
			width: 150rpx;
			height: 80rpx;
			transition: all .6s;

			&--tk {
				margin-right: 36rpx;
			}

			&--active {
				position: relative;
				font-size: 46rpx;
				font-weight: 800;
				color: #FFFFFF;

				&:after {
					position: absolute;
					bottom: -4rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 10rpx;
					opacity: 0.5;
					background: #ffffff;
					border-radius: 13rpx;
					content: "";

				}
			}
		}
	}


	.btn-box {
		padding-top: 20px;
	}

	.row{
		margin-bottom:15rpx;
	}

	.box-140 {
		height: 140rpx;
		width: 100%;
	}

	.refresh {
		position: fixed;
		bottom: 60rpx;
		right: 40rpx;
		z-index: 9;
		display: flex;
		justify-items: center;
		align-items: center;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background: #FFF;
		text-align: center;

		&-text {
			flex: 1;
			font-size: 26rpx;
			color: #3970F0;
		}
	}

	.refresh-hover {
		color: rgba(255, 255, 255, 0.6);
	}
	.set{
		margin-top:40rpx;
	}
	::v-deep .ladder-control-panel .qrcode-item .qrcode-tab{
		width:168;
		height:64rpx;
		line-height: 26rpx;
	}
	.titleImg{
		width:32rpx;
		height:32rpx;
	}
	.flex100{
		margin:40rpx 0;
	}

</style>
