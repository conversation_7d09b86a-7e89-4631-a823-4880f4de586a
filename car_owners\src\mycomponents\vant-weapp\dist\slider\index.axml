<view class='slider-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{customClass}} {{ utils.bem('slider', { disabled }) }}" style="{{ inactiveColor ? 'background:' + inactiveColor : '' }}" onTap='antmoveAction' data-antmove-tap='onClick'>
    <view class='van-slider__bar' style='{{ barStyle }};{{ computed.barStyle(barHeight, activeColor) }}'>
      <view class='van-slider__button-wrapper' catchTouchStart='antmoveAction' data-antmove-touchstart='onTouchStart' catchTouchMove='antmoveAction' data-antmove-touchmove='onTouchMove' catchTouchEnd='antmoveAction' data-antmove-touchend='onTouchEnd' catchTouchCancel='antmoveAction' data-antmove-touchcancel='onTouchEnd'>
        <slot a:if='{{ useButtonSlot }}' name='button'>
        </slot>
        <view a:else  class='van-slider__button'>
        </view>
      </view>
    </view>
  </view>
</view>