<view class='tag-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{customClass}} {{ utils.bem('tag', [type, size, { mark, plain, round }]) }}" style="{{ color && !plain ? 'background-color: ' + color + ';' : '' }}{{ textColor || (color && plain) ? 'color: ' + (textColor || color) : '' }}">
    <slot>
    </slot>
    <van-icon a:if='{{ closeable }}' name='cross' custom-class='van-tag__close' onClick='onClose' ref='saveChildRef1'>
    </van-icon>
  </view>
</view>