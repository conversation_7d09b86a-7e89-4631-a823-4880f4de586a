
import http from '../utils/http.js'
import BaseConfig from '../config/index.config.js'
export default {
	// 获取车场列表
	queryParkList: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/queryParkList', e, type, mask, loading, method),
	// 获取车位列表
	getAreaList: (e, type = true, mask = false, loading = false, method = 'GET') => http.request(
		'capp/api/prePark/areaList', e, type, mask, loading, method),
	// 获取车位列表	
	getParklotList: (e, type = true, mask = false, loading = false, method = 'GET') => http.request(
		'capp/api/prePark/parkLot', e, type, mask, loading, method),
	// 提交预约订单
	addPreInfo: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/addPreInfo', e, type, mask, loading, method),
	// 获取预约订单详情（车牌，车位，时间）
	getAppointmentDetail: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/preDetail', e, type, mask, loading, method),
	// 预约车位收费信息
	getAppointmentChargingInfo: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/preText', e, type, mask, loading, method),
	// 取消预约
	appointmentCancel: (e, type = true, mask = false, loading = false, method = 'GET') => http.request(
		'capp/api/prePark/cancel', e, type, mask, loading, method),
	// 预约记录
	getAppointmentRecords: (e, type = true, mask = false, loading = true, method = 'POST') => http.request(
		'capp/api/prePark/history', e, type, mask, loading, method),
	// 预约支付
	appointmentPay: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/pay', e, type, mask, loading, method),
	// 通过桩编码获取车位信息
	getParkLotByNo: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/prePark/getParkLotByNo', e, type, mask, loading, method),
	// 先走后付订单支付前查询(检查车场能不能合并支付)
	isLsPayChannel: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/pay/isLsPayChannel', e, type, mask, loading, method),
	// 获取车场信息
	getParkInfo: (e, type = true, mask = false, loading = false, method = 'GET') => http.request(
		'capp/api/prePark/getComInfo', e, type, mask, loading, method),
	// 查看商户固定码信息
	getShopCouponInfo: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/fixCode/query', e, type, mask, loading, method,isCompleteUrl),
	// 获取支付信息（查所选券支付金额）
	getCouponPayInfo: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/payInfo/query', e, type, mask, loading, method,isCompleteUrl),
	// 获取支付参数 （唤起支付）
	getCouponPayParams: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/pay', e, type, mask, loading, method,isCompleteUrl),
	// 领取优惠券（支付金额为0时）
	getFreeCoupon: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/confirmTicket', e, type, mask, loading, method,isCompleteUrl),
	// 获取支付优惠券（支付查询）
	getUserParkTicket: (e, type = true, mask = true, loading = false, method = 'POST') => http.request(
		'couponplatform/transmit/coupon/queryUserTicket', e, type, mask, loading, method),
	// H5获取支付优惠券（支付查询）
	getUserTicketH5: (e, type = true, mask = true, loading = false, method = 'POST') => http.request(
		'capp/transmit/coupon/queryUserTicket', e, type, mask, loading, method),
	// 获取订单总金额
	getOrderMoney: (e, type = true, mask = true, loading = false, method = 'POST') => http.request(
		'capp/transmit/coupon/getOrderMoney', e, type, mask, loading, method),
	// 领取|支付优惠券
	getCouponPayInfoNew: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/payInfo/get', e, type, mask, loading, method,isCompleteUrl),
	// 取消支付返回券额度
	recoveryTicket: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl1+
		'zld/coupon/recoveryTicket', e, type, mask, loading, method,isCompleteUrl),
	// 获取其心口令
	pswordObtain: (e, type = true, mask = true, loading = false, method = 'POST') => http.request(
		'capp/coupon/getPasswd3', e, type, mask, loading, method),
	// 小程序配置（是否获调用手机号组件）
	getMicroConfigPhone: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/user/getConfigApplet', e, type, mask, loading, method),
	// 取消支付（停车）
	closePayOrder: (e, type = true, mask = true, loading = false, method = 'POST') => http.request(
		'capp/api/pay/closePayOrder', e, type, mask, loading, method),
	// 查询支付宝停车助手数据
	getZfdNewAd: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/ad/getZfdNewAd', e, type, mask, loading, method),	
	// 支付宝停车助手统计
	pushZfbNewResult: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/ad/pushZfbNewResult', e, type, mask, loading, method),	
	// 查询h5停车查询车牌广告
	getad: (e, type = false, mask = true, loading = false, method = 'GET') => http.request(
		'unionapi/ad/getad', e, type, mask, loading, method),	
	// 查询h5停车订单广告
	getadcommon: (e, type = false, mask = true, loading = false, method = 'GET') => http.request(
		'unionapi/ad/getadcommon', e, type, mask, loading, method),	
	// 查询h5停车appid相关信息
	getPayH5Appid: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/miniprogram/pay_h5_appid', e, type, mask, loading, method),
	//h5查车场信息
	queryParkInfoByQrCodeId: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/miniprogram/queryParkInfoByQrCodeId', e, type, mask, loading, method),
	// 查询h5停车公众号网页授权
	getPayH5UserId: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/miniprogram/pay_h5_user_id', e, type, mask, loading, method),
	// h5停车订单支付
	payH5: (e, type = false, mask = true, loading = false, method = 'POST') => http.request(
		'unionapi/miniprogram/pay_h5', e, type, mask, loading, method),
	// 查询h5支付信息
	getPayDetails: (e, type = false, mask = true, loading = false, method = 'GET') => http.request(
			'unionapi/payDetails', e, type, mask, loading, method),	
	// 获取离线码停车缴费信息(小程序)
	postOfflineInfo: (e, type = true, mask = false, loading = true, method = 'POST') => http.request(
		'capp/chargingPilePay/microcode', e, type, mask, loading, method),
	// 云平台离线发起支付(小程序)
	postOfflinePay: (e, type = true, mask = false, loading = true, method = 'POST') => http.request(
		'capp/api/pay/offlinePay', e, type, mask, loading, method),
	// 获取离线码停车缴费信息(H5)
	getH5OrderInfo: (url, e, type = false, mask = false, loading = true, method = 'GET', isCompleteUrl=true) => http.request(
		url, e, type, mask, loading, method,isCompleteUrl),
	h5Pay: (url, e, type = false, mask = false, loading = true, method = 'POST', isCompleteUrl=true) => http.request(
		url, e, type, mask, loading, method,isCompleteUrl),
	//H5回调事件接口，加购信息挽回弹窗接口
	popUpBehavior: (e, type = true, mask = false, loading = true, method = 'POST') => http.request('unionapi/ad/popUpBehavior', e, type, mask, loading,method), 
}