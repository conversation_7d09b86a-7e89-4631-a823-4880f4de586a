<template>
	<view class="">
		<view class="front-page-nav--panel">
			<view class="nav-panel-item"
			v-for="(item,index) in navList"
			:key="index">
			<!-- <template v-if="!userInfo && item.checkUserInfo">
				<button style="line-height: 100%;" @click="getUserProfile">
					<image :src="item.icon" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						<text v-text="item.name"></text>
					</view>
				</button>
			</template> -->
			<template v-if="item.type ==='getphonenumber' && verifyPhone">
				<button
				style="line-height: 100%;"				
				 @click="handleJump($event,item)">
					<image :src="item.icon" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						<text v-text="item.name"></text>
					</view>
				</button>
			</template>
			<template v-else>
				<view @click="handleNavItemClick(item)">
					<image :src="item.icon" class="nav-panel-item__icon"></image>
					<view class="nav-panel-item__name">
						<text v-text="item.name"></text>
					</view>
				</view>
			</template>
	
			</view>
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import util from '../../common/utils/util.js'
let app = getApp();
	export default {
		name:'controlPage',
		props:{
			mobile:{
				type:Number||String
			}
		},
		data(){
			return {
				childName:'control',//首页子组件标识，用于下一页调用跳转方法
				userInfo: app.globalData.userInfo, // 用户信息
				navList: [
						{
							name: '通行码',
							icon: 'https://image.bolink.club/front_page_elevator_icon_2.png',
							target: 'navigateTo',// 跳转方式
							path: '/pagesI/control/index',// 跳转路径
							type: 'getphonenumber',// 容器类型 - 需要获取手机号
							flag: 'ladderControl',// 功能标记，一般和type配合使用
							meta: {
								keyWord: '发现页-点击进入梯控',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '梯控项目选择',
							}
						},
						{
							name: '一键开门',
							icon: 'https://image.bolink.club/front_page_open_door%402x.png',
							target: 'navigateTo',// 跳转方式
							path: '/pagesI/door/index',// 跳转路径
							type: 'getphonenumber',// 容器类型 - 需要获取手机号
							flag: 'openDoor',// 功能标记，一般和type配合使用
							meta: {
								keyWord: '发现页-点击进入一键开门',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '一键开门',
							}
						},
						{
							name: '一键乘梯',
							icon: 'https://image.bolink.club/front_page_one_touch_elevator.png',
							target: 'navigateTo',// 跳转方式
							path: '/pagesI/multiply/index',// 跳转路径
							type: 'getphonenumber',// 容器类型 - 需要获取手机号
							flag: 'multiply',// 功能标记，一般和type配合使用
							meta: {
								keyWord: '发现页-点击进入一键乘梯',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '一键乘梯',
							}
						},
				    {
				      name: '',
				      icon: '',
				      target: ''
				    },
						{
						  name: '',
						  icon: '',
						  target: ''
						},
					]
			}
		},
		computed: {
			verifyPhone() {
				let boo = true;
				try {
					let mobile = this.mobile;
					if (mobile && String(mobile).length === 11) {
						boo = false;
					} else {
						boo = true;
					}
				} catch (e) {
					boo = true;
				}
				return boo;
			},
		},
		onLoad(){
			this.mobile=uni.getStorageSync("mobile")
		},
		methods:{
			getElementIcon(){
				apis.homeApis.getElementIcon(['1']).then(res=>{
					if(res.data.length>0){
						let navList = res.data[0].icons
						let length = res.data[0].icons.length
						for (let i = length; i < 5; i++) {
							navList.push({
							name: '',
							icon: '',
							target: ''
						},)
						}
						this.navList=navList
					}

				})
			},
			getMobile() {
				let boo = true;
				try {
					let mobile = uni.getStorageSync('mobile');
					if (mobile && String(mobile).length === 11) {
						boo = false;
					}else {
						boo = true;
					}
				} catch(e) {
					boo = true;
				}
			
				return boo;
			},
			// 获取用户信息的回调
			getUserProfile(e) {
				app.updataUserInfo().then(() => {
					this.userInfo = app.globalData.userInfo;
				});
			},
			// 处理获取手机号后的跳转 
			async handleJump(evt,row) {
				let res=await util.getMobile()
				if(res.flag===0){
					row=JSON.stringify(row)
					uni.setStorageSync("jumpRow",row)
					uni.navigateTo({
						url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=1&activeName=control`,
					})
				}else{
					this.handleNavItemClick(row);
				}			
			
			},
			// 点击导航栏子元素 
			handleNavItemClick(row) {
				let startTime = new Date().getTime();
				if (row.flag === 'promotion') {return this.getOilAdPath(row);}
				if (row.icon === '') {return false}
				if (row.path === undefined || row.path === '') {return this.hintModel()}
			
				// 重新组装 path 数据
				let jumpPath = row.path;
				// 如果标识是"ladderControl"，并且有梯控的项目id,说明不是第一次使用
				if (row.flag === 'ladderControl') {
					let projectId = uni.getStorageSync('bl_community_ladder_id') || '';
					let projectName = uni.getStorageSync('bl_community_name') || '';
					if (projectId === '') {
						uni.navigateTo({
							url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
							complete: (res) => {
			
							}
						})
						return false;
					}
			
					jumpPath += `?id=${projectId}&name=${projectName}`;
				}
			
				// 如果标识是"openDoor",并且有项目id,说明不是第一次使用
				if (row.flag === 'openDoor' || row.flag === 'multiply'||row.flag==='notice'||row.flag==='phone'||row.flag==='report') {
					let bl_community_id = uni.getStorageSync('bl_community_id') || '';
					if (bl_community_id === '') {
						uni.navigateTo({
							url: `/pagesI/index/index?path=${encodeURIComponent(row.path)}`,
							complete: (res) => {
			
							}
						})
						return false;
					}
				}
			
				// 页内跳转
				if (row.target === 'tab') {
					uni.switchTab({
					    url: jumpPath
					});
				}
				else if (row.target === 'url') {
					if (row.path === 'placement_information') {
						if (Math.round(new Date()) > BaseConfig.releaseTime) {
							getApp().jumpWeiBao('weibao-车险服务导航');
						}else {
							uni.showToast({
							    title: '功能暂未开通，敬请期待',
								icon: 'none',
							    duration: 3000
							});
						}
						return false;
					}
					getApp().jumpAd({
						url: jumpPath,
						keyWord: row.meta.keyWord
					});
				}
				else if (row.target === 'mini_program') {
					let meta = row.meta;
					uni.navigateToMiniProgram({
						appId: meta.appId,
						path: jumpPath,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: meta.keyWord,
								clickType: 'Button',
								jumpType: meta.jumpType,
								jumpDesc: meta.jumpDesc,
								result: res.errMsg == 'navigateToMiniProgram:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				}
				else {
					let meta = row.meta;
					uni.navigateTo({
						url: jumpPath,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: meta.keyWord,
								clickType: meta.clickType,
								jumpType: meta.jumpType,
								jumpDesc: meta.jumpDesc,
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				}
			},
		}
	}
</script>

<style lang="less">

	.front-page-nav {
		padding-top: 32rpx;
		display: block;
		background-image: url('https://image.bolink.club/front_page_bg_2%402x.png');
		background-position: center top;
		background-repeat: no-repeat;
		background-size: 100% 339rpx;
	
		&--panel {
			// position: relative;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			padding: 20rpx 56rpx;
			transition: all 0.4s;
	
			.nav-panel-item {
				flex-shrink: 0;
				width: 18%;
				text-align: center;
				margin: 10rpx 52rpx 10rpx 0;
				&__icon {
					display: inline-block;
					padding: 0;
					margin: 0 0 10rpx 0;
					width: 96rpx;
					height: 96rpx;
				}
	
				&__name {
					display: block;
					width: 100%;
					height: 30rpx;
					line-height: 30rpx;
					font-size: 26rpx;
					font-weight: 500;
					color: #000000;
				}
			}
		}
	}
	.front-page-nav--panel view:nth-child(4n){
		margin: 10rpx 0 10rpx 0;

	}
</style>
