<view class='picker-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from="./index.axmlisSimple.sjs" name='isSimple'>
  </import-sjs>
  <view class='van-picker {{customClass}}'>
    <block a:if="{{ toolbarPosition === 'top' }}" a:for='{{ [{ showToolbar, cancelButtonText, title, confirmButtonText  }] }}' a:for-item='amitem' _is='toolbar' ref-numbers='{{ [{ showToolbar, cancelButtonText, title, confirmButtonText  }] }}'>
      <view a:if='{{ amitem.showToolbar }}' class='van-picker__toolbar {{toolbarClass}}'>
        <view class='van-picker__cancel' hover-class='van-picker__cancel--hover' hover-stay-time='70' data-type='cancel' onTap='antmoveAction' data-antmove-tap='emit'>
          {{ amitem.cancelButtonText }}
        </view>
        <view a:if='{{ amitem.title }}' class='van-picker__title van-ellipsis'>
          {{
      amitem.title
    }}
        </view>
        <view class='van-picker__confirm' hover-class='van-picker__confirm--hover' hover-stay-time='70' data-type='confirm' onTap='antmoveAction' data-antmove-tap='emit'>
          {{ amitem.confirmButtonText }}
        </view>
      </view>
    </block>
    <view a:if='{{ loading }}' class='van-picker__loading'>
      <custom-loading color='#1989fa' ref='saveChildRef1'>
      </custom-loading>
    </view>
    <view class='van-picker__columns' style='height: {{ itemHeight * visibleItemCount }}px' catchTouchMove='antmoveAction' data-antmove-touchmove='noop'>
      <picker-column class='van-picker__column' a:for='{{ isSimple(columns) ? [columns] : columns }}' a:key='{{*this}}' data-index='{{ index }}' custom-class='{{columnClass}}' value-key='{{ valueKey }}' initial-options='{{ isSimple(columns) ? item : item.values }}' default-index='{{ item.defaultIndex || defaultIndex }}' item-height='{{ itemHeight }}' visible-item-count='{{ visibleItemCount }}' active-class='{{activeClass}}' onChange='onChange' ref-numbers='{{ isSimple(columns) ? [columns] : columns }}' ref='saveChildRef1'>
      </picker-column>
      <view class='van-picker__mask' style='background-size: 100% {{ (itemHeight * visibleItemCount - itemHeight) / 2 }}px'>
      </view>
      <view class='van-picker__frame van-hairline--top-bottom' style='height: {{ itemHeight }}px'>
      </view>
    </view>
    <block a:if="{{ toolbarPosition === 'bottom' }}" a:for='{{ [{ showToolbar, cancelButtonText, title, confirmButtonText  }] }}' a:for-item='amitem' _is='toolbar' ref-numbers='{{ [{ showToolbar, cancelButtonText, title, confirmButtonText  }] }}'>
      <view a:if='{{ amitem.showToolbar }}' class='van-picker__toolbar {{toolbarClass}}'>
        <view class='van-picker__cancel' hover-class='van-picker__cancel--hover' hover-stay-time='70' data-type='cancel' onTap='antmoveAction' data-antmove-tap='emit'>
          {{ amitem.cancelButtonText }}
        </view>
        <view a:if='{{ amitem.title }}' class='van-picker__title van-ellipsis'>
          {{
      amitem.title
    }}
        </view>
        <view class='van-picker__confirm' hover-class='van-picker__confirm--hover' hover-stay-time='70' data-type='confirm' onTap='antmoveAction' data-antmove-tap='emit'>
          {{ amitem.confirmButtonText }}
        </view>
      </view>
    </block>
  </view>
</view>