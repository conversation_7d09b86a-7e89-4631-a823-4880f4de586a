<template>
	<view v-show="showData" class="search-park">
		<!-- #ifndef MP-ALIPAY -->
		<view class="search-header" :style="{height:(statusBarHeight + navHeight + 27)+'px'}">
			<!-- 顶部菜单 start -->
			<view class="nav-bar-center-default" :style="{top: statusBarHeight+'px',height: navHeight+'px'}">
				<view class="title" :style="{height: navHeight+'px','line-height': navHeight+'px'}">
					<view class="flex jus alc padl-40"
						:style="{height: '100%',width: navHeight+'px','line-height': navHeight+'px'}" @click="toIndex"
						hover-class="park-hover-class">
						<van-icon name="arrow-left" size="36rpx" />
					</view>
					<view class="title--center">预约停车</view>
				</view>
			</view>
			<view class="flex100" :style="{height:(statusBarHeight + navHeight + 4)+'px'}" />
			<!-- 顶部菜单 end -->
		</view>
		<!-- #endif -->
		<!-- 车场搜索 start -->
		<view class="flex-row search-box">
			<!-- #ifdef MP-ALIPAY -->
			<view class="park-bg-b"></view>
			<!-- #endif -->
			<view class="flex-row search">
				<van-icon class="marl-20 marr-10" size="36rpx" name="search" />
				<view class="font-28 flex-1" @click="toSearch">{{searchWord}}</view>
			</view>
		</view>
		<!-- 车场搜索 end -->
		<view class="search-body">
			<!-- 车场列表 start -->
			<scroll-view v-if="parkList.length>0" scroll-y="true" class="park-list" refresher-enabled="true"
				lower-threshold="120" :refresher-triggered="isRefresher" @scrolltolower="scrolltolowerPark"
				@refresherrefresh="refresherrefreshFun" @refresherpulling="refresherpulling">
				<!-- @refresherpulling="refresherpulling" -->
				<view class="park-item-default default flex alc" v-for="(item,index) in parkList" hover-class="park-hover-class"
					@click="toAppointment(item)" :key="index">
					<view class="left flex-col font-32 flex-1">
						<view class="of-clamp1">
							{{item.name}}
						</view>
						<view class="address font-26 of-clamp1">
							{{item.address || '--'}}
						</view>
						<view class="flex-row flex-row font-26">
							<van-icon name="https://image.bolink.club/Fncpwr-yJ9M_be6NsQdawkj07lOI" size="27rpx" />
							<view class="font-26 park-num">
								{{item.emptyPlot}}
							</view>
							<van-icon name="https://image.bolink.club/Fo9eFiH2n32l7avC_7qh3Txpst6G"
								v-if="item.isExistChargePlie" class="marr-20" />
							<view class="flex-row" @click.stop="toNavigate(item)" hover-class="park-hover-class">
								<van-icon name="https://image.bolink.club/Flc2_EXgOfue70yH4MLOsxX4E_0x"
									class="marr-10" />
								{{item.dis | toFixed2}}公里
							</view>
						</view>
					</view>
					<view class="right">
						<van-icon name="arrow" size="48rpx" />
					</view>
				</view>
				<view class="list-end">{{parkListIsEnd ? '暂无更多~' : '加载中...'}}</view>
			</scroll-view>
			<view class="empty-box flex alc juc" v-else>
				<empty type="3" textTip="当前位置暂无可预约车场~" />
			</view>
			<!-- 车场列表 end -->
		</view>
		<view class="search-footer flex juc alc">
			<view class="footer-icon" @click="showOrderList" hover-class="park-hover-class">
			</view>
		</view>

		<!-- 预约记录 start -->
		<view @touchmove.stop.prevent>
			<van-popup :show="showOrder" position="bottom" class="appointment-record" @click="closeOrderList"
				@click-overlay="closeOrderList" :lock-scroll="false" hover-class="park-hover-class">
				<view class="order-list">
					<view class="title">已预约记录</view>
					<image class="close-icon" @click="closeOrderList"
						src="https://image.bolink.club/yima/order_detail_close_icon.png"></image>
					<scroll-view scroll-y="true" class="scroll-list" lower-threshold="300"
						@scrolltolower="scrolltolowerOrder">
						<view class="order-item flex-col" v-for="(item,index) in orderList" @click="toOrderDetail(item)"
							:key="index">
							<view class="flex-row marb-20">
								<view class="park-name of-clamp1 font-32">
									{{item.name}}
								</view>
								<view class="order-type flex-1 jue alc">
									<view class="flex-row alc">
										<template v-if="item.preStatus == 0">
											<van-icon name="https://image.bolink.club/FpHgLoq7LVK_Dafdwp0gS5H6Vl6T"
												class="marr-10" size="26rpx"></van-icon>
											<view class="wait font-26">已预约，待入场</view>
										</template>
										<template v-else-if="item.preStatus == 1">
											<van-icon name="https://image.bolink.club/FgDKBF43OhQy7Wr9W37WYeRZ1XEh"
												class="marr-10" size="26rpx"></van-icon>
											<view class="timeout font-26">超时未入场</view>
										</template>
										<template v-else-if="item.preStatus == 2">
											<van-icon name="https://image.bolink.club/FgDKBF43OhQy7Wr9W37WYeRZ1XEh"
												class="marr-10" size="26rpx"></van-icon>
											<view class="timeout font-26">预约已取消</view>
										</template>
										<template v-else-if="item.preStatus == 3">
											<van-icon name="https://image.bolink.club/FmLv3XPNi1yYmtIqF13qJziJb2QA"
												class="marr-10" size="26rpx"></van-icon>
											<view class="font-26">已入场，已完成</view>
										</template>
										<van-icon name="arrow" size="26rpx" />
									</view>
								</view>
							</view>
							<view class="flex-row">
								<view class="address of-clamp1 font-26">
									{{item.address || '--'}}
								</view>
								<view class="price fw-600 font-32">
									￥{{item.preMoney | toFixed2}}
								</view>
							</view>
							<view class="flex-row mart-10">
								<van-icon name="https://image.bolink.club/FiYcrfjc8sPyEPLlPaW2Fntf9kPk" class="marr-10"
									size="26rpx" />
								<view class="font-26 fw-400">
									{{item.preTime | formetDate}}
								</view>
							</view>
						</view>
						<view class="list-end">{{isOrderEnd ? '暂无更多~' : '加载中...'}}</view>
					</scroll-view>
				</view>
			</van-popup>
		</view>
		<!-- 预约记录 end -->
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import util from "../../common/utils/util";
	export default {
		data() {
			return {
				navHeight: 40,
				statusBarHeight: 47,
				scrollTop: 0, // 距离顶部距离
				searchWord: '', // 搜索框内容
				parkListIsEnd: false, // 车场列表是否全部查询
				showOrder: false, // 是否展示订单
				orderType: 1,
				orderList: [], // 订单列表
				parkList: [], // 车场列表
				latitude: '', // 当前位置纬度
				longitude: '', // 当前位置经度
				getLocationFail: 0, // 定位失败车数
				pageNum: 1,
				orderPageNum: 1, // 订单当前查询页
				orderPageSize: 5, // 订单每页查询条数 
				isOrderEnd: false, // 是否已加载所有订单
				showData: false, // 是否展示数据
				isRefresher: false,
			}
		},
		computed: {},
		filters: {
			// 保留两位数字
			toFixed2(val) {
				return val ? Number(val).toFixed(2) : '0.00'
			},
			// 格式化时间
			formetTime(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd hh:mm:ss') : ''
			},
			formetDate(val) {
				return val ? util.formatDate(val * 1000, 'yyyy-MM-dd c') : ''
			},
		},
		onLoad(options) {
			this.showData = false;
			uni.showLoading({
				title: '加载中',
			})
			console.log('options----->', options);
			getApp().getSystemInfo().then(res => {
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight || res.titleBarHeight;
			});
			// // 如果从搜索页面跳转过来，则使用返回的数据，不重新定位
			if ('info' in options) {
				let info = JSON.parse(options.info)
				console.log('info----->', info);
				this.searchWord = info.detail;
				this.longitude = info.location && info.location.lng;
				this.latitude = info.location && info.location.lat;
				this.pageNum = 1;
				this.getParkList();
			} else {
				this.getLocation();
			}
		},
		onShow() {
			if(this.showOrder){
				this.showOrderList()
			}
		},
		onPullDownRefresh() {
			this.pageNum = 1;
			this.parkListIsEnd = false;
			this.getParkList();
		},
		methods: {
			// 获取位置信息
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (r) => {
						console.log('当前位置的经度：' + r.longitude);
						console.log('当前位置的纬度：' + r.latitude);
						this.longitude = r.longitude;
						this.latitude = r.latitude;
						this.searchWord = '';
						uni.setStorageSync('longitude', r.longitude)
						uni.setStorageSync('latitude', r.latitude)
						this.getPositionName()
					},
					fail: (err) => {
						this.getLocationFail += 1;
						if (this.getLocationFail > 5) {
							Dialog.confirm({
								title: '打开设置授权',
								message: "获取定位失败，请检查是否授权。",
							}).then(() => {
								// on confirm
								uni.openSetting({
									success: (res) => {
										if (res.authSetting['scope.userLocation']) {
											this.getLocation();
										}
									}
								})
							}).catch(() => {
								// on cancel
							});
						}
					}
				});
			},

			// 预约
			toAppointment(info) {
				util.reNavigateTo(`/pagesC/appointment/appointment?info=${JSON.stringify(info)}`);
			},

			// 获取位置名称
			getPositionName() {
				let data = {
					get_poi: 1,
					location: this.latitude + ',' + this.longitude,
					token: uni.getStorageSync('token'),
					path: "/place/geocoder",
				}
				apis.homeApis.mapService(data).then(res => {
					this.pageNum = 1;
					let list = res.result.pois || [];
					this.searchWord = list[0] && list[0].title;
					this.getParkList();
				}).catch(err => {
					this.pageNum = 1;
					this.getParkList();
				})
			},

			// 获取车场列表
			getParkList() {
				const that = this;
				if (that.parkListIsEnd) {
					setTimeout(() => {
						that.isRefresher = false;
					}, 1000)
					return false;
				}
				let params = {
					lat: this.latitude || uni.getStorageSync('latitude'),
					lon: this.longitude || uni.getStorageInfoSync('longitude'),
					pageNum: this.pageNum,
					pageSize: 10,
				};
				apis.park.queryParkList(params).then((res) => {
					setTimeout(() => {
						that.isRefresher = false;
					}, 1000)
					uni.hideLoading();
					uni.stopPullDownRefresh();
					this.showData = true;
					if (res.status === 200) {
						let data = res.data || [];
						this.showSwiper = true;
						if (data.length > 0) {
							if (this.pageNum === 1) {
								this.parkData = data;
								this.parkList = data;
							} else {
								this.parkData = [...this.parkData, ...data];
								this.parkList = [...this.parkList, ...data];
							}
							if (data.length < params.pageSize) {
								this.parkListIsEnd = true;
							}
						}
					}
				}).catch(() => {
					setTimeout(() => {
						that.isRefresher = false;
					}, 1000)
					uni.hideLoading()
					uni.stopPullDownRefresh();
					that.showData = true;
					uni.showToast({
						title: '网络超时，请稍后重试!',
						icon: 'none',
					});
				})
			},

			// 返回首页
			toIndex() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},

			// 搜索
			toSearch() {
				util.reNavigateTo(`/pagesC/park/nearpark?callBack=${encodeURIComponent('/pagesC/appointment/search')}&focus=true`);
			},

			toNavigate(item) {
				util.reNavigateTo(`/pagesC/park/nearparkDetail?item=${JSON.stringify(item)}&location=${JSON.stringify({longitude:this.longitude,latitude:this.latitude})}`);
			},

			toOrderDetail(item) {
				util.reNavigateTo(`/pagesC/appointment/detail?orderId=${item.id}`);
			},
			// 上拉刷新
			refresherrefreshFun() {
				// this.$set(this, 'isRefresher', true)
				// console.log('已触发','refresherrefreshFun',this.isRefresher);
				// this.pageNum = 1;
				// this.getParkList();
				// this.$set(this, 'isRefresher', false)
				const that = this;
				if (!this.isRefresher) {
					//下拉刷新，先变true再变false才能关闭
					this.parkListIsEnd = false;
					this.isRefresher = true;
					this.pageNum = 1;
					this.getParkList();
				}
			},
			// 上拉刷新
			refresherpulling() {
				// const that = this
				// if (!this.isRefresher) {
				// 	//下拉刷新，先变true再变false才能关闭
				// 	this.isRefresher = true;
				// 	this.pageNum = 1;
				// 	this.getParkList();
				// }
				// console.log('已触发','refresherpulling',this.isRefresher);
				// this.isRefresher = true
				// if(!this.isRefresher){
				// 	this.pageNum = 1;
				// 	this.getParkList();
				// }
			},

			// 下拉获取车场信息
			scrolltolowerPark() {
				this.pageNum += 1;
				this.getParkList();
			},

			// 下拉获取订单信息
			scrolltolowerOrder() {
				if (this.isOrderEnd) {
					return false;
				}
				this.orderPageNum += 1;
				this.getOrderList();
			},
			getOrderList() {
				let params = {
					openid: uni.getStorageSync('openId'),
					pageNum: this.orderPageNum,
					pageSize: this.orderPageSize,
				}
				apis.park.getAppointmentRecords(params).then((res) => {
					let orderList = res.data || [];
					if (res.status === 200) {
						if (orderList.length < this.orderPageSize) {
							this.isOrderEnd = true;
						}
						this.orderList = [...this.orderList, ...orderList];
					}else if(res.status==-1){
						this.isOrderEnd = true;
					}
				}).catch(() => {

				})
			},

			// 关闭订单列表
			closeOrderList() {
				this.showOrder = false;
			},

			// 展示订单列表
			showOrderList() {
				this.orderPageNum = 1;
				this.orderList = [];
				this.getOrderList();
				this.showOrder = true;
			}

		}
	}
</script>

<style lang="scss" scoped>
	.search-park {
		width: 100%;
		height: 100%;
		overflow: hidden;
		background-color: #F8F8F8;

		.search-header {
			// background-image: linear-gradient(to right, #2F5AE5, #468CFE);
			background-color: #468CFE;
			width: 100%;

		}
		
		.search-box {
			height: 88rpx;
			line-height: 88rpx;
			/* #ifdef MP-ALIPAY */
			margin: 0 40rpx;
			/* #endif */
			/* #ifndef MP-ALIPAY */
			margin: -44rpx 40rpx 0;
			/* #endif */
			border-radius: 4rpx;
			
			.search{
				background-color: #fff;
				width: 100%;
				position: relative;
				z-index: 50;
			}
		
			input {
				width: 100%;
				height: 100%;
				font-size: 32rpx;
			}
		}
		
		.search-body {
			margin-top: 20rpx;
			/* #ifndef MP-ALIPAY */
			height: calc(100vh - 240rpx);
			/* #endif */
			/* #ifdef MP-ALIPAY */
			height: calc(100vh - 108rpx);
			/* #endif */
			.empty-box{
				height: calc(100vh - 420rpx);
			}
			.park-list {
				/* #ifndef MP-ALIPAY */
				height: calc(100vh - 240rpx);
				/* #endif */
				/* #ifdef MP-ALIPAY */
				height: calc(100vh - 108rpx);
				/* #endif */
				.list-end {
					padding-bottom: 128rpx;
				}
			}

		}

		.search-footer {
			position: fixed;
			height: 128rpx;
			bottom: 0rpx;

			.footer-icon {
				height: 80rpx;
				width: 240rpx;
				border-radius: 40rpx;
				background-image: url("https://image.bolink.club/FlFCVjITkrZ9tVZmSSKr7U9dI5g1");
				background-size: 100% 100%;
			}
		}
	}

	.appointment-record {
		.order-list {
			.title {
				margin-top: 44rpx;
				text-align: center;
				font-size: 32rpx;
				font-family: PingFang-SC-Bold, PingFang-SC;
				font-weight: bold;
				color: #242E3E;
			}

			.close-icon {
				position: absolute;
				top: 40rpx;
				right: 24rpx;
				width: 48rpx;
				height: 48rpx;
			}

			.scroll-list {
				padding: 44rpx 24rpx;
				height: 50vh;

				.park-name {
					width: 400rpx;
				}

				.order-item {
					background-color: #F3F6FF;
					border-radius: 5rpx;
					padding: 32rpx 24rpx 36rpx 32rpx;
					margin-bottom: 24rpx;

					.order-type {
						.wait {
							color: #F59A23;
						}

						.timeout {
							color: #A30014;
						}

						.complete {
							color: #5A667E;
						}
					}

					.address {
						width: 532rpx;
						color: #242E3E;
						line-height: 1.5;
					}
				}
			}

		}

	}

</style>
