<view class='icon-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./computed.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='{{customClass}} {{ computed.rootClass({ classPrefix, name }) }}' style='{{ computed.rootStyle({ customStyle, color, size }) }}' hidden='{{hidden ? false: true}}' data-score='{{score}}' onTap='antmoveAction' data-antmove-tap='onClick'>
    <van-info a:if='{{ info !== null || dot }}' dot='{{ dot }}' info='{{ info }}' custom-class='van-icon__info' ref='saveChildRef1'>
    </van-info>
    <image a:if='{{ computed.isImage(name) }}' src='{{ name }}' mode='aspectFit' class='van-icon__image'>
    </image>
  </view>
</view>