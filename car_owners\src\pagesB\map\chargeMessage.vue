<template>
	<view class="chageMap" v-show="showData">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<view class="custom-back-nav" @click.stop="goBack"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}">
			<view class="flex-row alc">
				<van-icon name="arrow-left" color="#000000" size="40rpx" />
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<custom-nav-bar title=""></custom-nav-bar>
		<!-- #endif -->
		<view class="map_container">
			<map class="map" id="map" :longitude="longitude" :latitude="latitude" :markers="markers" show-location
				enable-overlooking enable-3D :scale="scale" :polyline="polyline" @click="toChargeMap">
			</map>
		</view>
		<!-- 公告 start-->
		<view class='notice-wrap' v-show="chargeInfo.notice">
			<van-notice-bar mode="closeable" style="border-radius: 40rpx" speed="20" color="#FFF" left-icon="https://image.bolink.club/FqNyyMswAgTGAZ2l72CQyYogFSZN" :text="chargeInfo.notice" backgroundColor="#222E3F" />
		</view>
		<!-- 公告 end -->
		<view class="chargeInfo-container">
			<scroll-view class="chargeInfo-scroll" :scroll-y="true">
				<!-- #ifdef APP-PLUS -->
				<view style="width: 100%;height: 100rpx;"></view>
				<!-- #endif -->
				<view class="station-message">
					<view class="title fw-500 flex">
						<view class="flex-1">
							{{chargeInfo.name}}
						</view>
						<view class="icon-48" :class="otherInfo.collectionStation?'active-stars':'stars'" @click="collectionStation"></view>
					</view>
					<view v-if="chargeInfo.companyName || chargeInfo.busineHours" class="flex-row font-26 mart-20 marb-10 alc">
						<view class="grey name marr-20" v-if="showCompanyLabel">
							{{chargeInfo.companyName || '' }}
						</view>
						<view class="station-time-type">
							{{chargeInfo | formetBusineHours }}
						</view>
						<view class="station-await"  v-if="chargeInfo.releaseStatus==1">
							可预约
						</view>
					</view>
					<view class="flex-wrap">
						<view class="station-label-item" v-for="label in chargeInfo.label">
							{{ label }}
						</view>
					</view>
					<view class="image-list" v-if="pictures && pictures.length>0">
						<image class="image-item" v-for="(item,index) in pictures" :src="item" mode="" 
							@click="previewImage(index)">
						</image>
					</view>
					<view class="flex-row flex-100 mart-10 border-top">
						<view class="flex-1 flex-col">
							<view class="flex-row">
								<van-icon name="https://image.bolink.club/FqGsKNxqZn-0jMQJSeG7ySZt_pYA" color="#8E8D90"
									size="24rpx" />
								<view class="flex font-26 fw-500 marl-10">
									{{chargeInfo.address}}
								</view>
							</view>
							<view class="distance">
								距你 {{chargeInfo.distance | formetDistance }} 公里
							</view>
						</view>
						<view class="flex-col alc jue" @click="openLocation(chargeInfo)">
							<van-icon name="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" color="#8E8D90"
								size="44rpx" />
							<view class="font-26">
								导航
							</view>
						</view>
					</view>
				</view>
				<!-- <template  v-if="showPark">
					<view class="nav-default h-20"></view>
					<view class="parklot-list" v-if="showPark">
						<view class="flex-col padb-20">
							<view class="flex mart-30 marb-20">
								<view class="marl-30 font-36 fw-600">
									充电车位
								</view>
								<view class="flex-1 jue font-24 alc grey  marr-40" @click="toParkLotDetail">
									详情
									<van-icon name="arrow" color="#8E8D90" size="24rpx" />
								</view>
							</view>
							<view class="parklot-list-item flex-row">
								<view class="status-item of-rel" v-for="parkLot in parkingSpace.slowParkingSpace"
									@click="toReservation(parkLot)">
									<view :class="{'of-abs parklot-num font-24 grey':true,'free':parkLot.parkingStatus===0}">
										{{parkLot.parkingSpaceNo}}
									</view>
									<view class="icon-75"
										:class="[parkLot.parkingStatus?'appointment-occupy-icon':'appointment-free-icon']"
										mode="">
									</view>
								</view>
							</view>
						</view>
					</view>
				</template> -->
				<view class="nav-default h-20"></view>
				<!-- #ifdef MP-WEIXIN -->
				<view class="preferential"  v-if="openContact===1&&contactQrCode">
					<view>
						<view class="preferential-title">【车主充电交流群】</view>
						<view class="collecting-vouchers">充电有问题？车友来交流>>></view>
						<view class="preferential-tips">长按右侧二维码 <image src="http://image.bolink.club/Fs7Im8TpgvfEpAOKVXYUXtIbr8z6" class="hand"></image></view>
					</view>				
					<image :src="contactQrCode"  :show-menu-by-longpress="true" class="code"></image>
				</view>
				<view class="nav-default h-20"></view>
				<!-- #endif -->
				<view class="charge-list" @click="showGunList(1)">
					<view class="flex-row">
						<view class="flex jus alc title">
							充电枪
						</view>
						<view class="charge-detail flex jue font-24 alc grey">
							详情
							<van-icon name="arrow" color="#B0B6C1" size="24rpx" />
						</view>
					</view>
					<view class="flex-row mart-20 marb-10">
						<view class="charge-mian">
							<view class="default slow marr-20" v-if="chargeInfo.slowAllCount">
								{{chargeInfo.slowSpaceCount || 0}}空闲 | 共{{chargeInfo.slowAllCount || 0}}
							</view>
							<view class="default fast" v-if="chargeInfo.quickyAllCount">
								{{chargeInfo.quickySpaceCount || 0}}空闲 | 共{{chargeInfo.quickyAllCount || 0}}
							</view>
						</view>
					</view>
					<view class="status-list flex-nowrap" v-show="(chargeInfo.quickyAllCount+chargeInfo.slowAllCount) > 0">
						<view class="icon-75 gun-free-icon mar-5" v-for="i in gunStatus.freeCount" mode="">
						</view>
						<view class="icon-75 gun-busy-icon mar-5" v-for="i in gunStatus.busyCount" mode="">
						</view>
						<view class="icon-75 gun-occupy-icon mar-5" v-for="i in gunStatus.occupyCount" mode="">
						</view>
						<view class="icon-75 gun-fault-icon mar-5" v-for="i in gunStatus.faultCount" mode="">
						</view>
						<view class="icon-75 gun-offline-icon mar-5" v-for="i in gunStatus.offlineCount" mode="">
						</view>
					</view>
				</view>
				<view class="nav-default h-20"></view>
				<view class="chargeInfo"  style="height:300rpx">
					<view class="flex-row">
						<view class="flex jus alc title">电价信息</view>
						<view  class="flex jue alc">
							<view class="flex jue font-24 alc  grey" @click="toPriceDetail">
								详情
								<van-icon name="arrow" color="#B0B6C1" size="24rpx" />
							</view>
						</view>
					</view>
					<view class="middle">			
						<view class="flex-row alc">
							<view class="price">{{chargeInfo.minFee}}</view>
							<view class="marr-30 font-26 mart-10 fw-600">元/度</view>
						</view>
						<view class="flex-row  alc" v-show="isActivity">
							<view class="middle-preferential">优惠价</view>
							<view class="middle-preferential-price">{{ originalPrice }}元/度</view>
						</view>			

					</view>
					<view class="current-time">
						当前时段: {{chargeInfo.ctime}}~{{chargeInfo.etime}}
					</view>
					<view class="bottom font-26">
						电价{{chargeInfo.minCFee}}元/度 服务费{{chargeInfo.minSFee}}元/度
					</view>
				</view>
				<view class="nav-default h-20"></view>
				<view class="chargeInfo">
					<view class="flex-row">
						<view class="title flex jus alc">
							基本信息
						</view>
					</view>
					<view class="font-26 fw-400 marb-20 mart-20">
						开放时间：{{ openTime }}
					</view>
					<view class="font-26 fw-400 marb-20" v-if="otherInfo.supportInvoice">
						发票服务：可开票
					</view>
					<view class="font-26 fw-400" @click="handlePhone">
						客服电话：{{chargeInfo.configPhone||'400 1989098'}}
					</view>
				</view>
				<view class="nav-default h-40"></view>
				<!-- <view class="" @click.native="handlePhone">
					<view class="other-message flex alc juc font-24">
						<view class="image-box">
							<image class="flex100" src="https://image.bolink.club/FsFOokUss50T_mV3n4juPCy6RkMU" mode="widthFix"></image>
						</view>
					</view>
				</view> -->
			</scroll-view>
		</view>
		<view class="under">
			<!-- #ifdef MP-WEIXIN -->
			<view class="share" hover-class="park-hover-class" @click="shareStation">
				<view class="under-label">分享电站</view>
			</view>
			<!-- #endif -->	

		</view>
		<van-dialog id="van-dialog" />
		<!-- #ifdef MP-ALIPAY -->
		<van-popup :show="showGunListPopup" position="bottom" round="true" onClick-overlay="handleCancel">
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN || H5 -->
		<van-popup @click-overlay="showGunListPopup=false" :show="showGunListPopup" position="bottom" round="true"
				close-on-click-overlay>
		<!-- #endif -->
			<!-- <view class="gun-popup">
				<view class="popup-head flex-row">
					<view class="flex alc jus font-30 fw-600">
						详情
					</view>
					<view class="flex alc jue grey font-24">
						{{chargeInfo[type?'quickySpaceCount':'slowSpaceCount']}}空闲/共{{chargeInfo[type?'quickyAllCount':'slowAllCount']}}
					</view>
				</view>
				<view class="popup-body">
					<view class="gun-list" v-for="(gun, index) in gunList">
						<view class="gun-item" @click="toCharge(gun)">
							<view :class="['flex-row',index<gunList.length+1?'border':'']">
								<view class="flex-col">
									<view class="type-img" :class="gun.icon"></view>
								</view>
								<view class="flex-col">
									<view class="">
										<view class="font-24 fw-500 mart-5">
											电枪编号: {{gun.connectorId }}
										</view>
										<view class="grey font-24 mart-10 flex-row">
											{{ gun.connectorType | getGuntype }} | {{gun.power}}KW
											<view v-if="gun.status!==1&&gun.status!==10" class="type">
												忙碌
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view> -->
		</van-popup>
		<!-- 底部弹出 -->
		<!-- #ifdef MP-WEIXIN -->
		<van-popup @click-overlay="showSharePopup=false" :show="showSharePopup" position="center" round="true" close-on-click-overlay>
			<view class="poster-container">
				<image :src='shareChargeBgImage' class="poster-img"></image>
				<img class="qr-img" :src="qrImgSrc" alt="" srcset="">
				<img class="qr-img-down" src="http://image.bolink.club/FkjVrq3YW5iKGe7VD3cS8HuoCnh3"  @click="saveDrawQrCode(qrCodeText)">
				     
		</view>
		</van-popup>
		<van-popup @click-overlay="showShareSelect=false" :show="showShareSelect" position="bottom" round="true" close-on-click-overlay>
			<view class="flex-col mart-40 padb-30">
				<button  open-type="share">
					<view class="share-line border-bottom" hover-class="park-hover-class">
						分享好友
					</view> 
				</button>
				<view class="share-line" hover-class="park-hover-class" @click="drawQrCode">
					电站二维码
				</view>
			</view>
		</van-popup>
		<!-- #endif -->
		<view class="reserve-space"  v-if="chargeInfo.releaseStatus==1">
			<view  @click="toParkLotDetail" class="reserve-space-button">预约充电车位</view>
		</view>
		<canvas id="qrCode" canvas-id="qrCode" class="qr-code-container"></canvas>
		<canvas canvas-id="shareCanvas" class="share-canvas"></canvas>
		<canvas canvas-id="canvasName" class="canvasName"></canvas>
	</view>
</template>
<script>
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog'
	import util from '../../common/utils/util.js'
	import apis from "../../common/apis/index";
	import power from '../../common/utils/power.js';
	import Draw from '../../components/sakura-canvas/js_sdk/draw'
	let draw = null;
	import {
		qrcode
	} from '../../pagesF/static/utils/requcode.js'
	const app = getApp();
	export default {
		data() {
			return {
				saveQrImgSrc:'',//下载图片二维码地址
				userInfo: app.globalData.userInfo,
				chargeInfo: {},
				markers: [],
				latitude: '39.96402234127961', // 纬度
				longitude: '116.30649949969086', // 经度
				windowHeight: 0,
				statusBarHeight: 0,
				navHeight: 0,
				getLocationFail: 0,
				cfee:'',
				sfee:'',
				timesList: [],
				price: '0.00',
				mobile: '',
				time: 1,
				polyline: [{
					points: [],
					width: 10,
					color: '#09D78A',
					arrowLine: true
				}],
				longitudeLocation: '',
				latitudeLocation: '',
				showGunListPopup: '',
				toLongitude:'',
				toLatitude:'',
				pictures: [],
				showImage: false,
				gunType: [
					'https://image.bolink.club/FlYoi6fsEVG-DxfCFN0BqLv_wW6x', // 快充（空闲）
					'https://image.bolink.club/Fs1E03z-cK3XhrVzNY1NfWJM8YQ3', // 快充（非空闲）
					'https://image.bolink.club/Fg2cF0MIvV4w0dtB_mf0k-V8zYVs', // 慢充（非空闲）
					'https://image.bolink.club/Fn1_r7GdR70MkHfQG08qLfFpSw1M', // 慢充（空闲）
				],
				gunList: [],
				type: 1,
				parkingSpace: {},
				otherInfo: {} ,// 电站其他配置
				qrImgSrc: '', // 二维码图片地址
				showSharePopup: false, // 分享电站二维码弹窗
				showShareSelect: false, // 分享电站（选择）弹窗
				stationId: '', // 电站Id
				showData: false,
				isActivity:false,//电站优惠活动
				originalPrice:'',//原价
				contactQrCode:'',
				openContact:'',//客户联系二维码显示隐藏
				gunStatus: {
					freeCount: 0, // 空闲
					busyCount: 0, // 忙碌
					occupyCount: 0, // 占用
					faultCount: 0, // 故障
					offlineCount: 0, // 离线
				},
				shareChargeBgImage: 'https://image.bolink.club/FpApB6RiLpmAiN2jwjXcOXnvbOwO',
				shareChargeDownLoadBgImage: 'https://image.bolink.club/FrRLUfikoTLCiPwsKe263OYfd-8o',
				showCompanyLabel: true,
			}
		},
		computed: {
			scale() {
				let scale = ''
				let distance = this.chargeInfo.distance
				if (distance >= 1000) {
					scale = 5
				} else if (distance < 1000 && distance >= 500) {
					scale = 6
				} else if (distance < 500 && distance >= 200) {
					scale = 7
				} else if (distance < 200 && distance >= 100) {
					scale = 8
				} else if (distance < 100 && distance >= 50) {
					scale = 9
				} else if (distance < 50 && distance >= 20) {
					scale = 10
				} else if (distance < 20 && distance >= 10) {
					scale = 10
				} else if (distance < 10 && distance >= 2) {
					scale = 11
				} else if (distance < 2 && distance >= 1) {
					scale = 15
				} else {
					scale = 16
				}
				return scale
			},
			getGunCode(val) {
				let {} = val
			},
			showPark() {
				if(this.parkingSpace&&this.parkingSpace.slowParkingSpace){
					return this.parkingSpace.slowParkingSpace.length > 0
				}else{
					return false
				}
			},
			showFastPark() {
				if(this.parkingSpace&&this.parkingSpace.fastParkingSpace){
					return this.parkingSpace.fastParkingSpace.length > 0
				}else{
					return 0
				}

			},
			showSlowPark() {
				if(this.parkingSpace&&this.parkingSpace.slowParkingSpace){
					return this.parkingSpace.slowParkingSpace > 0
				}else{
					return 0
				}

			},
			// 故障
			faultCount() {
				let {
					slowFaultCount = 0, quickyFaultCount = 0
				} = this.chargeInfo
				return slowFaultCount + quickyFaultCount
			},
			// 占用
			occupyCount() {
				let {
					quickyOccupyCount = 0, slowOccupyCount = 0
				} = this.chargeInfo
				return slowOccupyCount + quickyOccupyCount
			},
			// 离线
			offlineCount() {
				let {
					slowOfflineCount = 0, quickyOfflineCount = 0
				} = this.chargeInfo
				return slowOfflineCount + quickyOfflineCount
			},
			// 空闲
			spaceCount() {
				let {
					slowSpaceCount = 0, quickySpaceCount = 0
				} = this.chargeInfo
				return slowSpaceCount + quickySpaceCount
			},
			// 时间
			openTime() {
				let {
					openingTime,
					closingTime,
				} = this.chargeInfo
				let str = (openingTime && closingTime && openingTime!='00:00' && closingTime !="24:00") ? openingTime + '~' + closingTime : '全天24小时'
				return str
			},
			verifyPhone() {
				let boo = true;
				try {
 					let mobile=uni.getStorageSync("mobile")
					if (mobile && String(mobile).length === 11) {
						boo = false;
					} else {
						boo = true;
					}
				} catch (e) {
					boo = true;
				}
				return boo;
			},

		},
		filters: {
			getGuntype(val) {
				//设备类型  类型 1 家用插座 2交流接口插座 3交流接口插头 4 直流接口枪头 5 无线充电座 6其他
				let name = val == 1 ? '家用插座' :
					val == 2 ? '交流接口插座' :
					val == 3 ? '交流接口插头' :
					val == 4 ? '直流接口枪头' :
					val == 5 ? '无线充电座' :
					val == 6 ? '其他' : '其他'
				return name
			},
			formetBusineHours(item) {
				let {
					openingTime,
					closingTime
				} = item
				return ((openingTime && closingTime) ? openingTime + '~' + closingTime : "24小时") + '营业'
			},
			formetDistance(val) {
				return Number(val).toFixed(2)
			}
		},
		onLoad(options) {
			this.showData = false
			if ('item' in options) {
				let item = JSON.parse(decodeURIComponent(decodeURIComponent(options.item)))
				this.pictures = item.pictures ? JSON.parse(item.pictures) : [] ,
				this.chargeInfo = item
				this.chargeInfo.minFee=(item.minFee-0).toFixed(4)
				this.chargeInfo.minCFee=(item.minCFee-0).toFixed(4)
				this.chargeInfo.minSFee=(item.minSFee-0).toFixed(4)
				this.initData()
				this.showData = true
			}
			if(options.longitude&&options.latitude){
				this.toLongitude=options.longitude-0
				this.toLatitude=options.latitude-0
			}
			if('powerStationId' in options){
				this.stationId = options.powerStationId
			}
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
			// #ifdef MP-WEIXIN
			let appletConfiguration = util.getAppletConfiguration();
			if(appletConfiguration){
				appletConfiguration.hideCompanyLabel && (this.showCompanyLabel = !appletConfiguration.hideCompanyLabel);
				appletConfiguration.shareChargeBgImage && (this.shareChargeBgImage = appletConfiguration.shareChargeBgImage);
				appletConfiguration.shareChargeDownLoadBgImage && (this.shareChargeDownLoadBgImage = appletConfiguration.shareChargeDownLoadBgImage);
			}
			// #endif
			// #ifdef MP-WEIXIN || MP-ALIPAY
			getApp().login().then(res => {
				this.getLocation();
				this.mobile = uni.getStorageSync('mobile');
			})
			// #endif
			// #ifdef MP-ALIPAY
			this.$scope.handleCancel = this.handleCancel.bind(this)
			// #endif
			// #ifdef H5 || APP-PLUS
			this.mobile = uni.getStorageSync('mobile');
			// #endif
			this.getParkData()
			this.getContact()
			this.getGunList()
		},
		onShow() {
			// 判断是否有token和session_key
			getApp().isloginSuccess();

		},
		onShareAppMessage(res) {
			let appletConfiguration = util.getAppletConfiguration()
			return {
				title: appletConfiguration && appletConfiguration.shareName || '一码App',
			}
		},
		methods: {
			getGunList() {
				let data = {
					powerStationId: this.chargeInfo.id || this.stationId,
				}
				apis.chargingPile.getConnectorList(data).then(res => {
					console.log(res)
					let {
						code,
						message,
						data = []
					} = res
					let gunStatus = {
						freeCount: 0, // 空闲
						busyCount: 0, // 忙碌
						occupyCount: 0, // 占用
						faultCount: 0, // 故障
						offlineCount: 0, // 离线
					}
					this.gunList = data.map((item, index) => {
						let {parkStatus,type} = item
						if(parkStatus == 2 && type==1){
							gunStatus.occupyCount += 1
						}else if(type==1){
							gunStatus.freeCount += 1
						}else if(type==2){
							gunStatus.busyCount += 1
						}else if(type==3){
							gunStatus.faultCount += 1
						}else if(type == 4){
							gunStatus.offlineCount += 1
						}
					})
					this.gunStatus = gunStatus
				})
			},
			
			// 查询客户联系
			getContact() {
				let id = this.chargeInfo.id || this.stationId,
					param = {
						id
					}
				apis.chargingPile.getContact(param).then(res => {
					if(res.code===200){
						this.contactQrCode=res.data.contactQrCode
						this.openContact=res.data.openContact
						
					}

				}).catch((err)=>{
					
				})
			
			},
			// 点击取消
			handleCancel() {
				this.showGunListPopup = false;
			},

			toPark(item) {
				if (item.parkingStatus) {

				} else {
					uni.navigateTo({
						url: '/pagesF/order/subscribe?item=' + JSON.stringify(item)
					})
				}
			},
			getParkData() {
				let id = this.chargeInfo.id || this.stationId,
					param = {
						id
					}
				apis.chargingPile.getParkData(param).then(res => {
					console.log(res)
					this.parkingSpace = res.data
				})

			},
			handlePhone() {
				uni.makePhoneCall({
					phoneNumber: this.chargeInfo.configPhone || '4001989098',
					// 成功回调
					success: (res) => {
						console.log(res, '调用成功!')
					},
					// 失败回调
					fail: (res) => {
						console.log(res, '调用失败!')
					}
				})
			},
			previewImage(index) {
				uni.previewImage({
					current: index, // 当前显示图片的链接/索引值
					urls: this.pictures, // 需要预览的图片链接列表，photoList要求必须是数组
					loop: true, // 是否可循环预览
					indicator: 'default'
				});
			},
			async toCharge(gun) {
				if (gun.status != 1 && gun.status != 10) return
				uni.setStorageSync('gunNo', gun.connectorId)
				uni.setStorageSync('pileCode', gun.chargeCode)
				this.showGunListPopup = false
				let cloudToken = await this.getCloudToken()
				this.getReductionMsg(cloudToken)
			},
			// 获取用户信息的回调
			getUserProfile(e) {
				app.updataUserInfo().then(() => {
					this.userInfo = app.globalData.userInfo;
				});
			},
			getPhoneNumber(e, name) {
				app.getPhoneNumber(e, this).then(res => {
					if (res) {
						this.mobile = res.mobile
					}
				})
			},
			openLocation(item) {
				console.log("item->", item);
				uni.openLocation({
					name: this.chargeInfo.name,
					address: this.chargeInfo.address || '',
					latitude: item.latitude,
					longitude: item.longitude,
				})
			},
			collectionStation(){
				apis.chargingPile.collectionStation({
					powerStationId: this.chargeInfo.id,
					phone: uni.getStorageSync('mobile')
				}).then(res => {
					console.log(res)
					if(res.code==200){
						this.getOtherInfo()
					}else{
						uni.showToast({
							title: res.message || '操作失败！',
							icon: 'none'
						})
					}
				})
			},
			queryBillingSet() {
				apis.chargingPile
				.queryCarBillingSet({
					powerStationId: this.chargeInfo.id,
				}).then(res => {
					this.timesList = res.data || []
					let nowDate = this.getTody()
					let now = new Date().getTime()
					this.timesList.forEach(item => {
						let btime = new Date(nowDate + ' ' + item.btime + ':00')
						let etime = new Date(nowDate + ' ' + item.etime + ':00')


						if(item.billType===0){
							item.color='#FF9812'
							item.title='尖'
						}else if(item.billType==1){
							item.color='#FF6E4B'
							item.title='峰'
						}else if(item.billType==2){
							item.color='#31BDFF'
							item.title='平'
						}else if(item.billType==3){
							item.color='#4DDC26'
							item.title='谷'
						}

						// if(item.activityCfee){								
						// 		item.activityCfee=((item.cfee-0) * (1 - (item.activityCfee-0))).toFixed(2)
						// 		item.activitySfee=((item.sfee-0) * (1 - (item.activitySfee-0))).toFixed(2)
						// 	}

						if (now < etime && now > btime) {
							this.price = (Number(item.sfee) + Number(item.cfee)).toFixed(4)
							item.active = true
							this.cfee=(item.cfee-0).toFixed(4) //电费
							this.sfee=(item.sfee-0).toFixed(4) //服务费
							this.originalPrice=(Number(item.sfee) + Number(item.cfee)).toFixed(4)
							this.isActivity=false
							if(item.activityCfee){
								this.isActivity=true
								this.cfee=(item.activityCfee-0).toFixed(4) //电费
								this.sfee=(item.activitySfee-0).toFixed(4) //服务费
								this.price=(this.cfee-0) + (this.sfee-0)
								this.price=this.price.toFixed(4)

							}
						}


					})
				}).catch(err=>{
					console.log('计费异常',err)
				})
			},
			getTody() {
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth() + 1;
				var day = date.getDate();
				if (month < 10) {
					month = "0" + month;
				}
				if (day < 10) {
					day = "0" + day;
				}
				return year + "/" + month + "/" + day;
			},
			toPriceDetail() {
				uni.navigateTo({
					url: '/pagesB/charge/priceDetail?timesList=' + JSON.stringify(this.timesList)
				})
			},
			toEquipList(data) {
				uni.navigateTo({
					url: `/pagesB/charge/equipList?stationName=${data.name}&id=${data.id}`
				})
			},
			getConnector(type) {
				let id = this.chargeInfo.id
				apis.chargingPile.getConnector({
					id,
					type,
				}).then(res => {
					console.log(res)
					if (res.code == 200) {
						let connectorInfoTbs = res.data.connectorInfoTbs || []
						connectorInfoTbs = connectorInfoTbs.map(gun => {
							gun.icon = type && (gun.status === 1 || gun.status === 10) ?
								'charge-fast-free-icon' : type ?
								'charge-fast-occupy-icon' : !type && (gun.status != 1 && gun.status !=
								10) ?
								'charge-slow-free-icon' : 'charge-slow-occupy-icon'
							return gun
						})
						this.gunList = connectorInfoTbs

					} else {
						this.gunList = []
					}
				}).catch(err => {
					this.gunList = []
				})
			},
			initData() {
				this.longitude = this.chargeInfo.longitude
				this.latitude = this.chargeInfo.latitude
				let x = parseInt(this.chargeInfo.name.length) * -6;
				let y = -75;
				let markers = [{
					id: 0,
					parkId: this.chargeInfo.parkId,
					latitude: this.chargeInfo.latitude,
					longitude: this.chargeInfo.longitude,
					iconPath: "../static/mapZhong.png",
					width: 25,
					height: 25,
				}]
				if (this.tolongitude) {
					markers.push({
						id: 1,
						latitude: this.tolatitude,
						longitude: this.tolongitude,
						iconPath: "../static/mapQi.png",
						width: 25,
						height: 25,
					})
				} else {
					markers.push({
						id: 1,
						latitude: this.latitudeLocation,
						longitude: this.longitudeLocation,
						iconPath: "../static/mapQi.png",
						width: 25,
						height: 25,
					})
				}
				this.markers = markers
				this.queryBillingSet()
				this.getOtherInfo()
			},
			
			getOtherInfo(powerStationId){
				let data = {
					powerStationId: this.chargeInfo.id,
					phone: uni.getStorageSync('mobile')
				}
				apis.chargingPile.queryStationDetails(data).then(res=>{
					this.otherInfo = res.data
				})
			},

			/* 获取减免信息 */
			getReductionMsg(cloudToken) {
				const param = {
					...cloudToken,
					chargeCode: this.pileCode || uni.getStorageSync('pileCode'),
				}
				apis.chargingPile.reductionMsg(param).then((res) => {
					if (res.code == 200) {
						uni.setStorageSync('reductionMsg', JSON.stringify(res.data))
						uni.navigateTo({
							url: '/pagesF/charge/Recharge',
						})
					} else if (res.code === 500) {
						uni.showToast({
							title: res.data.message,
							icon: 'none',
							duration: 2000,
						})
					} else if (res.code === 4002) {
						this.reRequestMessage(1)
					}
				})
			},


			/* 获取云平台token */
			getCloudToken() {
				return new Promise((resolve, reject) => {
					util.getCloudToken()
						.then((res) => {
							resolve(res)
						})
						.catch((err) => {
							reject(err)
						})
				})
			},

			/* token失效处理 */
			async reRequestMessage(type) {
				uni.setStorageSync('cloudToken', '')
				let cloudToken = await this.getCloudToken()
				if (!cloudToken.token) return
				if (type == 1) {
					this.getPileMessage(cloudToken)
				} else if (type == 2) {
					this.getReductionMsg(cloudToken)
				}
			},
			// 打开扫码功能
			async handleScanQr() {
				let that = this
				// 获取权限定位信息
				// #ifdef APP-PLUS
				let resp = await power.requestAndroidPermission('android.permission.CAMERA');
				if (resp == -1) { 
					return util.modify();
				}
				// #endif
				// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
				uni.scanCode({
					success: (res) => {
						console.log(res)
						util.dealQrcode(res.result || res).then(result => {
							if (result && result.code == 200) {
								that.getPileMessage()
							}
						}).catch(err => {
							uni.showToast({
								title: err,
								duration: 3000,
								icon: 'none'
							})
							uni.hideLoading();
						})
					},
					fail(err) {
						console.log(err)
					}
				})
				// #endif
				// #ifdef H5
				uni.chooseImage({
					sizeType: ['original'],
					count: 1,
					success(res) {
						uni.showLoading({
							title: '加载中......'
						})
						const tempFilePaths = res.tempFilePaths[0]
						qrcode.decode(tempFilePaths)
						qrcode.callback = code => {
							if (code == "error decoding QR Code") {
								uni.showToast({
									title: "无效的二维码，请重新上传!",
									duration: 2000,
									icon: 'none'
								})
								uni.hideLoading();
							} else {
								util.dealQrcode(code).then(result => {
									if (result && result.code == 200) {
										that.getPileMessage()
									}
								}).catch(err => {
									uni.showToast({
										title: err,
										duration: 3000,
										icon: 'none'
									})
									uni.hideLoading();
								})
							}
						}
					},
					fail: (err) => {

					}
				});
				// #endif
			},
			/* 获取充电桩信息 */
			getPileMessage() {
				util.getPileMessage().then().catch(err => {
					if (err && err.code == 4002) {
						uni.removeStorageSync('cloudToken')
						this.getPileMessage()
					} else {
						uni.showToast({
							title: err,
							icon: 'none',
							duration: 3000
						});
					}
				})
			},

			getMapService() {
				let longitude=this.toLongitude?this.toLongitude:this.longitudeLocation
				let latitude=this.toLatitude?this.toLatitude:this.latitudeLocation
				let data = {
					from: this.latitude + ',' + this.longitude,
					to: latitude + ',' + longitude,
					// token: JSON.parse(uni.getStorageSync('cloudToken')).token,
					token: uni.getStorageSync('token'),
					path: '/direction/driving',
					// #ifdef H5
					isH5Request: 1,
					phone: uni.getStorageSync('mobile'),
					// #endif
				}
				apis.homeApis.mapService(data).then(res => {
					let points = []
					let polyline = res.result.routes[0].polyline
					for (let i = 2; i < polyline.length; i++) {
						polyline[i] = polyline[i - 2] + polyline[i] / 1000000
					}
					for (let i = 0; i < polyline.length; i += 2) {
						let data = {
							latitude: polyline[i],
							longitude: polyline[i + 1]
						}
						points.push(data)
					}
					this.polyline[0].points = points.reverse()
				}).catch(err=>{})
			},
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (r) => {
						console.log('当前位置的经度：' + r.longitude);
						console.log('当前位置的纬度：' + r.latitude);
						console.log(r);
						this.longitudeLocation = r.longitude;
						this.latitudeLocation = r.latitude;
						this.getStationInfoById()
					},
					fail: (err) => {
						this.getStationInfoById()
						this.getLocationFail += 1;
						if (this.getLocationFail > 5) {
							Dialog.confirm({
								title: '打开设置授权',
								message: "获取定位失败，请检查是否授权。",
							}).then(() => {
								// on confirm
								uni.openSetting({
									success: (res) => {
										if (res.authSetting['scope.userLocation']) {
											this.getLocation();
										}
									}
								})
							}).catch(() => {
								// on cancel
							});
						}
					}
				});
			},
			// 获取站点信息
			getStationInfoById(){
				if(this.stationId){
					let param = {
						powerStationId: this.stationId,
						longitude: this.longitudeLocation,
						latitude: this.latitudeLocation,
					}
					apis.chargingPile.getStationInfoById(param).then(res => {
						if(res.code==200){
							let chargeInfo = res.data
							this.pictures = chargeInfo.pictures ? JSON.parse(chargeInfo.pictures) : []
							chargeInfo.label = chargeInfo.label ? JSON.parse(chargeInfo.label) : []
							this.chargeInfo = chargeInfo
							this.initData()
							this.getMapService()
							this.showData = true
						}
					}).catch(err=>{
						console.log(err)
						uni.showToast({
							title:'未查询到相应电站！',
							icon: 'none',
						})
					})
				}else{
					this.getMapService()
				}
			},
			// 点击地图触发
			mapTap(e) {
				this.longitude = e.detail.longitude;
				this.latitude = e.detail.latitude;
			},
			// 地图标点点击
			makertap(e) {
				console.log("e-->", e);
				this.openLocation(this.markers[e.detail.markerId])
			},
			goBack() {
				uni.switchTab({
					url: '/pages/index/index'
				})
				// const pages = getCurrentPages();
				// if (pages.length === 2) {
				// 	uni.navigateBack({
				// 		delta: 1
				// 	});
				// } else if (pages.length === 1) {
				// 	uni.switchTab({
				// 		url: '/pages/index/index',
				// 	})
				// } else {
				// 	uni.navigateBack({
				// 		delta: 1
				// 	});
				// }
			},
			// 枪列表页面
			showGunList(type) {
				uni.setStorageSync('chargeInfo',this.chargeInfo)
				let url = `/pagesF/charge/gunList?chargeInfo=${encodeURIComponent(JSON.stringify(this.chargeInfo))}`
				util.reNavigateTo(url)
				
			},
			async toParkLotDetail(type) {
				let parkInfo = {
					comid: this.chargeInfo.comid,
					powerStationId: this.chargeInfo.id,
				}
				let url = `/pagesC/appointment/choice?parkInfo=${JSON.stringify(parkInfo)}`
				if(this.verifyPhone){
					let res=await util.getMobile()
					if(res.flag===0){
						uni.setStorageSync('parkInfo',parkInfo)
						url = `/pagesA/agreementLogin/login?isEmpower=true&jumpType=3&jumpUrl=/pagesC/appointment/choice`
						util.reNavigateTo(url)
					}else{
						util.reNavigateTo(url)
					}
				}else{
					util.reNavigateTo(url)
				}


			},
			toChargeMap() {
				uni.navigateTo({
					url: `../../pagesB/map/chargeMap?item=${encodeURIComponent(JSON.stringify(this.chargeInfo))}`
				})
			},

			toAppointment(data) {
				console.log(data)
				const that = this
				if (data.parkingStatus) return false
				let tmplIds = [app.globalData.tmplIds[11], app.globalData.tmplIds[12], app.globalData.tmplIds[10]]
				// #ifdef MP-WEIXIN
				uni.requestSubscribeMessage({
					tmplIds: tmplIds,
					success: (res) => {
						let openid = uni.getStorageSync('openId');
						let status1 = res[tmplIds[0]] == "accept" ? 1 : 2;
						let status2 = res[tmplIds[1]] == "accept" ? 1 : 2;
						let status3 = res[tmplIds[2]] == "accept" ? 1 : 2;

						let params = [{
								priTmplId: tmplIds[0],
								openid: openid,
								status: status1
							},
							{
								priTmplId: tmplIds[1],
								openid: openid,
								status: status2
							},
							{
								priTmplId: tmplIds[2],
								openid: openid,
								status: status3
							}
						];
						console.log('fadingyue', params)
						apis.homeApis.subscribeSta(params).then((res) => {
							console.log(res, '结果')
						})
					},
					fail: (err) => {
						console.log('requestSubscribeMessage-err-->', err)
					},
					complete: () => {
						this.toSubscribe(data)
					},
				})
				// #endif
				// #ifdef MP-ALIPAY || H5 || APP-PLUS
				this.toSubscribe(data)
				// #endif
			},
			toSubscribe(data) {
				let {
					id,
					parkingSpaceNo,
					chargeSpeed,
					power,
					parkingStatus
				} = data
				let {
					appointmentBasis,
					address,
					id: parkId
				} = this.chargeInfo
				let params = {
					appointmentBasis,
					id,
					parkId,
					parkingSpaceNo,
					address,
					chargeSpeed,
					power,
					parkingStatus
				}
				uni.navigateTo({
					url: '/pagesF/order/subscribe?params=' + JSON.stringify(params)
				})
			},
			shareStation(){
				this.showShareSelect = true				       

			},
			// 绘制电站二维码
			async drawQrCode() {
				this.showShareSelect = false
				let text = this.chargeInfo.qrCode
				if(!text){
					uni.showToast({
						title: '未获取到二维码信息',
						icon: 'none'
					})
				}
				uni.showLoading({
					title: '正在生成电站二维码！'
				})
				draw = new Draw({
					width: 235,
					height: 235,
					canvasId: "qrCode",
					_this: this,
				})
				// 绘制二维码
				draw.drawQrCode({
					x: 0, // x轴方向 默认 0
					y: 0, // y轴方向 默认 0
					size: 235, // 二维码的大小 默认100
					text: text, // 二维码内容 默认''
					background: '#ffffff', // 二维码背景色 默认#ffffff
					foreground: '#000000', // 二维码前景色 默认#000000
					pdground: '#000000', // 二维码角标色 默认 #000000
					lv: 3, // 容错级别(一般不需要调它) 默认值是3
					windowAlign: 'none', // 二维码在窗口(整个画布的宽度)对齐的方式 默认: none 可选 居中: center 右边: right
				})
				try{
					let drawImg = await draw.canvasDraw()
					if(drawImg.success){
						let qrImgSrc = drawImg.data;
						const ctx = wx.createCanvasContext('shareCanvas')
						ctx.setTextAlign('center') // 文字居中
						ctx.setFontSize(20) // 文字字号：22px
						ctx.setFillStyle('#000000') // 文字颜色：黑色
						// ctx.fillText(`电站名称：${this.chargeInfo.name}`,170, 30)
						ctx.drawImage(qrImgSrc, 20,60, 300, 300)
						ctx.stroke()
						ctx.draw(false, () => {
							uni.canvasToTempFilePath({
								canvasId: 'shareCanvas',
								success: (res) => {
									this.tempFileImage(1)
									this.showSharePopup = true
								},
								fail: function() {
									//TODO
								}
							})
						})
					}
				}catch(e){
					console.log('失败原因',e)
					uni.hideLoading()
					uni.showToast({
						title:'生成二维码失败！'
					})
				}
			},
			
			// 生成临时图片地址  type 1 生成图片并打开弹框，2生成图片调下载接口
			tempFileImage(type) {
				let that = this
				uni.canvasToTempFilePath({
					canvasId: type===1?'shareCanvas':'canvasName',
					success: (res) => {									
						
						if(type===1){
							that.qrImgSrc = res.tempFilePath	
							that.showSharePopup = true
						}else{
							that.savePic(res.tempFilePath)
						}	
						uni.hideLoading()
						
					},
					fail: function() {
						uni.showToast({
							title:'生成二维码失败！'
						})
						uni.hideLoading()
					}
				})
			},
		async saveDrawQrCode() {
			let that = this
			let text = this.chargeInfo.qrCode
			draw = new Draw({
				width: 235,
				height: 235,
				canvasId: "qrCode",
				_this: this,
			})
			// 绘制二维码
			draw.drawQrCode({
				x: 0, // x轴方向 默认 0
				y: 0, // y轴方向 默认 0
				size: 235, // 二维码的大小 默认100
				text: text, // 二维码内容 默认''
				background: '#ffffff', // 二维码背景色 默认#ffffff
				foreground: '#000000', // 二维码前景色 默认#000000
				pdground: '#000000', // 二维码角标色 默认 #000000
				lv: 3, // 容错级别(一般不需要调它) 默认值是3
				windowAlign: 'none', // 二维码在窗口(整个画布的宽度)对齐的方式 默认: none 可选 居中: center 右边: right
			})
			await draw.canvasDraw().then(res => {
				if (res.success) {
					this.saveQrImgSrc = res.data
					this.init({
						qrImgSrc:this.saveQrImgSrc
					})
				} else {
					this.saveQrImgSrc = '';
				}
			}).catch(err => {
				console.error('draw err: ', err)
			})
		},
			init(params) {
				let that = this
				const wxGetImageInfo = this.promisify(uni.getImageInfo)
				Promise.all([
					wxGetImageInfo({
						src: that.shareChargeDownLoadBgImage // 背景图片
					}),
					wxGetImageInfo({
						src: params.qrImgSrc, // 二维码
					})
				]).then(res => {
					const ctx = wx.createCanvasContext('canvasName')
					// 底图
					let height =wx.getSystemInfoSync().windowHeight,
						width = wx.getSystemInfoSync().windowWidth,
						heightScale = height / 812,
						widthScale = width / 375
					ctx.drawImage(res[0].path, 0, 0, width, height)
					ctx.setTextAlign('center') // 文字居中
					ctx.setFontSize(14) // 文字字号：22px
					ctx.setFillStyle('#000000') // 文字颜色：黑色
					let rows = 1

					// 小程序码
					const qrImgSize = 280 * heightScale					
						let top = 270 * heightScale
						if ((height - 812) / 2 * 3 / 8 > 0) {
							top = 270 * heightScale + (height - 812) / 2 * 3 / 8
						}

					ctx.drawImage(res[1].path, (width - qrImgSize) / 2, top, qrImgSize, qrImgSize)
					ctx.stroke()
					// 绘图生成临时图片
					ctx.draw(false, () => {
						that.tempFileImage(2)

						
					})
				})
			},
			promisify(api) {
				return (options, ...params) => {
					return new Promise((resolve, reject) => {
						const extras = {
							success: resolve,
							fail: reject
						}

						api({
							...options,
							...extras
						}, ...params)
					})
				}
			},
			//保存
			savePic(filePath) {
				uni.showLoading({
					title: '正在保存'
				});
				uni.saveImageToPhotosAlbum({
					filePath: filePath,
					success: function() {
						uni.hideLoading()
						uni.showToast({
							title: '图片保存成功～',
							icon:'none'
						});
					},
					fail: function(e) {
						uni.hideLoading()
					},
					complete: function() {
						
					}
				});
			},
			
		},
		mounted() {
		}
	}
</script>

<style lang="less">
	::v-deep .van-notice-bar{
		border-radius: 40rpx;
	}
	.reserve-space{
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 110rpx;
		line-height: 110rpx;
		font-size: 28rpx;
		color: #fff;
		text-align: center;
		background: #ffffff;
		padding:20rpx  40rpx;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
		.reserve-space-button{
			border-radius: 50rpx;
			color: #1879fc;
			border: 1rpx solid #1879fc;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-weight: bold;
			
		}
	}
	.under {
		display: flex;
		flex-direction: column;
    position: fixed;
    bottom: 100rpx;
    right: 20rpx;
		/* #ifdef APP-PLUS */
		padding: 20rpx 30rpx 0;
		/* #endif */
		// #ifdef MP-ALIPAY
			bottom: 0;
			padding: 0 30rpx 10rpx 30rpx;
			z-index: 169;
		// #endif

		.share {
			width: 100rpx;
			height:100rpx;
			border-radius: 50%;
			background: #3d7af6;
			text-align: center;
			color: #FFFFFF;
			font-size: 24rpx;
			margin:20rpx 0;
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0px 12px 32px 0px rgba(57, 112, 240, 0.28);
			.under-label{
					word-wrap: break-word;
					text-align: center;
					width: 50rpx;
					line-height: 25rpx;
			}
		}

		.sys {
			padding-top: 20rpx;
			flex: 1;
			display: flex;
			justify-content: space-around;

			// ::v-deep.btn {
			// 	.custom-class.van-button {
			// 		width: 250rpx;
			// 		height: 80rpx;
			// 	}
			// }

			.default {
				height: 88rpx;
				line-height: 88rpx;
			}

			image {
				width: 32rpx;
				height: 32rpx;
				vertical-align: text-bottom;
				margin-right: 10rpx;
			}
		}

		.my-button {
			width: 250rpx;
			font-size: 32rpx !important;
			line-height: 88rpx;
			letter-spacing: 4rpx;
			box-shadow: 0px 4rpx 20rpx 0px rgba(49, 144, 251, 0.20);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.chargeInfo-container {
		position: absolute;
		border-radius: 30rpx 30rpx 0 0;
		width: 100%;
		height: calc(70vh);
		bottom: 0;
		overflow-x: auto;
		background-color: rgba(255,255,255,0);
		padding-bottom: 100rpx;
		/* #ifdef APP-PLUS */
		z-index: 999;
		/* #endif */
		
		.chargeInfo-scroll{
			height: calc(70vh);
		}
		
		.station-message {
			border-radius: 30rpx 30rpx 0 0;
			padding: 40rpx 40rpx 20rpx;
			background: #FFFFFF;

			.station-await {
				margin-left: 20rpx;
				padding: 0rpx 10rpx;
				font-size: 18rpx;
				color: #468cfe;
				border-radius: 20rpx 0 20rpx 0;
				width:80rpx;
				height: 32rpx;
				display: flex;
				justify-content: center;
				border:1rpx solid #468cfe;
				align-items: center;
				// #ifdef H5 
				width:106rpx;
				// #endif
			}
			.name {
				padding: 5rpx 0rpx;
			}

			.distance {
				margin-top: 24rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #828EA6;
			}

			.image-list {
				white-space: nowrap;
				width: calc(100vw - 60rpx);
				overflow: auto;
				height: 165rpx;
				margin: 25rpx 0;

				.image-item {
					height: 165rpx;
					width: 275rpx;
					border-radius: 20rpx;
					margin-right: 25rpx
				}
			}

			.image-list::-webkit-scrollbar {
				display: none;
			}

			.border-top {
				border-top: 1rpx solid #E6E8ED;
				padding-top: 20rpx;
			}
		}

		.charge-list {
			padding: 38rpx 40rpx;
			width: 100%;
			// padding-top: 20rpx;
			background: #FFFFFF;
			// box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

			.charge-mian {
				display: flex;
				width: 412rpx;
				text-align: center;
				color: #828EA6;
				font-size: 24rpx;
				line-height: 38px;
				justify-content: space-between;

				.default {
					width: 198rpx;
					padding-left: 30rpx;
					background-size: 198rpx 42rpx;
					background-position: center center;
					background-repeat: no-repeat;
				}

				.fast {
					background-image: url('https://image.bolink.club/FjoaPq1eUb9MjFIAU2CIaB5-YAdm');
					color: #45B5FF;
				}

				.slow {
					background-image: url('https://image.bolink.club/FrO_fIvwiirXkUkNL_sUa_4zbjMw');
					color: #BE18FF;
				}
			}

			.status-list {
				overflow-x: auto;
				overflow-y: hidden;
				height: 85rpx;

				.status-item {
					height: 75rpx;
					width: 75rpx;
				}

				.mar-5 {
					margin: 5rpx;
				}
			}



			.status-list::-webkit-scrollbar {
				display: none;
			}
		}

		.parklot-list {
			margin: 0;
			width: 100%;
			background: #FFFFFF;

			.parklot-list-item {
				margin: 0 40rpx;
				white-space: nowrap;
				width: calc(100vw - 60rpx);
				overflow: auto;
				height: 90rpx;

				.parklot-num {
					width: 80rpx;
					top: 10rpx;
					font-size: 26rpx;
					text-align: center;
					
					&.free{
						color: #4DDC26;
					}

					&.slow {
						color: #FFC06A;
					}

					&.fast {
						color: #44C99D;
					}
				}

				.status-item {
					height: 80rpx;
					width: 80rpx;
					margin-right: 10rpx;
				}
			}
		}

		.chargeInfo {
			height: 270rpx;
			width: 100%;
			background: #FFFFFF;
			// box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.1);
			padding: 40rpx;
			.middle {
				display: flex;
				align-items: center;
				margin: 10rpx 0 10rpx;

				.price {
					margin: 0;
					padding: 0;
					font-size: 56rpx;
					font-weight: bold;
					color:#df4839;
					margin-right: 10rpx;
				}

				.all {
					display: flex;
					width: 412rpx;
					text-align: center;
					color: #828EA6;
					font-size: 24rpx;
					line-height: 38px;
					justify-content: space-between;


				}
				.middle-preferential{
					color: #ffffff;
					background-color: #e75024;
					width: 95rpx;
					height: 35rpx;
					text-align: center;
					font-size: 22rpx;
					line-height: 35rpx;
					margin-right: 10rpx;
					border-top-left-radius: 30rpx;
					border-bottom-right-radius: 30rpx;
				}
				.middle-preferential-price{
					color:#706e6d;
					font-size: 28rpx;
					text-decoration: line-through;
				}
			}

			.bottom {
				display: flex;
				justify-content: space-between;
				color:#706e6d;

				.parkName {
					width: 420rpx;
					font-size: 32rpx;
					font-weight: 500;
					color: #242E3E;
					margin: 10rpx 0;
				}

				.address-b {
					padding-top: 20rpx;
					display: flex;
					flex-direction: column;
				}

				.address {
					margin-top: 20rpx;
					font-size: 26rpx;
					font-weight: 400;
					color: #828EA6;
				}

				.distance {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 500;
					color: #828EA6;
				}

				image {
					height: 32rpx;
					width: 32rpx;
					margin-left: 50%;
				}
			}
		}
		
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #242E3E;
		// margin: 8rpx 0 32rpx;
	}
	.current-time {
		height: 40rpx;
		line-height: 40rpx;
		background: #e6f0ff;
		border-radius: 24rpx;
		color: #9286ff;
		font-size: 26rpx;
		width:300rpx;
		max-width:320rpx;
		text-align: center;
		margin:5rpx 0 15rpx 0;
		padding:0 10rpx;
	}
		.other-message {
			height: 100rpx;

			.image-box {
				width: 311rpx;
				height: 37rpx;
			}

		}
	}


	.map {
		width: 100%;
		height: 30vh;
	}

	.map_container {
		position: absolute;
		top: 0rpx;
		bottom: 400rpx;
		left: 0;
		right: 0;
	}

	.chageMap {
		width: 100%;
	}

	.control-btn {
		z-index: 99;
		position: absolute;
		height: 60rpx;
		width: 60rpx;
		line-height: 60rpx;
		right: 36rpx;
	}

	.control-btn-increase {
		bottom: 190rpx;
	}

	.control-btn-decrease {
		bottom: 100rpx;
	}

	.control-btn-location {
		z-index: 99;
		position: absolute;
		height: 80rpx;
		width: 80rpx;
		left: 36rpx;
		bottom: 100rpx;
	}

	.grey {
		color: #B0B6C1;
	}
	.black{
		color:#242E3E
	}

	.blue {
		color: #5A8CE7;
	}

	.shallow-blue {
		color: #90AAE2;
	}

	.gun-popup {
		max-height: calc(80vh);
		overflow: auto;

		.popup-head {
			margin: 30rpx;
		}

		.popup-body {
			.gun-list {
				.gun-item {
					.flex-row {
						margin: 0 30rpx;
						padding: 20rpx 0;

						&.border {
							border-bottom: 1px solid #EFEFF1;
						}
					}

					.type-img {
						margin-right: 20rpx;
						width: 80rpx;
						height: 80rpx;
					}

				}

				.type {
					margin-left: 20rpx;
					padding: 0rpx 10rpx;
					border: 1rpx solid #EF635F;
					color: #EF635F;
					border-radius: 20rpx 0 20rpx 0;
					font-size: 20rpx;
				}
			}
		}
	}

	@keyframes remindMessage {
		0% {
			-webkit-transform: translateX(90%);
		}

		100% {
			-webkit-transform: translateX(-1200%);
		}
	}

	.tongzhitext {
		margin: 0 50rpx;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.tongzhi-text {
		font-size: 28rpx;
		animation: remindMessage 60s linear infinite;
		// width: 600rpx;
		color: #FFF;
		display: block;
	}

	.notice-wrap {
		margin: 176rpx 40rpx 0;
		width: 670rpx;
		opacity: 0.8;
		position: relative;
	}

	.closeView {
		width: 45rpx;
		height: 45rpx;
		line-height: 45rpx;
		position: absolute;
		right: 20rpx;
		top: 5rpx;
		text-align: center;
		font-size: 35rpx;
	}
// #ifdef MP-ALIPAY
	::v-deep .van-bottom-enter-active{
		z-index:169 !important;
	}
// #endif
.nav-default{
	background-color: #F0F2F5;
	width: 100%;
	&.h-20{
		height: 20rpx;
	}
	&.h-40{
		height: 40rpx;
	}
}
	.canvasName {
		position: fixed;
		left: -99999rpx;
		width: calc(100vw);
		height: calc(100vh);
	}
.share-canvas{
	position: fixed;
	left: -9999rpx;
	height: 380px;
	width: 340px;
}
.poster-container{
	width:567rpx;
	height:824rpx;
	margin: -12rpx;
	position: relative;
	.poster-img{
		width:100%;
		height:100%;
	}
	.qr-img{
		position: absolute;
		margin: 40rpx 40rpx 20rpx;
		width: 360rpx;
		height: 405rpx;
		top: 153rpx;
		left: 66rpx;
	}
	.qr-img-down{
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		width: 68rpx;
		height: 68rpx;

	}
	
}

.share-line{
	font-size: 28rpx;
	height: 80rpx;
	line-height: 80rpx;
	width: 670rpx;
	margin: 0 auto;
	text-align: center;
	&.border-bottom{
		border-bottom: 1px solid #CCC;
	}
}
	.preferential{
    height: 200rpx;
    box-shadow: 0px 4rpx 30rpx 0px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    font-size: 26rpx;
    font-weight: 500;
    color: #242e3e;
    padding: 20rpx 40rpx;
    line-height: 48rpx;
		.preferential-title{
			font-weight: bold;
			
		}
		.collecting-vouchers{
			line-height: 55rpx;
		}
		.preferential-tips{
			font-size: 20rpx;
			color:#ffffff;
			width: 210rpx;
			background-color:#f05037;
			border-radius: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-left:15rpx;
			// #ifdef MP-ALIPAY
				width: 220rpx;
			// #endif
			.hand{
				width:40rpx;
				height:40rpx;
				transform: rotate(90deg);
				transform-origin: 25% 30%;
				margin-left: 20rpx;
			}
		}
		.code{
			width:150rpx;
			height:150rpx;			
		}
		
	}
	.station-notice{
		position: fixed;
		z-index: 10000;
		left: 0rpx;
		top: 300rpx;
	}
</style>
