<view class='action-sheet-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <van-popup show='{{ show }}' position='bottom' round='{{ round }}' z-index='{{ zIndex }}' overlay='{{ overlay }}' custom-class='van-action-sheet' safe-area-inset-bottom='{{ safeAreaInsetBottom }}' close-on-click-overlay='{{ closeOnClickOverlay }}' onClose='onClickOverlay' ref='saveChildRef1'>
    <view a:if='{{ title }}' class='van-action-sheet__header'>
      {{ title }}      <van-icon name='cross' custom-class='van-action-sheet__close' class='van-action-sheet__close' onClick='onClose' ref='saveChildRef2'>
      </van-icon>
    </view>
    <view a:if='{{ description }}' class='van-action-sheet__description van-hairline--bottom'>
      {{ description }}
    </view>
    <view a:if='{{ actions && actions.length }}'>
      <btn a:for='{{ actions }}' a:key='{{index}}' open-type='{{ item.openType }}' style="{{ item.color ? 'color: ' + item.color : '' }}" class="{{ utils.bem('action-sheet__item', { disabled: item.disabled || item.loading }) }} {{ item.className || '' }}" hover-class='van-action-sheet__item--hover' data-index='{{ index }}' app-parameter='{{ appParameter }}' ref-numbers='{{ actions }}' onGetUserInfo='bindGetUserInfo' onGetPhoneNumber='bindGetPhoneNumber' onOpenSetting='bindOpenSetting' onTap='antmoveAction' data-antmove-tap='onSelect'>
        <block a:if='{{ !item.loading }}'>
          {{ item.name }}          <view a:if='{{ item.subname }}' class='van-action-sheet__subname'>
            {{ item.subname }}
          </view>
        </block>
        <van-loading a:else  custom-class='van-action-sheet__loading' size='22px' ref='saveChildRef3'>
        </van-loading>
      </btn>
    </view>
    <slot>
    </slot>
    <block a:if='{{ cancelText }}'>
      <view class='van-action-sheet__gap'>
      </view>
      <view class='van-action-sheet__cancel' hover-class='van-action-sheet__cancel--hover' hover-stay-time='70' onTap='antmoveAction' data-antmove-tap='onCancel'>
        {{ cancelText }}
      </view>
    </block>
  </van-popup>
</view>