<view class='dropdown-menu-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-dropdown-menu van-dropdown-menu--top-bottom'>
    <view a:for='{{ itemListData }}' a:key='{{index}}' data-index='{{ index }}' class="{{ utils.bem('dropdown-menu__item', { disabled: item.disabled }) }}" ref-numbers='{{ itemListData }}' onTap='antmoveAction' data-antmove-tap='onTitleTap'>
      <view class="{{ item.titleClass }} {{ utils.bem('dropdown-menu__title', { active: item.showPopup, down: item.showPopup === (direction === 'down') }) }}" style="{{ item.showPopup ? 'color:' + activeColor : '' }}">
        <view class='van-ellipsis'>
          {{ computed.displayTitle(item) }}
        </view>
      </view>
    </view>
    <slot>
    </slot>
  </view>
</view>