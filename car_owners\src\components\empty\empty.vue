<template>
	<view class="empty-data flex-col juc" v-bind:style="[styleName]">
		<view>
			<image v-if="type==1" class="empty-img" mode="widthFix" src="https://image.bolink.club/yima/empty1.jpg"></image>
			<image v-else-if="type==2" class="empty-img" mode="widthFix" src="https://image.bolink.club/yima/empty2.jpg"></image>
			<image v-else-if="type==3" class="empty-img" style="width: 500rpx;margin-bottom: 50rpx;" mode="widthFix" src="https://image.bolink.club/Fj3f-Zh2jxYSQ4WXjBdqKr5RAM-D"></image>
			<image v-else class="empty-img" mode="widthFix" src="https://image.bolink.club/yima/empty3.jpg"></image>
			
		</view>
		<view>{{textTip}}</view>
	</view>
</template>

<script>
	export default {
		// props: [ "type" ],
		props: {
			type: [String, Number],
			styleName: {
				type: Object,
				default() {
					return {}
				}
			},
			textTip: {
				type: String,
				default() {
					return "- 暂无数据 -"
				}
			},
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="less">

</style>
