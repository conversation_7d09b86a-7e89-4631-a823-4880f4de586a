<view class='field-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <van-cell size='{{ size }}' icon='{{ leftIcon }}' center='{{ center }}' border='{{ border }}' is-link='{{ isLink }}' required='{{ required }}' clickable='{{ clickable }}' title-width='{{ titleWidth }}' title-style='margin-right: 12px;' custom-style='{{ customStyle }}' arrow-direction='{{ arrowDirection }}' custom-class='van-field' ref='saveChildRef1'>
    <slot name='left-icon' slot='icon'>
    </slot>
    <view a:if='{{ label }}' class="{{labelClass}} {{ utils.bem('field__label', { disabled }) }}" slot='title'>
      {{ label }}
    </view>
    <slot a:else  name='label' slot='title'>
    </slot>
    <view class="{{ utils.bem('field__body', [type]) }}">
      <textarea a:if="{{ type === 'textarea' }}" class="{{inputClass}} {{ utils.bem('field__input', [inputAlign, type, { disabled, error }]) }}" focus='{{ autoFocus }}' value='{{ innerValue }}' disabled='{{ disabled || readonly }}' maxlength='{{ maxlength }}' placeholder='{{ placeholder }}' placeholder-style='{{ placeholderStyle }}' placeholder-class="{{ utils.bem('field__placeholder', { error, disabled }) }}" auto-height='{{ !!autosize }}' style='{{ computed.inputStyle(autosize) }}' undefined='onLineChange' onFocus='onFocus' onBlur='onBlur' onConfirm='onConfirm' onInput='onInput'>
      </textarea>
      <custom-input a:else  class="{{inputClass}} {{ utils.bem('field__input', [inputAlign, { disabled, error }]) }}" type="{{ type === 'search' ? 'text' : type}}" focus='{{ focus }}' cursor='{{ cursor }}' value='{{ innerValue }}' auto-focus='{{ autoFocus }}' disabled='{{ disabled || readonly }}' maxlength='{{ maxlength }}' placeholder='{{ placeholder }}' placeholder-style='{{ placeholderStyle }}' placeholder-class="{{ utils.bem('field__placeholder', { error }) }}" confirm-type='{{ confirmType }}' confirm-hold='{{ confirmHold }}' selection-end='{{ selectionEnd }}' selection-start='{{ selectionStart }}' password="{{ password || type === 'password' }}" onInput='onInput' onBlur='onBlur' onFocus='onFocus' onConfirm='onConfirm'>
      </custom-input>
      <van-icon hidden='{{ showClear }}' name='clear' class='van-field__clear-root van-field__icon-root' onClick='onClear' ref='saveChildRef2'>
      </van-icon>
      <view class='van-field__icon-container' onTap='antmoveAction' data-antmove-tap='onClickIcon'>
        <van-icon a:if='{{ rightIcon || icon }}' name='{{ rightIcon || icon }}' class='van-field__icon-root {{ iconClass }}' custom-class='{{rightIconClass}}' ref='saveChildRef3'>
        </van-icon>
        <slot name='right-icon'>
        </slot>
        <slot name='icon'>
        </slot>
      </view>
      <view class='van-field__button'>
        <slot name='button'>
        </slot>
      </view>
    </view>
    <view a:if='{{ showWordLimit && maxlength }}' class='van-field__word-limit'>
      <view class="{{ utils.bem('field__word-num', { full: value.length >= maxlength }) }}">
        {{ value.length }}
      </view>
      /{{ maxlength }}
    </view>
    <view a:if='{{ errorMessage }}' class="{{ utils.bem('field__error-message', [errorMessageAlign, { disabled, error }]) }}">
      {{ errorMessage }}
    </view>
  </van-cell>
</view>