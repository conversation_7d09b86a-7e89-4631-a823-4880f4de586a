.transition-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.transition-index .van-transition {
    transition-timing-function: ease;
}

.transition-index .van-fade-enter-active,
.transition-index .van-fade-leave-active {
    transition-property: opacity;
}

.transition-index .van-fade-enter,
.transition-index .van-fade-leave-to {
    opacity: 0;
}

.transition-index .van-fade-down-enter-active,
.transition-index .van-fade-down-leave-active,
.transition-index .van-fade-left-enter-active,
.transition-index .van-fade-left-leave-active,
.transition-index .van-fade-right-enter-active,
.transition-index .van-fade-right-leave-active,
.transition-index .van-fade-up-enter-active,
.transition-index .van-fade-up-leave-active {
    transition-property: opacity, -webkit-transform;
    transition-property: opacity, transform;
    transition-property: opacity, transform, -webkit-transform;
}

.transition-index .van-fade-up-enter,
.transition-index .van-fade-up-leave-to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    opacity: 0;
}

.transition-index .van-fade-down-enter,
.transition-index .van-fade-down-leave-to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    opacity: 0;
}

.transition-index .van-fade-left-enter,
.transition-index .van-fade-left-leave-to {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    opacity: 0;
}

.transition-index .van-fade-right-enter,
.transition-index .van-fade-right-leave-to {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    opacity: 0;
}

.transition-index .van-slide-down-enter-active,
.transition-index .van-slide-down-leave-active,
.transition-index .van-slide-left-enter-active,
.transition-index .van-slide-left-leave-active,
.transition-index .van-slide-right-enter-active,
.transition-index .van-slide-right-leave-active,
.transition-index .van-slide-up-enter-active,
.transition-index .van-slide-up-leave-active {
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
}

.transition-index .van-slide-up-enter,
.transition-index .van-slide-up-leave-to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
}

.transition-index .van-slide-down-enter,
.transition-index .van-slide-down-leave-to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
}

.transition-index .van-slide-left-enter,
.transition-index .van-slide-left-leave-to {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
}

.transition-index .van-slide-right-enter,
.transition-index .van-slide-right-leave-to {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
}
