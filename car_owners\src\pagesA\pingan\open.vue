<template>
	<view class="pingan">
	<!-- 	<view style="position: relative;">
			<image style="width: 100%;height: 862rpx" src="https://image.bolink.club/yima/ping_an_bg.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
		</view>
		
		<view class="main-cotain">
			<view class="top-box-par">
				<view class="top-box-con flex-row flex-1 alc">
					<view class="flex-1 ajc">
						<image class="top-box-con-icon marr-20" src="../static/pingan_new/line-left.png" style="width: 44rpx;height: 14rpx;"></image>
						开通互联网账户,享受优惠
						<image class="top-box-con-icon marl-20" src="../static/pingan_new/line-right.png" style="width: 44rpx;height: 14rpx;"></image>
					</view>
				</view>
			</view>
			
			<view class="top-box">
				<view class="top-box-content">
					<view class="flex-row alc pingan-input-par">
						<image src="../static/pingan_new/icon1.png" style="width: 33rpx;height: 36rpx;"></image>
						<input class="pingan-input" disabled type="text" v-model="name" />
					</view>
					<view class="flex-row alc pingan-input-par">
						<image src="../static/pingan_new/icon2.png" style="width: 35rpx;height: 28rpx;"></image>
						<input class="pingan-input" disabled maxlength="18" type="idcard" v-model="idcard" />
					</view>
					<view class="flex-row alc pingan-input-par" style="border-bottom: none;">
						<image src="../static/pingan_new/icon3.png" style="width: 36rpx;height: 27rpx;"></image>
						<input class="pingan-input" type="number" v-model.trim="bank" placeholder="请输入银行卡号" placeholder-style="color: #ABABAB;" />
						<view class="flex-1 jue">
							<image @click="photograph" src="../static/pingan_new/icon6.png" class="pingan-icon"></image>
						</view>
					</view>
					<view class="flex-row alc pingan-input-par">
						<image src="../static/pingan_new/icon4.png" style="width: 28rpx;height: 37rpx;"></image>
						<input 
							class="pingan-input" 
							type="number" 
							maxlength="11" 
							v-model.trim="phone" 
							placeholder="绑定卡预留的手机号"  
							placeholder-style="color: #ABABAB;"
							/>
					</view>
					<view class="flex-row alc">
						<view class="pingan-input-par" style="padding-left: 70rpx;">
							<input 
								class="pingan-input" 
								type="number" 
								v-model.trim="code" 
								maxlength="6"  
								style="width: 200rpx;"  
								placeholder="验证码" 
								placeholder-style="color: #ABABAB;"
							/>
						</view>
						<view class="flex-1 jue" style="margin-bottom: 25rpx;">
							<van-button
								customClass="my-submit-btn"
								@click="codeBtnClick" 
								size="small"
								:disabled="loadingCode"
								round 
								custom-style="width: 260rpx;height: 84rpx"
								color="#F65C27"
							>{{btnText}}</van-button>
						</view>
					</view>
					
					<view class="rule-checkbox">
						<view class="flex-row">
							<view>
								<van-checkbox custom-class="my-checkbox" style="block: inline" :value="true" :checked-color="checked1 ? '#FE6121': '#D7D7D7'" icon-size="30rpx" shape="square" @click.native="(e)=>{checked1=!checked1}">
								</van-checkbox>
							</view>
							<view>
								<span @click="()=>{checked1=!checked1}" style="color: #333">我已阅读并同意</span>
								<span class="rule-item" @click.stop="ruleView(1)">《平安银行电子II、III类户服务协议》</span>
								<span class="rule-item" @click.stop="ruleView(2)">《代收付服务协议》</span>
							</view>
						</view>
						<van-checkbox customClass="mart-10" :value="true" :checked-color="checked2 ? '#FE6121': '#D7D7D7'" icon-size="28rpx" shape="square" @click.native="(e)=>{checked2=!checked2}">
						  我已阅读并同意<span class="rule-item" @click.stop="ruleView(3)">《授权条款》</span>
						</van-checkbox>
					</view>
					<view class="open-btn padt-20">
						<van-button
							customClass="my-submit-btn"
							@click="btnClick" 
							:loading="loading"
							loading-text="加载中..."
							block 
							round
							color="#F65C27"
						>开通平安III类账户</van-button>
					</view>
				</view>
			</view>
		</view>
		
		
		
		<van-dialog id="van-dialog" /> -->
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	
	export default {
		data() {
			return {
				thirdId: '', // 查询资格时返回的id
				otpOrderNo: '', // 短信单号
				businessNo: '', // 业务号
				codeSuccess: false, // 是否成功获取验证码
				name: '',
				idcard: '',
				bank: '',
				phone: '',
				code: '',
				timer: null,
				loadingCode: false,
				loading: false,
				loadingText: 60,
				btnText: '获取验证码',
				btnTimber: '获取验证码',
				imagePath: null, // 拍照或相册的图片路径
				checked1: false,
				checked2: false,
				longitude: uni.getStorageSync('longitude'),
				latitude: uni.getStorageSync('latitude'),
			}
		},
		onLoad (options) {		
			if ('name' in options) {
				this.name = options.name;
				this.idcard = options.idcard;
				this.thirdId = options.thirdId;
			}
		},
		onUnload () {
			clearInterval(this.timer);
		},
		methods: {
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			ruleView (type) {
				uni.showLoading({
					title: '加载中...'
				})
				console.log('ruleView')
				let url = 'https://mweb.bolink.club/pingan/'
				if (type === 1) {
					url += 'pingan_er_san.pdf';
				} else if (type === 2) {
					url += 'pingan_dai.pdf';
				} else if (type === 3) {
					url += 'pingan_shou.pdf';
				}
				wx.downloadFile({
				  url: url,
				  success: (res) => {
					  uni.hideLoading();
				    const filePath = res.tempFilePath
				    wx.openDocument({
				      filePath: filePath,
				      success: (res) => {
						if (type === 1 || type === 2) {
						  this.checked1 = true;
						} else {
						  this.checked2 = true;
						}
				      }
				    })
				  },
				  fail: () => {
					  uni.showToast({
					  	title: '打开失败，请稍候再试',
					  	icon: 'none'
					  });
				  }
				})
			},
			// 获取验证码
			codeBtnClick () {
				if (!this.bank) {
					uni.showToast({
						title: '请先输入银行卡号',
						icon: "none"
					})
					return;
				} else if (!this.phone) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				} 
				this.loadingCode = true;
				let params = {
					thirdId: this.thirdId,
					cardId: this.idcard,
					customerName: this.name,
					bindCardNo: this.bank,
					mobileNo: this.phone,
					scene: 'OA'
				};
				apis.homeApis.getcheckcode(params).then((res)=> {
					if (res.status === 200) {
						this.otpOrderNo = res.data.otpOrderNo;
						this.businessNo = res.data.businessNo;
						this.thirdId = res.data.thirdId;
						this.codeSuccess = true;
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						});
						this.timer = setInterval(() => {
							this.loadingText = this.loadingText - 1;
							this.btnText = '倒计时' + this.loadingText;
							if (this.loadingText === 0) {
								this.btnText = this.btnTimber;
								this.loadingCode = false;
								this.loadingText = 60;
								clearInterval(this.timer)
							}
						}, 1000)
					} else {
						this.loadingCode = false;
						uni.showToast({
							title: '系统繁忙，请稍候再试',
							icon: 'none'
						});
					}
				}).catch(() => {
					this.loadingCode = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				})
			},
			// 调用相机
			photograph () {
				uni.chooseImage({
				    count: 1, 
				    sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
				    sourceType: ['camera', 'album'], // album 从相册选图，camera 使用相机，默认二者都有
				    success: (res) => {
						// console.log(res.tempFilePaths);
						// this.src = res.tempFilePaths[0]
						if(res.tempFiles[0].size <= 1000000){  
							this.imagePath = res.tempFilePaths[0]
							this.imageUpload(res.tempFilePaths[0]);
						} else {
							uni.showToast({
								title:'上传图片不能大于1M!',
								icon:'none'
							})
						}
				    },
					fail: (res) => {
						if (res.errMsg != 'chooseImage:fail cancel') {
							uni.showToast({
								title: '选择图片出错了，请重新尝试',
								icon: 'none'
							});
						}
					}
				});
			},
			// 上传图片识别
			imageUpload (src) {
				apis.homeApis.bankpic(src).then((res)=> {
					console.log(res);
					if (res.data.bank_card_number) {
						this.bank = res.data.bank_card_number;
					}
				}).catch(res=>{
					uni.hideLoading();
					uni.showToast({
						title: '图片上传失败，请稍候再试',
						icon: 'none'
					});
				})
			},
			// 点击开通按钮
			btnClick () {
				if (!this.name) {
					uni.showToast({
						title: '请先输入姓名',
						icon: "none"
					})
					return;
				} else if (!this.idcard) {
					uni.showToast({
						title: '请先输入身份证号',
						icon: "none"
					})
					return;
				} else if (!this.bank) {
					uni.showToast({
						title: '请先输入银行卡号',
						icon: "none"
					})
					return;
				} else if (!this.phone) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				} else if (this.phone.length != 11) {
					uni.showToast({
						title: '请输入正确手机号',
						icon: "none"
					})
					return;
				} else if (!this.codeSuccess) {
					uni.showToast({
						title: '请先获取验证码',
						icon: "none"
					})
					return;
				} else if (!this.code) {
					uni.showToast({
						title: '请先输入验证码',
						icon: "none"
					})
					return;
				} else if (!this.checked1 || !this.checked2) {
					uni.showToast({
						title: '请先阅读并勾选同意',
						icon: "none"
					})
					return;
				}
				this.loading = true;
				let params = {
					customerName: this.name,
					cardId: this.idcard,
					thirdId: this.thirdId,
					bindCardNo: this.bank,
					mobileNo: this.phone,
					otpValue: this.code,
					businessNo: this.businessNo,
					otpOrderNo: this.otpOrderNo,
					lbs: this.latitude + '/' + this.longitude,
				}
				apis.homeApis.openthirdacct(params).then((res) => {
					this.loading = false;
					if (res.status === 200) {
						let accountNo = res.data.accountNo;
						let thirdId = res.data.thirdId;
						let mobileNo = res.data.mobileNo;
						uni.navigateTo({
							url: `/pagesA/pingan/pay?phone=${mobileNo}&thirdId=${thirdId}&accountNo=${accountNo}`,
						})
					} else {
						let msg = res.data && JSON.parse(res.data)
						if (msg.responseCode == '********') {
							uni.showToast({
								title: '验证码错误',
								icon: 'none',
								duration: 5000
							});
						} else if (msg.responseCode == 'IA070059') {
							uni.showToast({
								title: '银行卡错误，请确认手机号是否为预留手机号',
								icon: 'none',
								duration: 5000
							});
						} else {
							uni.showToast({
								title: msg.responseMsg || '系统繁忙，请稍候再试',
								icon: 'none',
								duration: 5000
							});
						}
					}
				}).catch(()=>{
					this.loading = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		background-color: #DE572D;
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 420rpx;
	}
</style>
<style>
	.open-btn {
		margin: 0 12%;
		padding: 40rpx 0 60rpx 0;
	}
	.my-checkbox {
		display: inline-block!important;
		vertical-align: middle;
		width: 50rpx;
	}
	.top-box-content {
		padding: 20rpx;
	}
	.top-box-par {
		width: 100%;
		position: absolute;
		top: 0rpx;
	}
	.top-box-con {
		height: 90rpx;
		margin: 0 30rpx;
		font-size: 28rpx;
		color: #E03B00;
		border-radius: 20rpx 20rpx 0 0;
		background-color: #FAE1D1;
		/* box-shadow:0px 0px 10rpx 0px rgba(0,0,0,0.1); */
	}
	.top-box-con-icon {
		margin-bottom: -8rpx;
	}
	.btn-view {
		margin-right: 20rpx;
		font-size: 24rpx;
		text-align: center;
		color: #fff;
		width: 167rpx;
		height: 44rpx;
		line-height: 44rpx;
		background: linear-gradient(135deg,#fca484 0%, #f15a24);
		border-radius: 0px 50rpx 50rpx 50rpx;
	}
	.top-box {
		position: absolute;
		top: 90rpx;
		margin: 0 30rpx 40rpx 30rpx;
		/* border-radius: 20rpx 20rpx 0 0; */
		background-color: #fff;
		/* box-shadow:0px 0px 10rpx 0px rgba(0,0,0,0.12); */
	}
	.square {
		position: absolute;
		top: -20rpx;
		left: calc(50% - 25rpx);
		width: 40rpx;
		height: 30rpx;
		transform: rotate(45deg);
		background: #ffe0cf;
		border-radius: 6rpx;
	}
	
</style>
