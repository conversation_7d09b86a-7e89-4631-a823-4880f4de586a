<view class='toast-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <van-overlay a:if='{{ mask || forbidClick }}' show='{{ show }}' z-index='{{ zIndex }}' custom-style="{{ mask ? '' : 'background-color: transparent;' }}" ref='saveChildRef1'>
  </van-overlay>
  <van-transition show='{{ show }}' custom-style='z-index: {{ zIndex }}' custom-class='van-toast__container' ref='saveChildRef2'>
    <view class="van-toast van-toast--{{ type === 'text' ? 'text' : 'icon' }} van-toast--{{ position }}" catchTouchMove='antmoveAction' data-antmove-touchmove='noop'>
      <text a:if="{{ type === 'text' }}">
        {{ message }}      </text>      <block a:else >
        <van-loading a:if="{{ type === 'loading' }}" color='white' type='{{ loadingType }}' custom-class='van-toast__loading' ref='saveChildRef3'>
        </van-loading>
        <van-icon a:else  class='van-toast__icon' name='{{ type }}' ref='saveChildRef4'>
        </van-icon>
        <text a:if='{{ message }}' class='van-toast__text'>
          {{ message }}        </text>
      </block>      <slot>
      </slot>
    </view>
  </van-transition>
</view>