<template>
	<view class="agreement-container">
		<view class="agreement">
			<view class="yima-login">
				<image src="http://image.bolink.club/Fm5vHRmYqyGIC7MLl_SzQlo8VnqV" class="login"></image>
			</view>
			<view class="text-content">
				<view>
					感谢您的信任并使用一码App		
				</view>
				<view class="text agreement">
					请您仔细阅读<text class="orange" @click="toDetail(0)">《用户协议》</text>及<text class="orange" @click="toDetail(1)">《隐私政策》</text>，点击同意后即代表您已阅读并同意用户协议及隐私政策。您也可以在“一码App-我的-账户设置”查看相关服务条款。为了向您提供更好的服务，我们将严格按照政策的内容使用和保护您的个人信息，同时将弹框提示征求您的授权，获取以下权限和信息。您可以在系统设置中关闭授权，但会影响部分功能使用。
				</view>
				<view class="text">
					【地理位置】经授权，我们会根据您的地理位置信息，为您推荐附近的充电站、活动基于地理位置使用导航服务。
				</view>
				<view class="text">
					【相机】经授权，我们会在您使用扫码充电时使用您的相机功能。
				</view>
				<view class="text">
					【相册】经授权，我们会在您扫码充电、车辆认证时使用您的相册功能。
				</view>
				<view class="text">
					我们将通过《隐私政策》向您详细说明
				</view>
			</view>
			<view>
				<view class="login-footer">
					<view class="bl-button bl-button--primary" @click="goHome"
						>同意并继续</view>
					<view class="bl-button btn" @click="showPopup=true"
							>不同意</view>
				</view>

			</view>
		</view>
		<van-popup
			:show="showPopup"
			@close="onClose"
			round
			:close-on-click-overlay="false"
		>
			<view class="container">
				<view style="font-weight: bold;">温馨提示</view>
				<view  class="tip">我方提供的服务涉及大量线下场景，需要您的手机号以方便联系处理各种问题。若您不同意，将无法为您提供服务。</view>

				
				<view class="footer">
					<navigator open-type="exit" target="miniProgram" class="cancel btn">关闭应用</navigator>
					<view class="submit btn" @click="onClose">继续查看</view>
				</view>
			</view>
		</van-popup>
	</view>
</template>

<script>
	import apis from "../../common/apis/index"
	import util from '../../common/utils/util'

	let app = getApp();
	export default {
		data() {
			return {
				showPopup:false,

			}
		},
		onLoad(){
			// #ifdef MP-ALIPAY
			my.setNavigationBar({
				backgroundColor:'#2385ff'
			})
			// #endif
		},
		methods: {
			onClose() {
				this.showPopup=false
			},
			goHome(){	
				uni.setStorageSync('loginAgreement',true)
				uni.switchTab({
					url:'/pages/index/index?loginType=loginAgreement'
				})
			},

			toDetail(num) {
				let url =
					num == 0 ? "/pagesA/agreement/user" : "/pagesA/agreement/privacy"
				let startTime = new Date().getTime()
				uni.navigateTo({
					url: url,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: `协议列表`,
							clickType: "Button",
							jumpType: "本程序页面",
							jumpDesc: "查看协议明细",
							result: res.errMsg == "navigateTo:ok" ? "成功" : "失败",
							startTime: startTime,
							endTime: new Date().getTime(),
						})
					},
				})
			},
		},
	}
</script>

<style lang="less" scoped>
	page {
		background-color: #f0f2f5;
	}
	.agreement-container {
		width: 100%;
		margin:0 40rpx;
		//#ifdef MP-WEIXIN || APP-PLUS
		margin: 180rpx 40rpx 0  40rpx;
		// #endif
		// #ifdef MP-ALIPAY
		margin: 80rpx 40rpx 0 40rpx;
		// #endif
		.yima-login{
			text-align: center;
			
			.login{
				width:200rpx;
				height:200rpx;
				border-radius: 50%;
			}
			
		}
		.text-content{
			font-size: 28rpx;
			font-weight: bold;
			margin-top:20rpx;
			color: #242e3e;
			.orange{
				color:#fc954c;
			}
			
			.text{
				text-indent: 2em;
				line-height: 35rpx;
				margin: 20rpx 0;
			}
			
		}
		.login-footer{
			position: fixed;
			bottom: 50rpx;
			font-weight: bold;
			width: 100%;
			left: 0;
			padding: 0 40rpx;
			font-size: 28rpx;
			.bl-button--primary{
				background: #24be7d;
				text-align: center !important;
				height:80rpx;
				color:#ffffff;
				line-height: 80rpx;
				border-radius: 20rpx;
				font-size: 28rpx;
				
			}
			.btn{
				color: #242e3e;
				text-align: center !important;
				font-size: 28rpx;
				border:1rpx solid #d8d8d8;
				width: 100%;
				margin: 50rpx auto 0 auto;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 20rpx;
			}
			
		}

	}
.container {
	padding: 20rpx;
	width: 85vw;
	height: 320rpx;
	text-align: center;
	color: #242e3e;
	.tip{
		margin-top:20rpx;
		font-size: 26rpx;
	}

	.footer {
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		justify-content: space-between;
		padding: 45rpx 40rpx 20rpx 40rpx;
		margin-bottom: 20rpx;
		.btn {
			width: 200rpx;
			height: 86rpx;
			line-height: 86rpx;
			font-size: 28rpx;
			color: #242e3e;
			border-radius: 50rpx;
		}
		.submit {
			color: #f8f8f8;
			color:#45c691;
		}
	}
}

</style>