<view class='stepper-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-stepper {{customClass}}'>
    <view a:if='{{ showMinus }}' data-type='minus' style='width: {{ utils.addUnit(buttonSize) }}; height: {{ utils.addUnit(buttonSize) }}' class="{{minusClass}} {{ utils.bem('stepper__minus', { disabled: disabled || disableMinus || currentValue <= min }) }}" hover-class='van-stepper__minus--hover' hover-stay-time='70' onTap='antmoveAction' data-antmove-tap='onTap' catchTouchStart='antmoveAction' data-antmove-touchstart='onTouchStart' catchTouchEnd='antmoveAction' data-antmove-touchend='onTouchEnd'>
    </view>
    <input no-custom=" " type="{{ integer ? 'number' : 'digit' }}" class="{{inputClass}} {{ utils.bem('stepper__input', { disabled: disabled || disableInput }) }}" style='width: {{ utils.addUnit(inputWidth) }}; height: {{ utils.addUnit(buttonSize) }}' value='{{ currentValue }}' focus='{{ focus }}' disabled='{{ disabled || disableInput }}' onInput='onInput' onFocus='onFocus' onBlur='onBlur'>
    </input>
    <view a:if='{{ showPlus }}' data-type='plus' style='width: {{ utils.addUnit(buttonSize) }}; height: {{ utils.addUnit(buttonSize) }}' class="{{plusClass}} {{ utils.bem('stepper__plus', { disabled: disabled || disablePlus || currentValue >= max }) }}" hover-class='van-stepper__plus--hover' hover-stay-time='70' onTap='antmoveAction' data-antmove-tap='onTap' catchTouchStart='antmoveAction' data-antmove-touchstart='onTouchStart' catchTouchEnd='antmoveAction' data-antmove-touchend='onTouchEnd'>
    </view>
  </view>
</view>