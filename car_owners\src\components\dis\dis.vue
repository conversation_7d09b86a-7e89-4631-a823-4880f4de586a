<template>
	<view class="flex-row alc dis-con" @click="clickIcon">
		<view class="dis-icon flex-row alc">
			<image src="https://image.bolink.club/yima/chat.png" mode=""></image>
		</view>
		<view class="flex-1 spread-box" v-if="spread">
			<!-- <span @click="jumpToDis"></span> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				spread: true
			};
		},
		watch: {
			spread (val) {
				if (val) {
					setTimeout(()=>{
						this.spread = false;
					}, 10000)
				}
			}
		},
		created() {
			setTimeout(()=>{
				this.spread = false;
			}, 10000)
		},
		methods: {
			clickIcon () {
				this.spread = true;
				this.jumpToDis();
			},
			jumpToDis () {
				uni.navigateTo({
					url: '/pages/activity/dis',
					complete: (res) => {
						getApp().eventRecord({
							keyWord: '点击吐槽图标-跳转',
							clickType: '图片',
							jumpType: '本程序页面',
							jumpDesc: '吐槽页面',
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: new Date().getTime(),
							endTime: new Date().getTime()
						});
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.dis-con {
		position: fixed;
		right: -34rpx;
		bottom: 240rpx;
		border-radius: 50rpx;
		padding-right: 20rpx;
		background-color: rgba(255, 255, 255, 0.5);
	}
	.dis-icon image {
		width: 74rpx;
		height: 74rpx;
	}
	.spread-box {
		width: 40rpx;
	}
</style>
