<template>
	<view class="oil-container" :class="canvasShow?'dom-fixed':''">
		<view
			class="custom-back-nav"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}"
		>
			<view class="flex-row alc">
				<!-- #ifdef MP-WEIXIN -->
				<van-icon @click="goBack" name="arrow-left" color="#fff" size="36rpx" />
				<!-- #endif -->
				<!-- #ifdef MP-ALIPAY -->
				<van-icon @click="goBack" style="width: 18px;" color="#fff" size="36rpx" />
				<!-- #endif -->
				<view @click="goBack" style="margin-left: 4rpx;">今日油价</view>
			</view>
		</view>
		<view class="oil-header">
			<image src="https://image.bolink.club/yima/oil_top_bg.jpg" mode="" class="oil-to-bg"></image>
			<view class="oil-header-title">
				<text>今日油价</text>
			</view>
			<view class="oil-header-date">
				下次调价日预计在：<text>{{nextUpdateTime}}</text>
			</view>
		</view>
		<view class="oil-body">
			<view class="oil-area-container" @tap="chooseCity">
				<image src="../static/location-icon.png" mode="" class="oil-area-location__img"></image>
				<text class="oil-area-location__text">{{areaName|| '选择地区'}}</text>
				<view class="oil-area-location__flex"></view>
				<van-icon name="arrow" class="oil-area-location__icon"/>
			</view>
			<view class="oil-list">
				<view class="oil-item" v-for="(item, index) in oilPriceList" :key="item.name" :class="index===0?'oil-item__first':''">
					<image :src="item.diff>= 0?'../static/diff_up.png':'../static/diff_down.png'" class="oil-item__icon" :class="item.diff>= 0?'icon-up':'icon-down'"></image>
					<view class="oil-item__text oil-item__name">{{item.name}}</view>
					<view class="oil-item__text oil-item__diff" :class="item.diff>= 0?'diff-up':'diff-down'">{{item.diff}}</view>
					<view class="oil-item__text oil-item__price">{{item.price}}/升</view>
				</view>
			</view>
		</view>
		<view class="oil-footer">
			<view class="oil-btn" @tap="calendarshow">
				<image src="../static/btn_bg_1.png" class="oil-btn-bg"></image>
				<view class="oil-btn__content">
					<image src="../static/btn_date_icon.png" class="oil-btn-icon"></image>
					<text>油价日历</text>
				</view>
				
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<view class="oil-btn" @tap="handleShareClick">
				<image src="../static/btn_bg_2.png" class="oil-btn-bg"></image>
				<view class="oil-btn__content">
					<image src="../static/btn_share_icon.png" class="oil-btn-icon"></image>
					<text>分享好友</text>
				</view>
				
			</view>
			<!-- #endif -->
		</view>
		<poster ref="poster" @change="handlePosterChange"></poster>
		<!-- #ifdef MP-ALIPAY -->
		<van-popup :show="showArea" position="bottom" custom-style="height: 30%;" onClick-overlay="areaCancel">
			<van-area :area-list="areaList" :loading="areaLoading" value="110101" :columns-num="1" title="所在城市"
				onConfirm="areaConfirm" onCancel="areaCancel" />
		</van-popup>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<van-popup :show="showArea" position="bottom" custom-style="height: 30%;" @click-overlay="areaCancel">
			<van-area :area-list="areaList" :loading="areaLoading" value="110101" :columns-num="1" title="所在城市"
				@confirm="areaConfirm" @cancel="areaCancel" />
		</van-popup>
		<!-- #endif -->  
	</view>
</template>

<script>
import BaseConfig from '../../common/config/index.config.js';
import apis from "../../common/apis/index";
import form from '../../common/utils/form.js';
import poster from '../components/poster/poster.vue'
const app = getApp();
export default {
	components: { 
		poster
	},
	data() {
		return {
			areaList:{},
			showArea: false, //是否显示城市
			areaName: '北京市', // 当前城市
			areaCode: '110100', // 当前城市code
			userInfo: app.globalData.userInfo, // 用户信息
			latitude: BaseConfig.latitude,
			longitude: BaseConfig.longitude,
			showLocationTip: true, // 没获取到定位显示'暂无定位，默认', 
			userLocation: false, // 用户是否开启定位授权，用户未授权时，提示显示"暂无授权，默认"
			areaLoading: true,
			nextUpdateTime: '',
			posterImgUrl:null,
			canvasShow: false,
			oilPriceList:[
				{
					name: '- -',
					diff: 0,
					price:0.0
				}
			],
			windowHeight: 0,
			statusBarHeight: 0,
			navHeight: 0,
			timer:null,
		}
	},
	onLoad() {
		getApp().getSystemInfo().then(res => {
			this.windowHeight = res.windowHeight;
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight;
		});
		this.getNextUpdateTime();
		// this.getOilList();
		// #ifdef MP-ALIPAY
		this.$scope.areaCancel = this.areaCancel.bind(this)
		this.$scope.areaConfirm = this.areaConfirm.bind(this)
		// #endif
	},
	onReady(){
		// app.userInfo().then(res => {
		// 	this.userInfo = app.globalData.userInfo;
		// }).catch((err) => {
		// 	console.log(err);
		// })
		app.login().then(res => {
			this.initData();
			this.getLocation();
			// app.userInfo().then(res => {
			// 	this.userInfo = app.globalData.userInfo;
			// }).catch((err) => {
			// 	console.log(err);
			// })
		})
	},
	onShow () {
		
		// 判断是否有token和session_key
		getApp().isloginSuccess();
	},
	onHide() {
		clearTimeout(this.timer)
	},
	onUnload () {
		clearTimeout(this.timer)
		uni.$off("@change")
	},
	//#ifdef MP-WEIXIN
	onShareAppMessage () {
		return {
			title: "全国油价实时查询，油价变动掌握在手!",
			imageUrl: this.posterImgUrl,
			path: '/pagesB/integral/oilPrice',
			success: res => {
				console.info(res)
			}
		}
	},
	// #endif
	methods: {
		// 海报图片的url
		handlePosterChange(val) {
			this.posterImgUrl = val;
		},
		// 初始化城市和车牌信息
		initData() {
			let areaList = uni.getStorageSync("areaList");
			if (areaList && areaList.province_list) {
				this.areaList = areaList;
				this.areaLoading = false;
			} else {
				apis.homeApis.getcitytree().then((res) => {
					
					let province_list = {};
					let city_list = {};
					let short_list = {};
					let arr = res.data.cityTreeNodes;
					let pro = res.data.pro;
					let len = res.data.cityTreeNodes.length;
					for (let i = 0; i < len; i++) {
						province_list[arr[i].areaCode] = arr[i].cityName;
					}
					for (let n = 0; n < pro.length; n++) {
						short_list[pro[n].code] = pro[n].name;
					}
					this.areaList.province_list = province_list;
					this.areaList.short_list = short_list;
					this.areaLoading = false;
					uni.setStorage({ // 缓存城市信息
						key: "areaList",
						data: this.areaList
					})
				}).catch(err => {
					console.error('err: ',err)
				});
			}
		},
		// 获取用户信息的回调
		getUserProfile(e) {
			app.updataUserInfo().then(() => {
				this.userInfo = app.globalData.userInfo;
			});
		},
		
		getLocation() {
			// 获取经纬度，通过高德获取城市名
			uni.getLocation({
				type: 'gcj02',
				success: (r) => {
					this.userLocation = true;
					uni.setStorageSync("longitude", r.longitude);
					uni.setStorageSync("latitude", r.latitude);
					this.latitude = r.latitude;
					this.longitude = r.longitude;
					apis.homeApis.getcode({
						"lon": r.longitude,
						"lat": r.latitude
					}).then((res) => {
						if (res.status === 200) {
							this.areaName = res.data.province;
							this.areaCode = res.data.provinceCode;
							this.getOilList();
							// let code = String(this.areaCode).slice(0, 2) + "0000";
							// uni.setStorageSync("shortName", res.data.cityName);
							// uni.setStorageSync("areaCode", code);
						}
						
					})
				},
				fail: (err) => {
					this.userLocation = false;
				},
			});
		},
		// 显示授权弹框
		showUserLocation() {
			Dialog.confirm({
				title: '打开设置授权',
				message: "获取定位失败，请检查是否开启定位授权。",
			}).then(() => {
				// on confirm
				uni.openSetting({
					success: (res) => {
						// console.log(res.authSetting);
						if (res.authSetting['scope.userLocation']) {
							this.getLocation();
						}
					}
				})
			}).catch(() => {
				// on cancel
			});
		},
		
		// 点击切换城市
		chooseCity() {
			if (this.userLocation) {
				this.showArea = true;
			} else {
				this.showUserLocation();
			}
		
		},
		// 选择地区点击确定
		areaConfirm(e) {
			try {
				this.areaName = e.detail.values[0].name;
				this.areaCode = e.detail.values[0].code;
				this.getOilList();
			} catch (e) {
				app.globalData.log.error('--areaConfirm--this.areaName->', e, this.areaName);
			}
			this.showArea = false;
		},
		// 选择地区点击取消
		areaCancel() {
			this.showArea = false;
		},
		getNextUpdateTime() {
			apis.homeApis.oiltime().then((res) => {
				if (res.status === 200) {
					this.nextUpdateTime = res.data.nextUpdateTime; //油价时间更新
				}else {
					this.nextUpdateTime = '获取中'; //油价时间更新
					this.timer = setTimeout(()=> {
						this.getNextUpdateTime();
					},3000)
				}
			})
		},
		handleCloseClick() {
			this.canvasShow = false;
		},
		/**
		 * 格式化成如下格式
		 * {
			x: 200,
			y: 265,
			text: '7.07',
			type: 'text',
			zIndex: 0,
			font: {
				size: 16,
				weight: 700,
			},
			color: '#fff',
			}
		 */
		foramtPrice() {
			let textGroup = [];
			let startPoint = 503, spacing = 106;
			for (let i in this.oilPriceList) {
				textGroup.push({
					x: 420,
					y: startPoint + i*spacing,
					text: this.oilPriceList[i].price+'',
					type: 'text',
					zIndex: 1,
					font: {
						family: 'HYLiLiangHeiJ, HYLiLiangHeiJ-Regular;',
						size: 32,
						weight: 700,
						
					},
					color: '#fff',
				})
			}
			return textGroup;
		},
		async handleShareClick() {
			this.$refs.poster.createdPoster(getApp().globalData.shareImg[3], this.foramtPrice())
		},
		// 跳转到油价日历
		calendarshow() {
			uni.navigateTo({
				url: "/pagesB/integral/oilcalendar" //跳转到记油耗日历
			})
		},
		getOilList() {
			let params = {
				province: this.areaName,
			};
			apis.homeApis.oillist(params).then((res) => {
				this.oilPriceList = res.data.rows;
			}) //查询油价，根据省份
		},
		goBack () {
			uni.navigateBack({
				fail: (err)=> {
					uni.switchTab({
						url: '/pages/park/index'
					})
				}
			});
		},
		
	}
};
</script>

<style lang="scss" scoped>
	
.oil-container {
	background-color: #fbfbfb;
	width: 100%;
	min-height: 100%;
}
.dom-fixed {
	overflow: hidden;
	position: fixed;
	height: 100vh;
	width: 100vw;
}
.oil-header {
	position: relative;
	height: 556rpx;
}
.oil-to-bg {
	position: absolute;
	top: 0;left: 0;
	margin: 0;padding: 0;
	height: 100%;width: 100%;
}
.oil-header-title {
	position: absolute;
	top: 290rpx;
	right: 63rpx;
	height: 70rpx;
	font-weight: 700;
	font-size: 70rpx;
	color: #fff;
}
.oil-header-date {
	position: absolute;
	top: 385rpx;
	right: 63rpx;
	font-weight: 500;
	font-size: 22rpx;
	color: #fff;
}
.oil-body {
	position: relative;
	margin: 0 34rpx;
	min-height: 630rpx;
	.oil-area-container {
		position: absolute;
		top: -110rpx;
		height: 94rpx;
		width: 100%;
		display: flex;
		align-items: center;
		background: #f7f9fd;
		border-radius: 30px 30px 0 0;
		.oil-area-location__img {
			margin: 0 19rpx 0 40rpx;
			width: 23rpx;
			height: 33rpx;
		}
		.oil-area-location__text {
			font-size: 26rpx;
			font-weight: 500;
			color: #999;
		}
		.oil-area-location__flex {
			flex: 1;
		}
		.oil-area-location__icon {
			margin-right: 40rpx;
		}
	}
}
.oil-list {
	position: relative;
	top: -18rpx;
	.oil-item {
		padding: 0 33rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		height: 110rpx;
		background: #fff;
		border-radius: 30rpx;
		box-shadow: 0 4rpx 20rpx 0 rgba(0,0,0,0.02); 
		.oil-item__icon {
			width: 40rpx;
		}
		.icon-up {
			height: 35rpx;
		}
		.icon-down {
			height: 48rpx;
		}
		.oil-item__text {
			font-size: 32rpx;
			font-weight: 700;
		}
		.oil-item__name {
			margin-left: 37rpx;
			flex:1;
			font-weight: 700;
			font-size: 32rpx;
			text-align: left;
			color: #333;
			
		}
		.oil-item__diff {
			
		}
		.diff-up {
			color: #FC8D29;
		}
		.diff-down {
			color: #1BCAD5;
		}
		.oil-item__price {
			width: 150rpx;
			text-align: right;
			color: #999;
			
		}
	}
	.oil-item__first {
		padding-top: 18rpx;
		border-radius: 0 0 30rpx 30rpx;
		box-shadow: none;
	}
}
.oil-footer {
	padding: 114rpx 42rpx;
	display: flex;
	align-items: center;
	// #ifdef MP-WEIXIN
	justify-content: space-between;
	// #endif
	// #ifdef MP-ALIPAY
	justify-content: center;
	// #endif
	.oil-btn {
		position: relative;
		width: 318rpx;
		height: 120rpx;
		text-align: center;
		image{
			margin: 0;
			padding: 0;
		}
		.oil-btn-bg {
			position: absolute;
			top:0;
			left: 0;
			z-index: 1;
			width: 100%;
			height: 100%;
		}
		.oil-btn__content {
			position: absolute;
			top:0;
			left: 0;
			z-index: 2;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.oil-btn-icon {
			margin-right: 27rpx;
			width: 48rpx;
			height: 39rpx;
		}
		text {
			font-size: 30rpx;
			font-weight: 700;
			color: #fff;
		}
	}
}
.canvas-dom {
	display: none;
}
.canvas-hide {
	position: fixed;
	right: 100vw;
	bottom: 100vh;
	/* 2 */
	z-index: -9999;
	/* 3 */
	opacity: 0;
}
.poster-container {
	position: absolute;
	top:0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 9;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.8);
	overflow: hidden;
	.poster-body {
		position: absolute;
		width: 77%;
		height: 70.5%;
		.poster-img {
			margin: 0;
			padding: 0;
			width: 100%;
			height: 84.18%;
		}
		.poster-footer {
			padding-top: 104rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.poster-btn {
				width: 34.89%;
				height: 96rpx;
			}
		}
	}
	
}
</style>