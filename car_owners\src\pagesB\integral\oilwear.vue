<template>
	<view class="juc" >
		<view class="flex-row alc tb">
			<van-icon @click="goBack" name="arrow-left" size="36rpx" />
			<view @click="goBack" style="margin-left: 4rpx;" class="return-text ">记油耗</view>
		</view>
	</view>
</template>
<script>
	import BaseConfig from '../../common/config/index.config.js';
	import apis from "../../common/apis/index";
	import form from '../../common/utils/form.js';
	export default{
		data(){
			return{
				plateNumber:null,
				pageNum:1,
				 pageSize:20
			}
		},
		onReady(){
			this.oilpage();
			this.oilrecodelist();
			// this.oilrecodeadd()
		},
		methods:{
			//查询油价首页
			oilpage(){
				let params = {
					plateNumber: this.plateNumber
				}
				apis.homeApis.oilpage(params).then((res) => {
						
					}) 
			},
			oilrecodelist(){
				let params = {
					plateNumber: this.plateNumber,
					pageNum:this.pageNum,
					 pageSize:this. pageSize
				}
				apis.homeApis.oilrecodelist(params).then((res) => {
							
					}) 
			},
		// 	oilrecodeadd(){
		// 		let params = {
		// 			addOilDate: this.addOilDate,
					
		// 		}
		// 		apis.homeApis.oilrecodeadd().then((res) => {
							
		// 			}) 
		// 	},
		
			goBack () {
				uni.navigateBack({
					fail: (err)=> {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				});
			},
		}
	}
	
</script>
<style class="less">

		.tb{
			position: absolute;
			top: 100rpx;
			left: 20rpx;
		}
	.return-text {
		text-shadow: 2rpx 2rpx 2rpx rgba(0,0,0, 0.5);
	}
</style>
