<template>
	<view class="flex100">
		<view class="date-select">
			<view class="bl-flex mar-30">
				<button type="default" class="bl-picker--button bl-picker--plain" @click="handleCanel">取消</button>
				<view class="bl-picker--title">{{title}}</view>
				<button type="default" class="bl-picker--button bl-picker--primary" @click="handleConfirm">确定</button>
			</view>
			<picker-view class="time-picker-group" :value="value" v-if="visible" :indicator-style="indicatorStyle"
				@change="bindChange">
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in years" :key="index">{{item}}年</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in months" :key="index">{{item}}月</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in days" :key="index">{{item}}日</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in 24" :key="index">{{item}}时</view>
				</picker-view-column>
				<picker-view-column>
					<view class="time-item" v-for="(item,index) in 60" :key="index">{{item}}分</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: '请选择',
			},
			limit: {
				type: Number,
				default: 1,
			}
		},
		data() {
			const date = new Date(new Date().getTime() + 11 * 60000)
			const years = []
			const year = date.getFullYear()
			const months = []
			const month = date.getMonth() + 1
			const days = []
			const day = date.getDate()

			for (let i = 0; i <= 1; i++) {
				years.push(Number(date.getFullYear()) + i)
			}

			for (let i = 1; i <= 12; i++) {
				months.push(i)
			}

			for (let i = 1; i <= 31; i++) {
				days.push(i)
			}
			return {
				years,
				year,
				months,
				month,
				days,
				day,
				hour: date.getHours(),
				minute: date.getMinutes(),
				value: [0, month - 1, day - 1, date.getHours(), date.getMinutes()],
				nian: '',
				yue: '',
				ri: '',
				visible: true,
				indicatorStyle: 'height: 50px',
			}
		},
		methods: {
			bindChange(e) {
				let a;
				let y;
				let val = e.detail.value;
				y = this.years[val[0]];
				a = this.months[val[1]];
				this.day = this.days[val[2]];
				this.hour = val[3];
				this.minute = val[4];
				this.month = a;
				this.year = y;
				this.days = [];
				if (a || y) {
					if (a == 1 || a == 3 || a == 5 || a == 7 || a == 8 || a == 10 || a == 12) {
						for (let i = 1; i <= 31; i++) {
							this.days.push(i)
						}
					} else if (a == 4 || a == 6 || a == 11 || a == 9) {
						for (let i = 1; i <= 30; i++) {
							this.days.push(i)
						}

					} else if (a == 2) {
						if (this.year % 4 == 0 && (this.year % 100 != 0 || this.year % 400 == 0)) {
							for (let i = 1; i <= 29; i++) {
								this.days.push(i)
							}
						} else {
							for (let i = 1; i <= 28; i++) {
								this.days.push(i)
							}
						}
					}

				}
			},
			handleConfirm(e) {
				let timeStr = `${this.year}/${this.month}/${this.day} ${this.hour}:${this.minute}:00`
				let time = Math.floor(new Date(timeStr).getTime() / 1000)
				let currentTime = Math.floor(new Date().getTime() / 1000)
				let flag = ((time - currentTime) > this._props.limit * 24 * 3600) || (currentTime > time)
				if (flag) {
					uni.showToast({
						title: '请选择当前时间起，24小时内时间！',
						icon: "none"
					})

				} else if (currentTime > time - 600) {
					uni.showToast({
						title: '预约时间至少大于当前10分钟!',
						icon: "none"
					})
				} else {
					let data = {
						time: time
					}
					this.$emit("timeConfirm", data)
				}
			},
			handleCanel() {
				this.$emit("timeCancel", false)
			},
		}
	}
</script>

<style scoped lang="scss">
	.date-select {
		width: 100%;
		height: 700rpx;
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		background-color: #FFFFFF;
		position: relative;

		.time-picker-group {
			background-color: #FFF;
			position: absolute;
			width: 100%;
			height: 500rpx;
			bottom: 20rpx;

			.time-item {
				height: 50px;
				line-height: 50px;
				font-size: 34rpx;
				text-align: center;
			}
		}

		.bl-picker {
			padding: 42rpx 50rpx;
			background-color: #fff;

			&--header {
				padding: 50rpx 0 20rpx 0;

				.header-item {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #b0b6c1;
				}
			}

			&--title {
				flex: 1;
				height: 64rpx;
				line-height: 64rpx;
				text-align: center;
				font-weight: 700;
				font-size: 30rpx;
				color: #666666;

			}

			&--button {
				padding: 0 34rpx;
				height: 64rpx;
				line-height: 64rpx;
				font-size: 30rpx;
				font-weight: 500;
			}

			&--plain {
				background-color: transparent;
				color: #3970F0 !important;
				border: 2rpx solid #3970F0;
				border-radius: 47rpx;
			}

			&--primary {
				background: linear-gradient(293deg, #3663f4 0%, #468cfe);
				border-radius: 55px;
				color: #fff !important;
			}
		}

	}
</style>
