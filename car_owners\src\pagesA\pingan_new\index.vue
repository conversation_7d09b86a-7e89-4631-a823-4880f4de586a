<template>
	<view class="pingan">
		<view style="position: relative;">
			<image style="width: 100%;height: 862rpx" src="https://image.bolink.club/yima/ping_an_bg2.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
		</view>

		<view class="main-cotain">

			<view class="flex-row juc" style="padding: 0 112rpx 25rpx 112rpx;">
				<view class="coupon-item flex-col flex-1 ajc">
					<view class="coupon-item-img">
						<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
						<view class="coupon-item-name">停车券</view>
						<view class="coupon-item-price flex-row ale">
							<view class="coupon-item-price-d">￥</view>
							<view class="coupon-item-price-num">3</view>
							<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
						</view>
						<view class="coupon-item-price-desc">满3.01元可用</view>
					</view>
					<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
				</view>
				<view class="coupon-item flex-col flex-1 ajc">
					<view class="coupon-item-img">
						<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 172rpx;height: 243rpx;"></image>
						<view class="coupon-item-name-c">停车券</view>
						<view class="coupon-item-price-c flex-row ale">
							<view class="coupon-item-price-d">￥</view>
							<view class="coupon-item-price-num">5</view>
							<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
						</view>
						<view class="coupon-item-price-desc-c">满5.01元可用</view>
					</view>
					<image src="../static/pingan_new/coupon-arr2.png" style="width: 55rpx;height: 56rpx;"></image>
				</view>
				<view class="coupon-item flex-col flex-1 ajc">
					<view class="coupon-item-img">
						<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
						<view class="coupon-item-name">停车券</view>
						<view class="coupon-item-price flex-row ale">
							<view class="coupon-item-price-d">￥</view>
							<view class="coupon-item-price-num">2</view>
							<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
						</view>
						<view class="coupon-item-price-desc">满2.01元可用</view>
					</view>
					<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
				</view>
			</view>

			<view class="home-box">
				<view style="position: relative;">
					<view class="home-box-title flex-row juc">
						<view class="home-box-title-text">仅需3步即可领赏</view>
					</view>
				</view>

				<!-- <view class="home-box-desc">注册后支付3元，10元优惠券到达注册手机号码</view> -->
				<view class="flex-row alc" style="padding-left: 60rpx;padding-top: 100rpx;">
					<view class="flex-col juc fg1 home-box-par">
						<view class="home-box-item flex-row alc">
							<image src="../static/pingan_new/1.png" style="width: 60rpx;height: 80rpx;margin-left: 40rpx;"></image>
						</view>
						<view class="home-box-text">填写注册信息</view>
					</view>
					<view class="flex-col juc fg1 home-box-par">
						<view class="home-box-item flex-row alc">
							<image src="../static/pingan_new/2.png" style="width: 80rpx;height: 72rpx;margin-left: 24rpx;"></image>
						</view>
						<view class="home-box-text" style="margin-left: -10rpx;">确认信息并支付</view>
					</view>
					<view class="flex-col juc fg1 home-box-par">
						<view class="home-box-item flex-row alc">
							<image src="../static/pingan_new/3.png" style="width: 70rpx;height: 68rpx;margin-left: 32rpx;"></image>
						</view>
						<view class="home-box-text" style="margin-left: 18rpx;">支付成功</view>
					</view>
				</view>
			</view>

			<view class="padt-20 content-box-btn2 flex-row juc">
				<view style="position: relative;">
					<image src="https://image.bolink.club/yima/ping_an_btn_bg.png" style="width: 480rpx;height: 130rpx"></image>
					<van-button
						@click="clickBtn"
						block
						round
					>立即领取</van-button>
				</view>
			</view>

			<view class="pingan-desc">
				<view class="pingan-desc-title">活动说明:</view>
				<view class="pingan-desc-content">
					<view>1、本活动为一码YiMa与平安银行联合针对平安银行新户专享活动； </view>
					<view>2、购买停车券用户需符合平安银行新户开户的相关要求； </view>
					<view>3、停车券可在一码YiMa指定停车场支付停车费时使用，停车费需大于停车券面额； </view>
					<view>4、查询停车券可微信搜索一码YiMa小程序查询；</view>
					<view>5、本活动最终解释权归一码YiMa与平安银行；</view>
				</view>
			</view>

			<view class="flex-row alc juc mart-30 padb-30">
				<image src="../static/pingan_new/pingan.png" style="width: 142rpx;height: 55rpx;"></image>
				<view class="content-box-bor"></view>
				<image src="../static/pingan_new/chezhu.png" style="width: 142rpx;height: 55rpx;"></image>
			</view>
		</view>


		<van-dialog id="van-dialog" />

	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';

	export default {
		data() {
			return {
				name: '', // 姓名
				idcard: '', // 身份证号
				thirdId: '', // 查询资格时返回的id
				loading: false,
				showDialog: false,
			}
		},
		// 转发到朋友
		onShareAppMessage: function (res) {
		    return {
		      title: '3元购10元停车券 立即可用！',
		      path: `/pagesA/pingan_new/index`
		    }
		},
		// 分享到朋友圈
		onShareTimeline: function () {
			return {
			  title: '平安银行新户专享 3元购10元停车券 立即可用！',
			  // imageUrl: this.item.picUrl + '?imageView2/0/w/300',
			  query: `/pagesA/pingan_new/index`
			}
		},
		onLoad () {
		},
		methods: {
			goHome () {
				uni.navigateBack({
					fail: (err)=> {
						uni.reLaunch({
							url: '/pagesA/park/index',
						})
					}
				});
			},
			clickBtn () {
				uni.navigateTo({
					url: `/pages/pingan_new/open`,
				})
			},
			btnClick () {
				if (!this.name) {
					uni.showToast({
						title: '请先输入姓名',
						icon: "none"
					})
					return;
				}
				if (!this.idcard) {
					uni.showToast({
						title: '请先输入身份证号',
						icon: "none"
					})
					return;
				}
				this.loading = true;
				let params = {
					customerName: this.name,
					cardId: this.idcard,
				}
				apis.homeApis.isnewuser(params).then((res) => {
					this.loading = false;
					console.log(res);
					if (res.status === 200 && res.data.isNewUser === 'Y') {
						uni.showToast({
							title: '操作成功',
							icon: "success"
						})
						this.thirdId = res.data.thirdId;
						uni.navigateTo({
							url: `/pagesA/pingan/open?name=${this.name}&idcard=${this.idcard}&thirdId=${this.thirdId}`,
						})
					} else {
						this.showDialog = true;
					}
				}).catch(() => {
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		height: 1880rpx;
		background-color: #DE572D;
		/* background: linear-gradient(to bottom, #FD6630, #FD6630, #FD6630, #FD6630, #F95B23, #F65015, #F24507); */
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 410rpx;
	}

	.pingan-desc {
		color: #fcd7b2;
		padding: 32rpx;
		margin-top: 30rpx;
		font-size: 24rpx;
	}
	.pingan-desc-title {
		font-size: 28rpx;
		margin-bottom: 20rpx;
	}
	.pingan-desc-content {
		line-height: 150%;
	}

	.home-box {
		background-color: #fff;
		border-radius: 20rpx;
		height: 380rpx;
		margin: 30rpx;
	}
	.home-box-title {
		position: absolute;
		top: -40rpx;
		width: 100%;
		text-align: center;
	}
	.home-box-title-text {
		color: #fff;
		width: 480rpx;
		height: 80rpx;
		letter-spacing: 2rpx;
		font-weight: bold;
		line-height: 80rpx;
		background-color: #FA5014;
		border-radius: 50rpx 0 50rpx 50rpx;
		box-shadow:0px 0px 10rpx 0px rgba(233, 79, 24, 0.43);
	}
	.home-box-desc {
		font-size: 28rpx;
		font-weight: 800;
		color: #F75D27;
		padding-top: 80rpx;
		text-align: center;
	}
	.home-box-par {
		width: 200rpx;
	}
	.home-box-item {
		width: 150rpx;
		height: 150rpx;
		border-radius: 10rpx 90rpx 10rpx 10rpx;
		background-color: #F66634;
		box-shadow:0px 0px 30rpx 0px #FDD8CC;
	}
	.home-box-text {
		color: #666;
		font-size: 26rpx;
		margin-top: 28rpx;
	}
</style>
<style>
	.content-box-btn2 button{
		border: none!important;
		background-color: rgba(255,255,255,0)!important;
		color: #EE5018!important;
		font-weight: bold;
		font-size: 40rpx!important;
		width: 480rpx!important;
		height: 130rpx!important;
		padding-bottom: 8rpx;
		letter-spacing: 4rpx;
		position: absolute;
		top: 0;
		left: 0;
	}
</style>
