<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block padding=" " title='基础用法' ref='saveChildRef1'>
    <van-button type='danger' onClick='showNotify' ref='saveChildRef2'>
      基础用法
    </van-button>
  </demo-block>
  <demo-block padding=" " title='通知类型' ref='saveChildRef3'>
    <view class='demo-margin-bottom'>
      <van-button class='demo-margin-right' type='info' data-type='primary' onClick='showNotifyByType' ref='saveChildRef4'>
        主要通知
      </van-button>
      <van-button type='primary' data-type='success' onClick='showNotifyByType' ref='saveChildRef5'>
        成功通知
      </van-button>
    </view>
    <view class='demo-margin-bottom'>
      <van-button class='demo-margin-right' type='danger' data-type='danger' onClick='showNotifyByType' ref='saveChildRef6'>
        危险通知
      </van-button>
      <van-button type='warning' data-type='warning' onClick='showNotifyByType' ref='saveChildRef7'>
        警告通知
      </van-button>
    </view>
  </demo-block>
  <demo-block padding=" " title='自定义通知' ref='saveChildRef8'>
    <van-button type='primary' class='demo-margin-right' onClick='showCustomColor' ref='saveChildRef9'>
      自定义颜色
    </van-button>
    <van-button type='primary' onClick='showCustomDuration' ref='saveChildRef10'>
      自定义时长
    </van-button>
  </demo-block>
  <demo-block padding=" " title='插入状态栏高度' ref='saveChildRef11'>
    <van-button type='primary' class='demo-margin-right' onClick='showSafe' ref='saveChildRef12'>
      插入状态栏高度
    </van-button>
  </demo-block>
  <van-notify id='van-notify' ref='saveChildRef13'>
  </van-notify>
</view>