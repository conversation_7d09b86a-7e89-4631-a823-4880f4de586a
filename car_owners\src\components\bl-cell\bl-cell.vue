<template>
	<button class="bl-cell-btn" 
	:open-type="verifyPhone?'getPhoneNumber':''"
	@getphonenumber="getPhoneNumber"
	@click="goto"
	>
		<view class="bl-cell" :class="customCellClass">
			<view class="bl-cell--icon" v-if="isIcon">
				<image mode="aspectFit" :src="icon" class="bl-cell--image"></image>
			</view>
			<view class="bl-cell--text" v-if="isTitle">{{title}}</view>
			<view class="bl-cell--tag" v-if="$slots.custom">
				<slot name="custom"></slot>
			</view>
			<view class="bl-cell--arrow" v-if="showArrow">
				<van-icon name="arrow" color="#DDDDDD" size="34rpx" v-if="arrow === 'icon'"/>
				<image mode="aspectFit" src="http://image.bolink.club/FpSWEdRu4M4x_xjqBuVRjp6-Jtid" v-else></image>
			</view>
		</view>
	</button>
</template>
<script>
	export default {
		name: 'BlCell',
		props: {
			// 是否线上右箭头
			showArrow:{
				type: Boolean,
				default: true
			},
			verifyPhone: Boolean,
			icon: String,
			title: String,
			arrow: {
				type: String,
				default: 'image'
			},
			jumpType: {
				type: String,
				default: 'navigateTo'
			},
			path: String,
			// 自定义最外层样式
			customCellClass: String,
		},
		computed: {
			isIcon() {
				return this.icon && this.icon !== '';
			},
			isTitle() {
				return this.title && this.title !== '';
			}
		},
		data() {
			return {}
		},
		methods: {
			goto() {
				if (this.verifyPhone) {return false}
				if (this.path === '') {return false}
				if (this.jumpType === "navigateTo") {
					let startTime = new Date().getTime();
					uni.navigateTo({
					    url: this.path,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: `${this.title}`,
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					});
				}
				else {
					uni.navigateTo({
					    url: this.path
					});
				}
				
			},
			getPhoneNumber(evt) {
				this.$emit('getphonenumber',evt)
			}
		}
	}
</script>
<style lang="less" scoped>
	.bl-cell-btn {
		display: block;
		line-height: 1;
	}
	.bl-cell {
		display: flex;
		align-items: center;
		padding: 16rpx 0;
		&--icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 35rpx;
			height: 35rpx;
			overflow: hidden;
		}
		&--image {
			margin: 0;
			padding: 0;
			max-height: 100%;
		}
		&--text {
			margin: 0 21rpx;
			flex: 1;
			font-weight: 500;
			font-size: 32rpx;
			color: #242e3e;
		}
		&--arrow {
			image {
				margin: 0;
				padding: 0;
				width: 48rpx;
				height: 48rpx;
			}
		}
	}
</style>