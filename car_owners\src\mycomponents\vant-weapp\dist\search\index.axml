<view class='search-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{ utils.bem('search', { withaction: showAction || useActionSlot }) }} {{customClass}}" style='background: {{ background }}'>
    <view class="{{ utils.bem('search__content', [shape]) }}">
      <view class='van-search__label' a:if='{{ label }}'>
        {{ label }}
      </view>
      <slot a:else  name='label'>
      </slot>
      <van-field type='search' left-icon="{{ !useLeftIconSlot ? leftIcon : '' }}" right-icon="{{ !useRightIconSlot ? rightIcon : '' }}" focus='{{ focus }}' error='{{ error }}' border='{{ false }}' confirm-type='search' class='van-search__field {{fieldClass}}' value='{{ value }}' disabled='{{ disabled }}' readonly='{{ readonly }}' clearable='{{ clearable }}' maxlength='{{ maxlength }}' input-align='{{ inputAlign }}' input-class='{{inputClass}}' placeholder='{{ placeholder }}' placeholder-style='{{ placeholderStyle }}' custom-style='padding: 5px 10px 5px 0; background-color: transparent;' onBlur='onBlur' onFocus='onFocus' onChange='onChange' onConfirm='onSearch' onClear='onClear' ref='saveChildRef1'>
        <slot a:if='{{ useLeftIconSlot }}' name='left-icon' slot='left-icon'>
        </slot>
        <slot a:if='{{ useRightIconSlot }}' name='right-icon' slot='right-icon'>
        </slot>
      </van-field>
    </view>
    <view a:if='{{ showAction || useActionSlot }}' class='van-search__action' hover-class='van-search__action--hover' hover-stay-time='70'>
      <slot a:if='{{ useActionSlot }}' name='action'>
      </slot>
      <view a:else  class='{{cancelClass}}' onTap='antmoveAction' data-antmove-tap='onCancel'>
        {{ actionText }}
      </view>
    </view>
  </view>
</view>