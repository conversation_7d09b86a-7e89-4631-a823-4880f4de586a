const PubSub = require('pubsub-js')
const ultFrame = class ultFrame {
  head //头8866 2
  frameLength //字节个数 int 1
  frameack //帧回复 0 发送，1回复
  frameNum //帧号 int 1
  totalPackage //总包数 int 2
  currentPackage //当前包 int 2
  data //数据域 hex N
  check //校验域 int 1
  constructor() {}
  static getNewFrame(frameNum, data, totalPackage, currentPackage) {
    if (!data || data.length % 2 != 0) {
      console.log("传入数据非法：" + data)
      return ""
    }
    var reFrame = new ultFrame()
    reFrame.head = "8866"
    reFrame.data = data
    reFrame.frameack = 0
    reFrame.frameNum = frameNum
    reFrame.totalPackage = totalPackage
    reFrame.currentPackage = currentPackage
    reFrame.frameLength = data.length / 2 + 10
    return reFrame
  }
  getFrameHexString() {
    var hex =
      this.head +
      ultFrame.paresHexbyInt(this.frameLength, 1) +
      ultFrame.paresHexbyInt(this.frameack, 1) +
      ultFrame.paresHexbyInt(this.frameNum, 1) +
      ultFrame.paresHexbyInt(this.totalPackage, 2) +
      ultFrame.paresHexbyInt(this.currentPackage, 2) +
      this.data
    hex = hex + ultFrame.checkAccrual(hex)
    return hex
  }
  static checkAccrual(hex) {
    var checkCount = 0
    if (hex && hex.length > 0 && hex.length % 2 == 0) {
      //计算累加和
      for (var i = 0; i < hex.length / 2; i++) {
        checkCount += parseInt(hex.substring(2 * i, 2 * i + 2), 16)
      }
    } else {
      console.log("计算累加和16进制数无效，16进制数" + hex)
    }
    checkCount = checkCount & 0xff
    return ultFrame.paresHexbyInt(checkCount, 1)
  }
  static paresHexbyInt(num, digit) {
    var re = num.toString(16)
    if (re.length % 2 != 0) {
      re = "0" + re
    }
    var temp = ""
    for (var i = 0; i < digit; i++) {
      if (re.length > 2 * i) {
        temp += re.substring(re.length - 2 - i * 2, re.length - i * 2)
      } else {
        temp += "00"
      }
    }
    return temp
  }
  static getByFrame(frame) {
    var re = new ultFrame()
    re.head = frame.substring(0, 4)
    re.frameLength = ultFrame.getNum(frame.substring(4, 6))
    re.frameack = ultFrame.getNum(frame.substring(6, 8))
    re.frameNum = ultFrame.getNum(frame.substring(8, 10))
    re.totalPackage = ultFrame.getNum(frame.substring(10, 14))
    re.currentPackage = ultFrame.getNum(frame.substring(14, 18))
    re.data = frame.substring(18, frame.length - 2)
    re.check = frame.substring(frame.length - 2)
    return re
  }
  static getNum(hex) {
    var s = ""
    for (var i = 0; i < hex.length / 2; i++) {
      s += hex.substring(hex.length - 2 - i * 2, hex.length - i * 2)
    }
    if (s == "" || s.length < 1) {
      s = "0"
    }
    return parseInt(s, 16)
  }
}

//负责蓝牙帧解析
const bluetoothFrameHalder = class bluetoothFrameHalder {
  #head = "8866"
  #decodeTime = 3 //只重复解析连续3个包匹配，否则丢弃
  #currentTime = 0
  remainhex = ""

  decode(hex, handle) {
    try {
      console.log("decode剩余："+this.remainhex+" hex:"+hex);
      var allFrame = this.remainhex + hex
      this.remainhex = allFrame
      // console.log("decode："+this.remainhex+" hex:"+hex);
      var idx = allFrame.indexOf(this.#head)
      if (idx < 0) {
        if (this.#currentTime < this.#decodeTime) {
          this.#currentTime++
        } else {
          console.log("丢弃超包数据:" + this.remainhex)
          this.remainhex = ""
          this.#currentTime = 0
        }
      } else {
        if (idx > 0) {
          var dropHex = allFrame.substring(0, idx)
          console.log("丢弃多余数据:" + dropHex)
        }
        allFrame = allFrame.substring(idx)
        this.remainhex = allFrame
       let length = parseInt(allFrame.substring(4, 6), 16)
         if (allFrame.length >= length * 2) {
          //处理帧数据
          var cFrame = allFrame.substring(0, length * 2)
          this.remainhex = allFrame.substring(length * 2)
          this.#currentTime = 0
          var checkstr = cFrame.substring(cFrame.length - 2)
          var relCheck = ultFrame.checkAccrual(
            cFrame.substring(0, cFrame.length - 2)
          )
          if (checkstr == relCheck) {
            handle.handleFrame(ultFrame.getByFrame(cFrame))
          } else {
            console.log("校验和不正确丢弃：" + cFrame)
          }
          this.decode("", handle)
        }
      }
    } catch (err) {
      console.log('decode报错',err)
    }
  }
}

const bluetoothHalder  =  class bluetoothHalder {
  #frameList
  frameDecodeHandle
  constructor(decodeHandle) {
      this.frameDecodeHandle = decodeHandle;
      this.#frameList = []
  }
  /**
   * hex 收到16进制文件
   * hadle 处理帧数据文件 入参 ultFrame
   */
  receive(hex) {
      this.frameDecodeHandle.decode(hex, this)
  }
  //默认顺序流
  handleFrame(frame) {
      if (this.#frameList && this.#frameList.length > 0) {
          if (this.#frameList[0].frameNum != frame.frameNum) {
              //清除帧
              console.log("帧号不匹配，清除帧信息")
              this.#frameList = []
          }
      }
        //回复包
      if (frame.frameack == 0) {
          this.#frameList[this.#frameList.length] = frame
          if (frame.totalPackage == this.#frameList.length) {
              //生成数据包
              var dataHex = '';
              for (var i = 0; i < this.#frameList.length; i++) {
                  dataHex += this.#frameList[i].data
              }
              try {
                  this.handleBigFrame(dataHex)
              } catch (err) {
                  console.log("帧处理异常" + err)
              }
              this.#frameList = []
          }

          var reFrame = ultFrame.getNewFrame(frame.frameNum, "00", frame.totalPackage, frame.currentPackage);
          this.answer(reFrame.getFrameHexString())
      }else{
          this.deviceReciveAnwser(frame)
      }
  }
  //设备收到回复
  deviceReciveAnwser(frame){
      console.log("设备应答帧："+frame.getFrameHexString())
  }
  //hex：16进制报文
  answer(hex) {
      //回复设备
      console.log("回复设备" + hex)


  }

  //处理合并好得报文
  handleBigFrame(hex) {
      console.log("处理合并好得报文bigFrame:" + hex)
      explainHex(hex)

  }
}

//指令解析
const explainHex = (Hex) => {
  console.log('指令解析', Hex)
  let params = {
    Hex
  }
   let PubSubExplainHexToken=PubSub.publish('explainHex', Hex)
   wx.setStorageSync('PubSubExplainHexToken',PubSubExplainHexToken)

}
module.exports = {
  bluetoothHalder,
  bluetoothFrameHalder,
  ultFrame
}