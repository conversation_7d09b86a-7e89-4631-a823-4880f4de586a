<template>
	<view class="pay-container">
		<custom-nav-bar title="授权登录"></custom-nav-bar>
		<view class="btn">			
			<button class="bl-button bl-button--default marb-40" @click="goBack">返回首页</button>
			<button class="bl-button bl-button--primary" hover-class="bl-button--primary--hover" type="info" open-type="launchApp" :app-parameter="JSON.stringify(form)" @error="launchAppError">返回APP</button>
		</view>
		<van-dialog id="van-dialog"></van-dialog>
	</view>
</template>
<script>
	import apis from '../../common/apis/index'
	import util from "../../common/utils/util.js"
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	export default {
		components: {},
		data() {
			return {
				form: {},
				params: {}
			}
		},
		onLoad(options) {
			if(options.params){
				this.params = JSON.parse(decodeURIComponent(options.params))
			}
			this.form = {
				is: false
			}
			console.log(this.params,options.params)
			this.bindMobile()
            
		},
		onShow(options){
			
		},
		onReady() {
			
		},
		methods: {
			goBack(){
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			bindMobile(isUpdate=0){
				let { mobile } = this.params
				let data = {
					mobile: mobile,
					openId: uni.getStorageSync('openId'),
					isUpdate: isUpdate
				}
				console.log(data,'参数')
				apis.homeApis.bindWxOpeid(data).then(res=>{
					console.log(res)
					if(res.status==223){
						Dialog.confirm({
						  title: '更换绑定',
						  message: res.msg,
						}).then(() => {
							this.bindMobile(1)
						})
					}else if(res.status==200){
						uni.showToast({
							title:"绑定成功，请点击返回APP!",
							icon:"none"
						})
					}else {
						uni.showToast({
							title: res.message || res.msg || '绑定失败！',
						})
					}
				}).catch(err => {
					console.log(err)
				})
				
			}
		}
	}
</script>
<style lang="less" scoped>
	.pay-container{
		.main{
			margin-top: 176rpx;
		}
		.btn{
			position: fixed;
			width: 670rpx;
			bottom: 200rpx;
			margin: 0 40rpx;
			text-align: center;
			color: #FFF;
		}
	}
</style>
