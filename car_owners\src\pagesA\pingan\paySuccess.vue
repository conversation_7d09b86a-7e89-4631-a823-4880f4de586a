<template>
	<view class="pingan">
		
		<view style="position: relative;">
			<image style="width: 100%;height: 694rpx" src="../static/pingan_new/top-bg.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
			
			<view class="my-icon">
				<image v-if="status==4" src="../static/pingan_new/success.png" style="width: 148rpx;height: 240rpx;"></image>
				<image v-else-if="status==3" src="../static/pingan_new/fail.png" style="width: 148rpx;height: 240rpx;"></image>
				<view v-else>
					<van-button size="large" custom-class="my-loding-btn" loading loading-text="支付中,请稍候..." />
				</view>
				<view v-if="status==4" class="mart-20">支付成功</view>
				<view v-else-if="status==3" class="mart-20">扣款失败</view>
			</view>
		</view>
		
		
		<view class="main-cotain" v-if="status==4">
			<view class="input-box">
				<view class="top-long">
					<image src="../static/pingan_new/long.png" style="width: 100%;height: 60rpx;"></image>
				</view>
				<view class="top-long-title">优惠券</view>
				<view class="flex-row juc">
					<view class="coupon-item flex-col flex-1 ajc">
						<view class="coupon-item-img">
							<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
							<view class="coupon-item-name">停车券</view>
							<view class="coupon-item-price flex-row ale">
								<view class="coupon-item-price-d">￥</view>
								<view class="coupon-item-price-num">3</view>
								<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
							</view>
							<view class="coupon-item-price-desc">满3.01元可用</view>
						</view>
						<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
					</view>
					<view class="coupon-item flex-col flex-1 ajc">
						<view class="coupon-item-img">
							<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 172rpx;height: 243rpx;"></image>
							<view class="coupon-item-name-c">停车券</view>
							<view class="coupon-item-price-c flex-row ale">
								<view class="coupon-item-price-d">￥</view>
								<view class="coupon-item-price-num">5</view>
								<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
							</view>
							<view class="coupon-item-price-desc-c">满5.01元可用</view>
						</view>
						<image src="../static/pingan_new/coupon-arr2.png" style="width: 55rpx;height: 56rpx;"></image>
					</view>
					<view class="coupon-item flex-col flex-1 ajc">
						<view class="coupon-item-img">
							<image src="https://image.bolink.club/yima/ping_an_coupon_bg.png" style="width: 160rpx;height: 226rpx;"></image>
							<view class="coupon-item-name">停车券</view>
							<view class="coupon-item-price flex-row ale">
								<view class="coupon-item-price-d">￥</view>
								<view class="coupon-item-price-num">2</view>
								<view class="coupon-item-price-icon"><image src="../static/pingan_new/coupon-icon1.png" style="width: 64rpx;height: 64rpx;"></image></view>
							</view>
							<view class="coupon-item-price-desc">满2.01元可用</view>
						</view>
						<image src="../static/pingan_new/coupon-arr.png" style="width: 80rpx;height: 55rpx;"></image>
					</view>
				</view>
				<view class="mart-30">
					<van-button
						v-if="mobile"
						customClass="my-submit-btn"
						@click="submit(mobile)"
						block
						round 
						color="#F65C27"
						>领取停车券礼包</van-button>
					<van-button
						v-else
						customClass="my-submit-btn"
						open-type="getPhoneNumber" 
						@getphonenumber="getPhoneNumber"
						block
						round 
						color="#F65C27"
						>领取停车券礼包</van-button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	
	export default {
		data() {
			return {
				payTimer: null,
				payCount: 0,
				status: 1,
				rechargeId: null,
				mobile: ''
			}
		},
		onLoad (options) {
			this.mobile = uni.getStorageSync('mobile');
			uni.showLoading({
				title: '支付中,请稍候...'
			})
			if ('thirdId' in options) {
				this.payCount = 0;
				this.payTimer = setInterval(() => {
					this.queryStatus(options.thirdId, options.businessNo);
				}, 800)
			}
		},
		onUnload () {
			clearInterval(this.payTimer);
		},
		methods: {
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			submit (mobile) {
				let params = {
					rechargeId: this.rechargeId,
					mobile: mobile,
				};
				apis.homeApis.operasendcoupon(params).then((res) => {
					if (res.status === 200) {
						this.btnClick();
					} else {
						uni.showToast({
							title: res.msg || '服务器异常，请稍候再试',
							icon: 'none'
						});
					}
				})
			},
			// 获取手机号
			getPhoneNumber (e) {
				getApp().getPhoneNumber(e, this).then(res => {
					console.log('getPhoneNumber-->', res);
					if (res) {
						this.submit(res.mobile);
					} else {
						uni.showToast({
							title: '必须授权才能领优惠券',
							icon: 'none',
							duration: 5000
						})
					}
				})
			},
			// 查询支付的状态
			queryStatus (thirdId, businessNo) {
				let params = {
					thirdId: thirdId,
					businessNo: businessNo,
				};
				apis.homeApis.transferInResultQuery(params).then((res) => {
					this.payCount += 1;
					if (res.status === 200) {
						this.status = res.data.status || 1;
						if (res.data.status == 3) {
							uni.hideLoading();
							clearInterval(this.payTimer);
							// this.loading = false;
							uni.showToast({
								title: '扣款失败，请稍候再试',
								icon: 'none'
							});
						} else if (res.data.status == 4) {
							this.rechargeId = res.data.rechargeId;
							clearInterval(this.payTimer);
							uni.hideLoading();
							// this.loading = false;
						}
					}
					if (this.payCount > 120) {
						uni.showToast({
							title: '系统繁忙，请稍候再试',
							icon: 'none'
						});
						// this.loading = false;
						clearInterval(this.payTimer);
					}
				}).catch(res => {
					this.payCount += 1;
				})
			},
			btnClick () {
				uni.reLaunch({
					url: "/pagesE/user/mycoupon",
					complete: (res) => {
						getApp().eventRecord({
							keyWord: '平安银行-支付成功-我的停车劵',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '停车劵页面',
							result: res.errMsg == 'reLaunch:ok' ? '成功' : '失败',
							startTime: new Date().getTime(),
							endTime: new Date().getTime()
						});
					}
				})
			}
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		background-color: #fff;
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 600rpx;
	}
	.my-icon {
		width: 100%;
		color: #fff;
		text-align: center;
		position: absolute;
		top: 200rpx;
	}
	.top-long {
		width: 100%;
		position: absolute;
		top: -20rpx;
		left: 0;
	}
	.top-long-title {
		text-align: center;
		letter-spacing: 4rpx;
		font-size: 36rpx;
		font-weight: 800;
		color: #FD5D2D;
		margin-bottom: 40rpx;
	}
	.input-box {
		padding: 40rpx 40rpx 60rpx 40rpx;
		margin: 0 60rpx;
		border-radius: 0 0 20rpx 20rpx;
		background-color: #fff;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
	}
	
</style>
<style>
	.my-button button{
		border: none!important;
	}
	.my-loding-btn {
		color: #fff!important;
		border: none!important;
		background-color: rgba(255,255,255,0)!important;
		position: absolute;
		top: 0;
		left: 0;
	}
</style>
