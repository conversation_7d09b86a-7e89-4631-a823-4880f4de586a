<template>
	<view class="">
	</view>
</template>
<script>
	export default {
			data() {
				return {
					
				}
			},
			
			onLoad(option) {
				console.log('option',option)
				if(option.callbackUrl){
					let url = decodeURIComponent(option.callbackUrl)
						console.log('url',option)

					uni.navigateTo({
						url:url
					})
				}
			},
			methods: {
				
			},
		}
	
</script>

<style>
</style>