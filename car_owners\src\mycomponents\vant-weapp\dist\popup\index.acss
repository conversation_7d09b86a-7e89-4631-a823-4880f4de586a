.popup-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.popup-index .van-popup {
    position: fixed;
    box-sizing: border-box;
    max-height: 100%;
    overflow-y: auto;
    transition-timing-function: ease;
    -webkit-animation: ease both;
    animation: ease both;
    -webkit-overflow-scrolling: touch;
    background-color: #fff;
    background-color: var(--popup-background-color, #fff);
}

.popup-index .van-popup--center {
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
}

.popup-index .van-popup--center.van-popup--round {
    border-radius: 16px;
    border-radius: var(--popup-round-border-radius, 16px);
}

.popup-index .van-popup--top {
    top: 0;
    left: 0;
    width: 100%;
}

.popup-index .van-popup--top.van-popup--round {
    border-radius: 0 0 16px 16px;
    border-radius: 0 0 var(--popup-round-border-radius, 16px)
        var(--popup-round-border-radius, 16px);
}

.popup-index .van-popup--right {
    top: 50%;
    right: 0;
    -webkit-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0);
}

.popup-index .van-popup--right.van-popup--round {
    border-radius: 16px 0 0 16px;
    border-radius: var(--popup-round-border-radius, 16px) 0 0
        var(--popup-round-border-radius, 16px);
}

.popup-index .van-popup--bottom {
    bottom: 0;
    left: 0;
    width: 100%;
}

.popup-index .van-popup--bottom.van-popup--round {
    border-radius: 16px 16px 0 0;
    border-radius: var(--popup-round-border-radius, 16px)
        var(--popup-round-border-radius, 16px) 0 0;
}

.popup-index .van-popup--left {
    top: 50%;
    left: 0;
    -webkit-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0);
}

.popup-index .van-popup--left.van-popup--round {
    border-radius: 0 16px 16px 0;
    border-radius: 0 var(--popup-round-border-radius, 16px)
        var(--popup-round-border-radius, 16px) 0;
}

.popup-index .van-popup--bottom.van-popup--safe {
    padding-bottom: env(safe-area-inset-bottom);
}

.popup-index .van-popup--safeTop {
    padding-top: env(safe-area-inset-top);
}

.popup-index .van-popup__close-icon {
    position: absolute;
    z-index: 1;
    z-index: var(--popup-close-icon-z-index, 1);
    color: #969799;
    color: var(--popup-close-icon-color, #969799);
    font-size: 18px;
    font-size: var(--popup-close-icon-size, 18px);
}

.popup-index .van-popup__close-icon--top-left {
    top: 16px;
    top: var(--popup-close-icon-margin, 16px);
    left: 16px;
    left: var(--popup-close-icon-margin, 16px);
}

.popup-index .van-popup__close-icon--top-right {
    top: 16px;
    top: var(--popup-close-icon-margin, 16px);
    right: 16px;
    right: var(--popup-close-icon-margin, 16px);
}

.popup-index .van-popup__close-icon--bottom-left {
    bottom: 16px;
    bottom: var(--popup-close-icon-margin, 16px);
    left: 16px;
    left: var(--popup-close-icon-margin, 16px);
}

.popup-index .van-popup__close-icon--bottom-right {
    right: 16px;
    right: var(--popup-close-icon-margin, 16px);
    bottom: 16px;
    bottom: var(--popup-close-icon-margin, 16px);
}

.popup-index .van-popup__close-icon:active {
    opacity: 0.6;
}

.popup-index .van-scale-enter-active,
.popup-index .van-scale-leave-active {
    transition-property: opacity, -webkit-transform;
    transition-property: opacity, transform;
    transition-property: opacity, transform, -webkit-transform;
}

.popup-index .van-scale-enter,
.popup-index .van-scale-leave-to {
    -webkit-transform: translate3d(-50%, -50%, 0) scale(0.7);
    transform: translate3d(-50%, -50%, 0) scale(0.7);
    opacity: 0;
}

.popup-index .van-fade-enter-active,
.popup-index .van-fade-leave-active {
    transition-property: opacity;
}

.popup-index .van-fade-enter,
.popup-index .van-fade-leave-to {
    opacity: 0;
}

.popup-index .van-center-enter-active,
.popup-index .van-center-leave-active {
    transition-property: opacity;
}

.popup-index .van-center-enter,
.popup-index .van-center-leave-to {
    opacity: 0;
}

.popup-index .van-bottom-enter-active,
.popup-index .van-bottom-leave-active,
.popup-index .van-left-enter-active,
.popup-index .van-left-leave-active,
.popup-index .van-right-enter-active,
.popup-index .van-right-leave-active,
.popup-index .van-top-enter-active,
.popup-index .van-top-leave-active {
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
}

.popup-index .van-bottom-enter,
.popup-index .van-bottom-leave-to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
}

.popup-index .van-top-enter,
.popup-index .van-top-leave-to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
}

.popup-index .van-left-enter,
.popup-index .van-left-leave-to {
    -webkit-transform: translate3d(-100%, -50%, 0);
    transform: translate3d(-100%, -50%, 0);
}

.popup-index .van-right-enter,
.popup-index .van-right-leave-to {
    -webkit-transform: translate3d(100%, -50%, 0);
    transform: translate3d(100%, -50%, 0);
}
