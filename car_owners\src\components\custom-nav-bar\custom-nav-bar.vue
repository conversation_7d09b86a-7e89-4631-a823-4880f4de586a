<template>
	<view
		class="custom-nav-bar"
		:class="customClass"
		:style="{top: statusBarHeight+'px',height: navHeight+'px'}"
	>
		<view class="title" :style="{height: navHeight+'px','line-height': navHeight+'px'}">
			<van-icon @click="goBack(url, tabBar)" :name="name" :color="color" size="36rpx" />
			<view @click="goBack(url, tabBar)" :style="{'color':color}" :class="titleClass" >{{title}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"custom-nav-bar",
		props: {
			title: {
				type: String,
				default: '返回'
			},
			color: {
				type: String,
				default: '#242e3e'
			},
			name: {
				type: String,
				default: 'arrow-left'
			},
			titleClass: {
				type: String,
				default: 'title--text'
			},
			url: String,
			tabBar:String,
			customClass: String,
			customBack: Boolean,
		},
		data() {
			return {
				windowHeight: null,
				statusBarHeight: 47,
				navHeight: 40,
			};
		},
		created() {
			// #ifdef H5
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
				// #ifdef APP-PLUS
				this.statusBarHeight = 40;
				// #endif
			});
			// #endif
		},
		onReady() {
			getApp().getSystemInfo().then(res => {
				console.log(res)
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
		},
		onLoad() {
			
		},
		methods: {
			goBack(url,tabBar) {
				if (tabBar) {
					if(tabBar.includes('activeName')){
						wx.reLaunch({
						  url:tabBar
						})
						return
					}
					uni.switchTab({
						url: tabBar,
						fail: (err) => {
							console.log('跳转失败',err)		
							uni.redirectTo({
								url: tabBar,
							})
							
						},
					})
					return false
			}
				if(url){
					uni.navigateTo({
						url,
						fail: err=>{
							uni.redirectTo({
								url
							})
						}
					})
					return false;
				}
				if (this.custom) {
					return false;
				}
				let pages = getCurrentPages();//当前页面
   			let beforePage = pages[pages.length-2];//前一页
				 beforePage=beforePage?beforePage.route:''
				// #ifndef H5
				if(beforePage=='pagesF/code/index'){
					console.log('nav-bar空白码',beforePage)
					// 空白码跳转到开始充电页面返回首页
					uni.switchTab({
								url:'/pages/index/index'
							})
				}else{
					uni.navigateBack({
					delta: 1,
					fail:(err)=>{
						console.log('nav-bar',err)
						uni.switchTab({
								url:'/pages/index/index'
							})
					}
				});

				}

				// #endif
				// #ifdef H5
				history.back()
				// #endif
			}
		}
	}
</script>

<style lang="less">
	.custom-nav-bar {
		position: fixed;
		z-index: 9;
		left: 38rpx;
		.title {
			display: flex;
			font-size: 34rpx;
			font-weight: 700;
			&--text {
				margin-left: 6rpx;
			}
			&--center {
				margin-left: 6rpx;
				width: 590rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
</style>
