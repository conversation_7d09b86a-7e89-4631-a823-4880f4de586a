{"version": 3, "file": "GLmethod.js", "sourceRoot": "", "sources": ["../../../../../packages/plugins/gcanvas/gcanvas/context-webgl/GLmethod.js"], "names": [], "mappings": "AAAA,IAAI,CAAC,GAAG,CAAC,CAAC;AAEV,MAAM,QAAQ,GAAG,EAAE,CAAC;AAEpB,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC,CAAS,GAAG;AACzC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAClC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3B,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI;AAC1C,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;AACtC,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;AACrB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAc,IAAI;AAC3C,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;AACpC,QAAQ,CAAC,uBAAuB,GAAG,CAAC,EAAE,CAAC;AACvC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;AAC9B,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAClC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAW,IAAI;AAC3C,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;AACxB,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAClC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAc,IAAI;AAC3C,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;AACvB,QAAQ,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,0BAA0B,GAAG,CAAC,EAAE,CAAC;AAC1C,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;AACtB,QAAQ,CAAC,uBAAuB,GAAG,CAAC,EAAE,CAAC,CAAI,IAAI;AAC/C,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;AACrB,QAAQ,CAAC,uBAAuB,GAAG,CAAC,EAAE,CAAC;AACvC,QAAQ,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;AACpC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;AAC9B,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAClC,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,CAAS,IAAI;AAC/C,QAAQ,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;AACpC,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;AACxB,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,iCAAiC,GAAG,CAAC,EAAE,CAAC;AACjD,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;AACnC,QAAQ,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,CAAS,IAAI;AAC/C,QAAQ,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;AACtC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAClC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;AACrC,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;AACxB,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC,CAAc,IAAI;AAC/C,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;AAC9B,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;AACxB,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3B,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC,CAAc,IAAI;AAC/C,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;AACnC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;AAC9B,QAAQ,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;AACvB,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;AAC5B,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3B,QAAQ,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;AACnC,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3B,QAAQ,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;AACnC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAkB,KAAK;AAChD,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;AACjC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;AAC7B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAkB,KAAK;AAChD,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;AACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAkB,KAAK;AAChD,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;AAChC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;AAC1B,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACpC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACpC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACpC,QAAQ,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,iBAAiB;AAChD,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACrC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACrC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACrC,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACrC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;AACnC,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;AAExB,eAAe,QAAQ,CAAC"}