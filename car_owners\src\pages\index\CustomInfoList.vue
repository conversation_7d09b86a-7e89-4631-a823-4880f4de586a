<template>
	<view>
	<scroll-view class="custom-info-list" :scroll-y="true" @scrolltolower="scrolltolower" :style="{height:scrollHeight}">	
	<view>
		<custom-panel 
		v-for="(item, index) in dataList"
		:key="index"
		:title="index=== 0?title:''">
			<view v-if="index=== 0" slot="operate" class="hot-news--all" @click="jumpInfo">
				<van-icon size="28rpx" name="arrow" style="position: relative;top: 3rpx"/>
			</view>
			<view class="hot-news" @click="jumpInfoDetail(item)">
				<view class="hot-news--img">
					<image :src="item.picUrl + '?imageView2/0/w/750'" mode="aspectFill" :lazy-load="true"></image>
				</view>
				<view class="hot-news--content">
					<view class="hot-news--title">{{item.title}}</view>
					<view class="hot-news--tip">{{item.source || item.typeName || ''}}</view>
					<view class="hot-news--foot">
						<view class="hot-news--date">{{form.timestampToTime(item.createTime, "date")}}</view>
						<view class="hot-news--collect" 
						@click.stop="praiseClick(item, index)"
						:class="{'collect-icon-active': item.praise==1}">
							{{item.praiseNum || 0}}
						</view>
					</view>
				</view>
			</view>
			
		</custom-panel>
		
		<!-- 初始加载 image -->
		<view v-show="false">
			<image src="https://image.bolink.club/front_page_collected%402x.png"></image>
		</view>
		</view>
		<view class="list-end">{{ isEnd ? '亲，到底了' : '加载中...' }}</view>
	</scroll-view>
	</view>
</template>
<script>
	import form from '../../common/utils/form.js';
	import CustomPanel from './CustomPanel.vue'
	export default {
		components: { CustomPanel },
		props: {
			title: String,
			dataList: {
				type: Array,
				default:() => {
					return []
				}
			},
			isEnd: {
				type: Boolean,
				default: false,
			},
			statusBarHeight:{
				type:Number,				
			},
			navHeight:{
				type:Number,				
			}
		},
		data() {
			return {
				form: form
			}
		},
		watch: {
			dataList: {
				handler(n) {
					console.log('dataList', n)
				},
				immediate: true
			}
		},
		computed:{
			/* 新闻资讯高度 */
			scrollHeight(){
				let {navHeight,statusBarHeight}=this
				let height=Number(navHeight+statusBarHeight)+130+'px'
				 return `calc(100vh - ${height});`

			},
		},
		methods: {
			// 滚到底部
				scrolltolower(){
					this.$emit('talkCarScroll')
					
				},
				praiseClick (item, index) {
					this.$emit('praiseClick', item, index)
				},
				// 跳转热点资讯页
				jumpInfo () {
					let startTime = new Date().getTime();
					uni.navigateTo({
						url: '/pagesA/index/info',
						complete: (res) => {
							getApp().eventRecord({
								keyWord: '首页-点击全部资讯',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '资讯列表页面',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				},
				jumpInfoDetail (item) {
					let startTime = new Date().getTime();
					uni.navigateTo({
						url: `/pagesA/index/infoDetail?id=${item.id}`,
						complete: (res) => {
							getApp().eventRecord({
								keyWord: '点击单个资讯',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '资讯详情页面',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				}
			}
		
	}
</script>
<style lang="less">
	.hot-news--all {
		float: right;
		font-size: 28rpx;
		font-weight: 500;
		color: #B0B6C1;
		z-index: 0;
	}
	.custom-info-list {
		width: 100%;
		z-index: 0;
		height:73vh;
		background-color: #fbfbfb;
		padding-bottom:130rpx;
	}
	.hot-news {
		display: flex;
		z-index: 0;
		// align-items: center;
		width: 100%;
		&--img {
			flex: 0 0 240rpx;
			height: 180rpx;
			border-radius: 20rpx;
			overflow: hidden;
			image {
				margin: 0; padding: 0;
				width: 100%;
				height: 100%;
			}
		}
		&--content {
			flex:1;
			display: block;
			margin-left: 22rpx;
			// height: 179rpx;
		}
		&--title {
			margin-bottom: 10rpx;
			line-height: 48rpx;
			font-weight: 700;
			font-size: 32rpx;
			text-align: left;
			color: #242E3E;
			text-overflow: -o-ellipsis-lastline;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		&--tip {
			margin-bottom: 10rpx;
			height: 37rpx;
			line-height: 37rpx;
			font-weight: 500;
			font-size: 30rpx;
			text-align: left;
			color: #828EA6;
		}
		&--foot {
			display: flex;
			justify-content: space-between;
			height: 33rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #BFC4CD;
		}
		&--collect {
			background-image: url('https://image.bolink.club/front_page_collect%402x.png');
			background-size: 37rpx 33rpx;
			background-position: left bottom;
			background-repeat: no-repeat;
			text-indent: 45rpx;
			transition: background-image 1s;
		}
		.collect-icon-active {
			background-image: url('https://image.bolink.club/front_page_collected%402x.png');
			background-size: 37rpx 33rpx;
			background-position: left bottom;
			background-repeat: no-repeat;
			text-indent: 45rpx;
			color: #FFAF47;
		}
	}

</style>