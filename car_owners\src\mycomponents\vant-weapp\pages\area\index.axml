<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block title='基础用法' ref='saveChildRef1'>
    <van-area value='{{ value }}' loading='{{ loading }}' area-list='{{ areaList }}' onChange='onChange' onConfirm='onConfirm' onCancel='onCancel' ref='saveChildRef2'>
    </van-area>
  </demo-block>
  <demo-block title='选中省市县' ref='saveChildRef3'>
    <van-area value='{{ value }}' loading='{{ loading }}' area-list='{{ areaList }}' onChange='onChange' onConfirm='onConfirm' ref='saveChildRef4'>
    </van-area>
  </demo-block>
  <demo-block title='配置显示列' ref='saveChildRef5'>
    <van-area title='标题' columns-num='{{ 2 }}' loading='{{ loading }}' area-list='{{ areaList }}' onChange='onChange' onConfirm='onConfirm' ref='saveChildRef6'>
    </van-area>
  </demo-block>
  <demo-block title='配置列占位提示文字' ref='saveChildRef7'>
    <van-area title='标题' columns-num='{{ 2 }}' loading='{{ loading }}' area-list='{{ areaList }}' columns-placeholder="{{ ['请选择', '请选择', '请选择'] }}" onChange='onChange' onConfirm='onConfirm' ref='saveChildRef8'>
    </van-area>
  </demo-block>
  <van-toast id='van-toast' ref='saveChildRef9'>
  </van-toast>
</view>