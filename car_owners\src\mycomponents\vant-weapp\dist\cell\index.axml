<view class='cell-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{customClass}} {{ utils.bem('cell', [size, { center, required, borderless: !border, clickable: isLink || clickable }]) }}" hover-class='van-cell--hover {{hoverClass}}' hover-stay-time='70' style='{{ customStyle }}' onTap='antmoveAction' data-antmove-tap='onClick'>
    <van-icon a:if='{{ icon }}' name='{{ icon }}' class='van-cell__left-icon-wrap' custom-class='van-cell__left-icon' ref='saveChildRef1'>
    </van-icon>
    <slot a:else  name='icon'>
    </slot>
    <view style="{{ (titleWidth ? 'max-width:' + titleWidth + ';min-width:' + titleWidth + ';' : '') + titleStyle }}" class='van-cell__title {{titleClass}}'>
      <block a:if='{{ title }}'>
        {{ title }}
      </block>
      <slot a:else  name='title'>
      </slot>
      <view a:if='{{ label || useLabelSlot }}' class='van-cell__label {{labelClass}}'>
        <slot a:if='{{ useLabelSlot }}' name='label'>
        </slot>
        <block a:elif='{{ label }}'>
          {{ label }}
        </block>
      </view>
    </view>
    <view class='van-cell__value {{valueClass}}'>
      <block a:if='{{ value || value === 0 }}'>
        {{ value }}
      </block>
      <slot a:else >
      </slot>
    </view>
    <van-icon a:if='{{ isLink }}' name="{{ arrowDirection ? 'arrow' + '-' + arrowDirection : 'arrow' }}" class='van-cell__right-icon-wrap ' custom-class='van-cell__right-icon {{rightIconClass}}' ref='saveChildRef2'>
    </van-icon>
    <slot a:else  name='right-icon'>
    </slot>
    <slot name='extra'>
    </slot>
  </view>
</view>