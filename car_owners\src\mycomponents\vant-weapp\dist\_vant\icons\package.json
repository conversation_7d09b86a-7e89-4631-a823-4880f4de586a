{"_from": "@vant/icons@1.3.2", "_id": "@vant/icons@1.3.2", "_inBundle": false, "_integrity": "sha512-uP0nUWw0pyMMGt3L9BO5fEacz/Cn7QIea0/gFoC/jPLv9ufvUxjPsV9HSyyUJsqMJ3qlYLAI/SxZDbmMZA814A==", "_location": "/@vant/icons", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vant/icons@1.3.2", "name": "@vant/icons", "escapedName": "@vant%2ficons", "scope": "@vant", "rawSpec": "1.3.2", "saveSpec": null, "fetchSpec": "1.3.2"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/@vant/icons/-/icons-1.3.2.tgz", "_shasum": "f0930010e7aaa9b06b03636fe9b5bc6477ea224b", "_spec": "@vant/icons@1.3.2", "_where": "/Users/<USER>/antmoveUserTest/wxDemo/wx/vant/wx", "bundleDependencies": false, "deprecated": false, "description": "vant icons", "devDependencies": {"fast-glob": "^3.2.2", "fs-extra": "^9.0.1", "gulp": "^4.0.2", "gulp-iconfont": "^10.0.3", "gulp-iconfont-css": "^3.0.0", "md5-file": "^5.0.0", "release-it": "^13.6.2", "shelljs": "^0.8.4", "svgo": "1.2.2"}, "files": ["src"], "license": "MIT", "main": "./src/config.js", "name": "@vant/icons", "publishConfig": {"access": "public"}, "release-it": {"git": {"tag": false, "commitMessage": "chore: release @vant/icons ${version}", "addUntrackedFiles": true, "requireCleanWorkingDir": false}}, "repository": {"type": "git", "url": "https://github.com/youzan/vant/tree/dev/packages/vant-icons"}, "scripts": {"build": "npm run export && gulp --gulpfile ./build/build-iconfont.js", "export": "node ./build/export.js", "release": "npm run build && release-it"}, "version": "1.3.2"}