<view class='loading-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class="{{customClass}} van-loading {{ vertical ? 'van-loading--vertical' : '' }}">
    <view class='van-loading__spinner van-loading__spinner--{{ type }}' style='color: {{ color }}; width: {{ utils.addUnit(size) }}; height: {{ utils.addUnit(size) }}'>
      <view a:if="{{ type === 'spinner' }}" a:for='{{ array12 }}' a:key='{{index}}' class='van-loading__dot' ref-numbers='{{ array12 }}'>
      </view>
    </view>
    <view class='van-loading__text' style='font-size: {{ utils.addUnit(textSize) }};'>
      <slot>
      </slot>
    </view>
  </view>
</view>