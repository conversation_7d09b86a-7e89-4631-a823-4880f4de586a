<template>
	<view class="bl-forms-text" :class="{'bl-forms-text--empty':isEmpty}">{{modelValue}}</view>
</template>
<script>
	export default {
		name: 'BlReadonlyText',
		props: {
			value: String,
			placeholder: String,
		},
		computed: {
			modelValue() {
				let val = this.value || '';
				this.isEmpty = val === '';
				return val === ''?this.placeholder:val;
			}
		},
		data() {
			return {
				isEmpty: true
			}
		}
	}
</script>
<style lang="less" scoped>
	.bl-forms-text {
		text-align: right;
		width: 100%;
		min-height: 36px;
		line-height: 36px;
		color: rgba(36, 46, 62, 1);
	}
	.bl-forms-text--empty {
		color: rgba(176, 182, 193, 1);
	}
</style>