<view class='radio-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-radio {{customClass}}'>
    <view a:if="{{ labelPosition === 'left' }}" class="{{labelClass}} {{ utils.bem('radio__label', [labelPosition, { disabled }]) }}" onTap='antmoveAction' data-antmove-tap='onClickLabel'>
      <slot>
      </slot>
    </view>
    <view class='van-radio__icon-wrap' style='font-size: {{ utils.addUnit(iconSize) }};' onTap='antmoveAction' data-antmove-tap='onChange'>
      <slot a:if='{{ useIconSlot }}' name='icon'>
      </slot>
      <van-icon a:else  name='success' class="{{ utils.bem('radio__icon', [shape, { disabled, checked: value === name }]) }}" style="line-height: {{ utils.addUnit(iconSize) }};font-size: {{ utils.addUnit(iconSize) }};{{ checkedColor && !disabled && value === name ? 'border-color:' + checkedColor + '; background-color:' + checkedColor + ';' : '' }}" custom-class='{{iconClass}}' custom-style='font-size: .8em;display: block;' ref='saveChildRef1'>
      </van-icon>
    </view>
    <view a:if="{{ labelPosition === 'right' }}" class="{{labelClass}} {{ utils.bem('radio__label', [labelPosition, { disabled }]) }}" onTap='antmoveAction' data-antmove-tap='onClickLabel'>
      <slot>
      </slot>
    </view>
  </view>
</view>