# Vant Icons

## Install

#### NPM

```shell
npm i @vant/icons -S
```

#### YARN

```shell
yarn add @vant/icons
```

## Document

- [Usage in Vue](https://youzan.github.io/vant/#/zh-CN/icon)
- [Usage in Weapp](https://youzan.github.io/vant-weapp/#/icon)

## Contribution

### Update Icons

1. Update assets/icons.sketch
2. Make a Pull Request

### Add New Icon

1. Add new icon to assets/icons.sketch
2. Add icon name to src/config.js
3. Add icon codepoints to build/codepoints.js
4. Make a Pull Request
