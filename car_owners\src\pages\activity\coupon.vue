<template>
	<view class="coupon" style="position: relative;">
		<view class="home-icon" @click="goHome">
			<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
		</view>
		
		<view class="conpon-container">
			<view class="bg-img">
				<image src="https://image.bolink.club/yima/activity-bg.jpg"></image>
			</view>
			<view class="text-img">
				<image src="https://image.bolink.club/yima/activity-text.png"></image>
			</view>
			
			<view class="coupon-desc">
				<view class="coupon-desc-text">满{{shareMoney}}.01元可用</view>
				<view class="flex-1 juc">
					<view class="coupon-desc-center flex-row alc">
						<view class="coupon-price">{{shareMoney}} <span class="coupon-price-symbol">￥</span></view>
						<view class="coupon-place"></view>
						<view class="coupon-text flex-1 jue">停车满减优惠券</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="coupon-content">
			
			<view class="flex-1 juc">
				<view style="position: relative;margin-top: 20rpx;">
					<image src="https://image.bolink.club/yima/activity-btn-bg.png" style="width: 370rpx;height: 88rpx"></image>
					<view v-if="!limit" class="coupon-btn" style="opacity: 0.6;">
						券已被领取完
					</view>
					<view v-else class="coupon-btn">
						<view v-if="mobile" @click="submitBtn(mobile)">领取停车券</view>
						<button v-else open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
							<view>领取停车券</view>
						</button>
					</view>
				</view>
			</view>
			
			<view class="padl-30 padr-30 padb-30">
				<view class="coupon-rules" style="color: #fff;">
					<view class="coupon-rules-title">分享 & 使用规则</view>
					<view style="opacity: 0.6;">
						<view class="coupon-rules-item">1. 每个用户仅能领取一次;</view>
						<view class="coupon-rules-item">2. 领取的停车券使用有效期不变;</view>
						<view class="coupon-rules-item">3. 停车券分享后，分享者不能使用;</view>
						<view class="coupon-rules-item">4. 仅限小程序支付停车费时使用;</view>
						<view class="coupon-rules-item">5. 本分享最终解释权归一码App。</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	
	export default {
		data() {
			return {
				couponId: '',
				openId: '',
				mobile: '',
				shareMoney: 0,
				limit: false,
			};
		},
		onLoad (options) {
			this.mobile = uni.getStorageSync('mobile');
			this.openId = uni.getStorageSync('openId');
			if ('couponId' in options) {
				this.couponId = options.couponId;
				console.log('this.couponId-->', this.couponId);
			}
			this.initData();
		},
		onReady () {
			getApp().pagePathRecord();
		},
		methods: {
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			initData () {
				let params = {
					couponId: this.couponId,
				}
				apis.homeApis.querysharecoupon(params).then((res) => {
					if (res.status === 0) {
						this.shareMoney = res.data.shareMoney;
						this.limit = res.data.limit;
					} else {
						uni.showToast({
							title: res.msg || '服务异常，请稍候再试',
							icon: "none"
						})
					}
				})
			},
			submitBtn () {
				let params = {
					couponId: this.couponId,
					phone: this.mobile,
					openId: this.openId,
				}
				apis.homeApis.getsharecoupon(params).then((res) => {
					if (res.status === 0) {
						uni.showToast({
							title: '领取成功',
							icon: "none"
						})
					} else {
						uni.showToast({
							title: res.msg || '服务异常，请稍候再试',
							icon: "none"
						})
					}
				})
			},
			// 获取手机号
			getPhoneNumber (e) {
				getApp().getPhoneNumber(e, this).then(res => {
					console.log('getPhoneNumber-->', res);
					if (res) {
						this.submitBtn(res.mobile);
					} else {
						uni.showToast({
							title: '必须授权才能领优惠券',
							icon: 'none',
							duration: 5000
						})
					}
				})
			},
		}
	}
</script>

<style lang="less">
	.coupon {
		width: 100%;
	}
	.conpon-container{
		position: relative;
		width: 100%;
		height: 915rpx;
	}
	.bg-img {
		height: 915rpx;
		font-size: 0;
	}
	.bg-img image {
		width: 100%;
		height: 915rpx;
	}
	.text-img {
		position: absolute;
		top: 168rpx;
		width: 100%;
		text-align: center;
	}
	.text-img image {
		width: 286px;
		height: 84px;
	}
	.coupon-content {
		height: calc(100% - 915rpx);
		background: linear-gradient(to bottom, #FA9150, #F88A55, #F9825D, #F67D61, #F67867);
	}
	.coupon-content button{
		border: none!important;
		background-color: rgba(255,255,255,0)!important;
		color: #EE5018!important;
		font-weight: bold;
		font-size: 40rpx!important;
		width: 480rpx!important;
		height: 130rpx!important;
		padding-bottom: 8rpx;
		letter-spacing: 4rpx;
		position: absolute;
		top: 0;
		left: 0;
	}
	.coupon-desc {
		position: absolute;
		bottom: 60rpx;
		width: 100%;
	}
	.coupon-desc-text {
		color: #999;
		font-size: 26rpx;
		text-align: center;
		letter-spacing: 1rpx;
	}
	.coupon-desc-center {
		width: 517rpx;
		height: 94rpx;
		margin-top: 18rpx;
		background: #fff3e4;
		border-radius: 20rpx;
		box-shadow: 0px 4rpx 4rpx 0px rgba(0,0,0,0.03) inset; 
	}
	.coupon-price {
		font-size: 64rpx;
		font-weight: bold;
		color: #fb754b;
		margin-left: 40rpx;
		margin-right: 70rpx;
		position: relative;
	}
	.coupon-price-symbol {
		font-size: 32rpx;
		position: absolute;
		right: -32rpx;
		top: 10rpx;
	}
	.coupon-place {
		width: 2rpx;
		height: 32rpx;
		background: #ffd8c1;
		border-radius: 4rpx;
	}
	.coupon-text {
		font-size: 34rpx;
		color: #666;
		margin-right: 40rpx;
	}
	.coupon-btn {
		position: absolute;
		top: 0;
		line-height: 80rpx;
		font-size: 32rpx;
		color: #f36b26;
		width: 100%;
		text-align: center;
	}
</style>