<template>
	<view class="chageMap">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<view class="custom-back-nav"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}">
			<view class="flex-row alc">
				<van-icon @click="goBack" name="arrow-left" color="#000000" size="40rpx" />
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<custom-nav-bar title=""></custom-nav-bar>
		<!-- #endif -->
		<view class="map_container">
			<map class="map" id="map" :longitude="longitude" :latitude="latitude" :markers="markers" show-location
				enable-overlooking enable-3D :scale="scale" :polyline="polyline" @markertap="makertap" @tap="mapTap"
				@click="toChargeMap">
			</map>
		</view>
		<view class="chargeInfo">
			<view class="bottom">
				<view class="parkName">
					<view class="title">{{chargeInfo.name}}
					</view>
					<view class="address">{{chargeInfo.address || ''}}</view>
				</view>
				<view class="address-b" @click="openLocation(chargeInfo)">
					<image src="https://image.bolink.club/charge-wait.png" mode=""></image>
					<view class="distance">{{chargeInfo.distance}}km</view>
				</view>
			</view>
		</view>
		<van-dialog id="van-dialog" />
	</view>

</template>

<script>
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog'
	import util from '../../common/utils/util.js'
	import apis from "../../common/apis/index";
	const app = getApp();

	export default {
		data() {
			return {
				userInfo: app.globalData.userInfo,
				chargeInfo: {},
				markers: [],
				latitude: '39.96402234127961', // 纬度
				longitude: '116.30649949969086', // 经度
				windowHeight: 0,
				statusBarHeight: 0,
				navHeight: 0,
				getLocationFail: 0,
				timesList: [],
				price: '0.00',
				mobile: '',
				polyline: [{
					points: [],
					width: 10,
					color: '#09D78A',
					arrowLine: true
				}],
				longitudeLocation: '',
				latitudeLocation: '',
			}
		},
		computed: {
			scale() {
				let scale = ''
				let distance = this.chargeInfo.distance
				if (distance >= 1000) {
					scale = 5
				} else if (distance < 1000 && distance >= 500) {
					scale = 6
				} else if (distance < 500 && distance >= 200) {
					scale = 7
				} else if (distance < 200 && distance >= 100) {
					scale = 8
				} else if (distance < 100 && distance >= 50) {
					scale = 9
				} else if (distance < 50 && distance >= 20) {
					scale = 10
				} else if (distance < 20 && distance >= 10) {
					scale = 10
				} else if (distance < 10 && distance >= 2) {
					scale = 11
				} else if (distance < 2 && distance >= 1) {
					scale = 15
				} else {
					scale = 16
				}
				return scale
			}
		},
		onLoad(options) {
			console.log('options-->', options);
			if ('item' in options) {
				let item = JSON.parse(decodeURIComponent(options.item))
				this.chargeInfo = item
				this.initData()
			}
			getApp().getSystemInfo().then(res => {
				this.windowHeight = res.windowHeight;
				this.statusBarHeight = res.statusBarHeight;
				this.navHeight = res.navHeight;
			});
			getApp().login().then(res => {
				this.getLocation();
				this.mobile = uni.getStorageSync('mobile');
			})
		},
		onShow() {
			// 判断是否有token和session_key
			getApp().isloginSuccess();

		},
		onShareAppMessage(res) {
			let appletConfiguration = util.getAppletConfiguration()
			return {
				title: appletConfiguration && appletConfiguration.sharName || '一码App',
			}
		},
		methods: {
			toGunCode() {
				uni.navigateTo({
					url: '/pagesB/charge/gunCode'
				})
			},
			// 获取用户信息的回调
			getUserProfile(e) {
				app.updataUserInfo().then(() => {
					this.userInfo = app.globalData.userInfo;
					console.log(this.userInfo);
				});
			},
			getPhoneNumber(e, name) {
				app.getPhoneNumber(e, this).then(res => {
					if (res) {
						this.mobile = res.mobile
					}
				})
			},
			openLocation(item) {
				console.log("item->", item);
				uni.openLocation({
					name: this.chargeInfo.name,
					address: this.chargeInfo.address || '',
					latitude: item.latitude,
					longitude: item.longitude,
				})
			},
			queryBillingSet() {
				apis.homeApis.queryBillingSet({
					id: this.chargeInfo.billingSetId
				}).then(res => {
					this.timesList = res.data
					let nowDate = this.getTody()
					let now = new Date().getTime()
					this.timesList.forEach(item => {
						let btime = new Date(nowDate + ' ' + item.btime + ':00')
						let etime = new Date(nowDate + ' ' + item.etime + ':00')
						if (now < etime && now > btime) {
							this.price = (Number(item.sfee) + Number(item.cfee)).toFixed(2)
							item.active = true
						}
					})
				})
			},
			getTody() {
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth() + 1;
				var day = date.getDate();
				if (month < 10) {
					month = "0" + month;
				}
				if (day < 10) {
					day = "0" + day;
				}
				return year + "/" + month + "/" + day;
			},
			toPriceDetail() {
				uni.navigateTo({
					url: '/pagesB/charge/priceDetail?timesList=' + JSON.stringify(this.timesList)
				})
			},
			toEquipList(data) {
				console.log(data)
				uni.navigateTo({
					url: `/pagesB/charge/equipList?stationName=${data.name}&id=${data.id}`
				})
			},
			initData() {
				this.longitude = this.chargeInfo.longitude
				this.latitude = this.chargeInfo.latitude
				let x = parseInt(this.chargeInfo.name.length) * -6;
				let y = -75;
				let markers = [{
					id: 0,
					parkId: this.chargeInfo.parkId,
					latitude: this.chargeInfo.latitude,
					longitude: this.chargeInfo.longitude,
					iconPath: "../static/mapZhong.png",
					width: 25,
					height: 25,
				}]
				if (this.tolongitude) {
					markers.push({
						id: 1,
						latitude: this.tolatitude,
						longitude: this.tolongitude,
						iconPath: "../static/mapQi.png",
						width: 25,
						height: 25,
					})
				} else {
					markers.push({
						id: 1,
						latitude: this.latitudeLocation,
						longitude: this.longitudeLocation,
						iconPath: "../static/mapQi.png",
						width: 25,
						height: 25,
					})
				}
				this.markers = markers
				this.queryBillingSet()
				console.log(this.markers);
			},
			/* 获取充电桩信息 */
			getPileMessage() {
				util.getPileMessage().then().catch(err=>{
					if(err && err.code==4002){
						uni.removeStorageSync('cloudToken')
						this.getPileMessage()
					}else{
						uni.showToast({
						title: err,
						icon: 'none',
						duration: 3000
					});
					}
				})
			},

			getMapService() {
				let data = {
					from: this.latitude + ',' + this.longitude,
					to: this.latitudeLocation + ',' + this.longitudeLocation,
					// token: JSON.parse(uni.getStorageSync('cloudToken')).token,
					token: uni.getStorageSync('token'),
					path: '/direction/driving'
				}
				apis.homeApis.mapService(data).then(res => {
					let points = []
					let polyline = res.result.routes[0].polyline
					for (let i = 2; i < polyline.length; i++) {
						polyline[i] = polyline[i - 2] + polyline[i] / 1000000
					}
					for (let i = 0; i < polyline.length; i += 2) {
						let data = {
							latitude: polyline[i],
							longitude: polyline[i + 1]
						}
						points.push(data)
					}
					this.polyline[0].points = points.reverse()
				})
			},
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (r) => {
						console.log('当前位置的经度：' + r.longitude);
						console.log('当前位置的纬度：' + r.latitude);
						console.log(r);
						this.longitudeLocation = r.longitude;
						this.latitudeLocation = r.latitude;
						this.getMapService()
					},
					fail: (err) => {
						this.getLocationFail += 1;
						if (this.getLocationFail > 5) {
							Dialog.confirm({
								title: '打开设置授权',
								message: "获取定位失败，请检查是否授权。",
							}).then(() => {
								// on confirm
								uni.openSetting({
									success: (res) => {
										// console.log(res.authSetting);
										if (res.authSetting['scope.userLocation']) {
											this.getLocation();
										}
									}
								})
							}).catch(() => {
								// on cancel
							});
						}
					}
				});
			},
			// 点击地图触发
			mapTap(e) {
				console.log(e)
				this.longitude = e.detail.longitude;
				this.latitude = e.detail.latitude;
			},
			// 地图标点点击
			makertap(e) {
				console.log("e-->", e);
				this.openLocation(this.markers[e.detail.markerId])
			},
			goBack() {
				uni.navigateBack({
					delta: 1,
				});
			},
		}
	}
</script>

<style lang="less">
	.chargeInfo {
		margin: 0 5%;
		height: 170rpx;
		width: 90%;
		background: #fff;
		box-shadow: 0px 4px 34px 0px rgba(0, 0, 0, 0.1);
		position: absolute;
		border-radius: 30rpx;
		bottom: 80rpx;
		padding: 24rpx;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			color: #242E3E;
			margin: 8rpx 0 10rpx;
		}

		.bottom {
			display: flex;
			justify-content: space-between;

			.parkName {
				width: 420rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #242E3E;
				margin: 10rpx 0;
			}

			.address-b {
				padding-top: 20rpx;
				display: flex;
				flex-direction: column;
			}

			.address {
				font-size: 26rpx;
				font-weight: 400;
				color: #828EA6;
			}

			.distance {
				margin-top: 10rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #3F7CF6;
			}

			image {
				height: 32rpx;
				width: 32rpx;
				margin-left: 50%;
			}
		}
	}

	.map {
		width: 100%;
		height: 100%;
	}

	.map_container {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
	}

	.chageMap {
		width: 100%;
	}

	.control-btn {
		z-index: 99;
		position: absolute;
		height: 60rpx;
		width: 60rpx;
		line-height: 60rpx;
		right: 36rpx;
	}

	.control-btn-increase {
		bottom: 190rpx;
	}

	.control-btn-decrease {
		bottom: 100rpx;
	}

	.control-btn-location {
		z-index: 99;
		position: absolute;
		height: 80rpx;
		width: 80rpx;
		left: 36rpx;
		bottom: 100rpx;
	}
</style>
