<view class='nav-bar-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view a:if='{{ fixed && placeholder }}' style='height: {{ height }}px;'>
  </view>
  <view class="{{ utils.bem('nav-bar', { fixed }) }} {{customClass}} {{ border ? 'van-hairline--bottom' : '' }}" style='{{ computed.barStyle({ zIndex, statusBarHeight, safeAreaInsetTop }) }}; {{ customStyle }}'>
    <view class='van-nav-bar__content'>
      <view class='van-nav-bar__left' onTap='antmoveAction' data-antmove-tap='onClickLeft'>
        <block a:if='{{ leftArrow || leftText }}'>
          <van-icon a:if='{{ leftArrow }}' size='16px' name='arrow-left' custom-class='van-nav-bar__arrow' ref='saveChildRef1'>
          </van-icon>
          <view a:if='{{ leftText }}' class='van-nav-bar__text' hover-class='van-nav-bar__text--hover' hover-stay-time='70'>
            {{ leftText }}
          </view>
        </block>
        <slot a:else  name='left'>
        </slot>
      </view>
      <view class='van-nav-bar__title {{titleClass}} van-ellipsis'>
        <block a:if='{{ title }}'>
          {{ title }}
        </block>
        <slot a:else  name='title'>
        </slot>
      </view>
      <view class='van-nav-bar__right' onTap='antmoveAction' data-antmove-tap='onClickRight'>
        <view a:if='{{ rightText }}' class='van-nav-bar__text' hover-class='van-nav-bar__text--hover' hover-stay-time='70'>
          {{ rightText }}
        </view>
        <slot a:else  name='right'>
        </slot>
      </view>
    </view>
  </view>
</view>