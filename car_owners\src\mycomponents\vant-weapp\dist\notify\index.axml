<view class='notify-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <van-transition name='slide-down' show='{{ show }}' custom-class='van-notify__container' custom-style='z-index: {{ zIndex }}; top: {{ utils.addUnit(top) }}' onTap='onTap' ref='saveChildRef1'>
    <view class='van-notify van-notify--{{ type }}' style='background:{{ background }};color:{{ color }};'>
      <view a:if='{{ safeAreaInsetTop }}' style='height: {{ statusBarHeight }}px'>
      </view>
      <text>
        {{ message }}      </text>
    </view>
  </van-transition>
</view>