<template>
<uni-shadow-root class="scroll_figure-scroll_figure"><swiper :style="'height: '+(autoHeight)" :indicator-dots="indicatorDots">

    <block v-for="(item,index) in (content)" :key="item.scroll_figure">
        <swiper-item>
            <image @load="autoImage" :src="item.pic || 'https://image.bolink.club/yima/logo.jpg'" class="slide-image" style="border-radius: 12rpx;"></image>
        </swiper-item>
    </block>

</swiper></uni-shadow-root>
</template>

<script>

global['__wxVueOptions'] = {components:{}}

global['__wxRoute'] = 'scroll_figure/scroll_figure'
// components/Scroll_figure.js
Component({

 options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
 },
  /**
   * 组件的属性列表,接收父组件传递过来的属性值，跟vue中的父子组件类似
   */
  properties: {
      content: {  //传入组件的数据
          type: null
      },
      height: {  //传入组件的数据
          type: null,
          value:320
      },
      indicatorDots: {
          type: Boolean,
          value: true
      }
  },

  /**
   * 组件的初始数据
   */
  data: {
	autoHeight: ""
  },
	
	lifetimes: {
		attached: function() {
		  // 在组件实例进入页面节点树时执行
		},
		detached: function() {
		  // 在组件实例被从页面节点树移除时执行
		},
	  },
	
  /**
   * 组件的方法列表
   */
  methods: {
	autoImage(e) {
	  let originalWidth = e.detail.width;
	  let originalHeight = e.detail.height;
	  let autoWidth = 0;
	  let autoHeight = 0;
	  let results = {};
	  wx.getSystemInfo({
	    success: function (res) {
	      // success
	      let winWidth = res.windowWidth;
	      if (originalWidth > winWidth) {
	        autoWidth = winWidth;
	        autoHeight = (autoWidth * originalHeight) / originalWidth;
	        results.imageWidth = autoWidth + 'px';
	        results.imageHeight = autoHeight + 'px';
	      } else {
	        results.imageWidth = originalWidth + 'px';
	        results.imageHeight = originalHeight + 'px';
	      }
		  console.log("results-->", results);
	    }
	  })
	  this.setData({
		  autoHeight: results.imageHeight
	  })
	  return results;
	}
  }
})
export default global['__wxComponents']['scroll_figure/scroll_figure']
</script>
<style platform="mp-weixin">
/* components/Scroll_figure.css */

.slide-image {
    width: 100%;
}
</style>