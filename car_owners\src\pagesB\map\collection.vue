<template>
	<view class="collection-container">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<custom-nav-bar title="我的收藏"></custom-nav-bar>
		<!-- #endif -->
		<view class="collection">
			<view class="list">
				<scroll-view v-if="chargeContentList.length>0" scroll-y="true" class="chargeContent" :scroll-top="scrollTop" lower-threshold="100" refresherrefresh="true" @scrolltolower="scrolltolower" >
				<view style="padding-bottom: 24rpx" v-for="(item,index) in chargeContentList">
					<view class="charge-item" @click="toChargeList(item)">
						<view class="top">
							<view class="parkName-container">
								<view class="parkName">{{item.name}}</view>							
								<view v-if="item.releaseStatus==1" class="station-await">
								可预约
								</view>
							</view>
							<view class="distance">
								<image src="http://image.bolink.club/FlyB64BeU4zug4DKTQ37MPpKu9UU" mode="aspectFit"></image>
								{{item.distance | formetDistance }}
							</view>
						</view>
						<view v-if="item.companyName || item.busineHours || (item.releaseStatus==1)" class="flex-row font-24">
							<view class="grey name" v-if="showCompanyLabel">
								{{item.companyName || '' }}
							</view>
							<!-- <view class="station-time-type">
								{{item | formetBusineHours }}
							</view> -->

						</view>
						<view class="middle">
							<view class="all">
								<view class="slow" v-if="item.slowAllCount">
								<view class="slow-text"></view>	空{{item.slowSpaceCount || 0}} <view class="grey">/{{item.slowAllCount || 0}}</view>
								</view>
								<view class="fast" v-if="item.quickyAllCount">
									<view class="fast-text"></view>	空{{item.quickySpaceCount || 0}} <view class="grey">/{{item.quickyAllCount || 0}}</view>
								</view>
							</view>
							<view class="priceall">
								<view class="text">￥</view>
								<view class="price">{{item.minFee}}</view>
								<view class="text">起</view>
							</view>
						</view>
						<view class="flex-wrap">
							<view class="station-label-item">
								{{item | formetBusineHours }}
							</view>
							<view class="station-label-item" v-for="label in item.label">
								{{ label }}
							</view>
						</view>
					</view>
				</view>
				<view class="list-end">{{isEndCharge ? '亲，到底了' : '加载中...'}}</view>
			</scroll-view>
			<!-- #ifdef MP-ALIPAY -->
			<view v-else class="tips">
				暂无收藏电站，请在首页-充电-收藏电站
			</view>
			<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script>
import apis from "../../common/apis/index"
import BaseConfig from '../../common/config/index.config.js';
import util from '../../common/utils/util.js'
export default {
	components: {},
	data() {
		return {
			mobile: uni.getStorageSync("mobile"),
			chargeContentList: [],
			scrollTop: 0,
			pageCharge: 0,
			isEndCharge: false,
			latitude: BaseConfig.latitude,
			longitude: BaseConfig.longitude,
			showCompanyLabel: true,
		}
	},
	onLoad(option) {
		// #ifdef MP-WEIXIN
		let appletConfiguration = util.getAppletConfiguration();
		if(appletConfiguration){
			appletConfiguration.hideCompanyLabel && (this.showCompanyLabel = !appletConfiguration.hideCompanyLabel);
		}
		// #endif
		if(this.mobile){
			this.getLocation()
		}

	},
	filters: {
			formetDistance(val){
				val = Number(val).toFixed(0)
				let str = val > 10 ? '10'+'km+'
					: val < 1 ? "<1km" 
					: val+'km'
				return str
			},
			formetBusineHours(item){
				let { openingTime, closingTime } = item
				return ((openingTime && closingTime) ? openingTime + '~' + closingTime : "24小时") + '营业' 
			}
		},
	methods: {
		getLocation() {
				// 获取经纬度，通过高德获取城市名
				uni.getLocation({
					type: 'gcj02',
					success: (r) => {
						console.log('当前位置的经度：' + r.longitude);
						console.log('当前位置的纬度：' + r.latitude);
						uni.setStorageSync("longitude", r.longitude);
						uni.setStorageSync("latitude", r.latitude);
						this.latitude = r.latitude;
						this.longitude = r.longitude;
						this.scrolltolower()
					},
					fail: (err) => {
						this.scrolltolower()
					},
					complete: () => {
						
					}
				});
			},
		scrolltolower() {
				this.pageCharge += 1
				if (this.isEndCharge) {
					this.pageCharge = 1
					return false
				}
				this.queryPowerStationList()
			},
			queryPowerStationList(flag) {
				let data = {
					longitude:this.longitude,
					latitude:this.latitude,
					distance: 5000,
					rp: 10,
					page: this.pageCharge || 1,
					queryType:1,
					phone:this.mobile,
					queryAll: 1
				}

				apis.homeApis.queryPowerStationList(data).then(res => {
					let list = res.data.voList || []
					list.forEach(item=>{
						item.label = item.label ? JSON.parse(item.label) : []
					})
					if (!list || list.length <10) {
						this.isEndCharge = true
					}else{
						this.isEndCharge = false
					}
					if (data.page === 1) {
						this.chargeContentList = list;
					} else {
						this.chargeContentList = this.chargeContentList.concat(list);
					}
				})
			},
			toChargeList(item) {
				console.log(item, '点击数据')
				uni.navigateTo({
					url: `../../pagesB/map/chargeMessage?item=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

	},
}
</script>

<style lang="less">
page {
	background-color: #f0f2f5;
}
.collection-container {
	width: 100%;
	background-color: #f0f2f5;
	// #ifdef MP-WEIXIN || APP-PLUS
	margin-top: 180rpx;
	// #endif
}

.collection {
	width: 100%;
}
.list {
	margin: 28rpx auto;
	.tips{
		width:100vw;
		height:100vh;
		text-align: center;
		margin-top: 500rpx;
		color:#b6bfd2;
	}
	.chargeContent {
		width: calc(100vw - 52rpx);
		// #ifdef MP-WEIXIN || APP-PLUS
		height: calc(100vh - 235rpx);
		// #endif			
		margin: 0 26rpx 20rpx 26rpx;
		overflow-y: auto;
		// background-color: #FCF9FC;
		.charge-item {
			border-radius: 30rpx;
			padding: 0 24rpx 4rpx;
			box-shadow: 0 4px 26px 0 rgba(6, 6, 6, 0.01);
			background-color: #fff;
		}

	}
}



</style>
