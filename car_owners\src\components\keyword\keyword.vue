<template>
    <view>
		<view class="panel-wrap"  v-if="isShow">
		  <view :class="isTabBar? 'vehicle-panel tab_bar_height':'vehicle-panel'">
		  <view class='topItem' style="padding: 20rpx 20rpx 0 20rpx">
			<!-- <span class='check'  @click='check'>中/英</span>
			<span class='contentShow'>{{oinp}}</span> -->
			<view class='exit' @click='vehicleTap("exit")'>确认</view>
		  </view>
		  <!--省份简写键盘-->
		  <view v-if="keyBoardType == 1" class="text-board">
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' v-for="(item,idx) in keyVehicle1" @click='vehicleTap(item)' :key="idx">{{item}}</view>
			</view>
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' v-for="(item,idx) in keyVehicle2" @click='vehicleTap(item)'  :key="idx">{{item}}</view>
			</view>
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' v-for="(item,idx) in keyVehicle3" @click='vehicleTap(item)'  :key="idx">
				{{item}}
			  </view>
			   
			</view>
		   
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' @click='vehicleTap(item)' v-for="(item,idx) in keyVehicle4"
				:key="idx">{{item}}</view>
				
				<view class='vehicle-panel-row-button vehicle-panel-row-button-img' @click='vehicleTap("delete")'>
				  <img src='https://image.bolink.club/yima/delete.png' class='vehicle-en-button-delete'>
				</view>
			</view>
		  </view>
		  <!--英文键盘  -->
		  <view v-else class="letter-board">
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' @click='vehicleTap(item)'  v-for="(item,idx) in keyNumber" 
				:key="item">{{item}}</view>
			</view>
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' v-for="(item,idx) in keyEnInput1" @click='vehicleTap(item)' :key="idx">{{item}}</view>
			</view>
			<view class="vehicle-panel-row">
			  <view class='vehicle-panel-row-button' v-for="(item,idx) in keyEnInput2" @click='vehicleTap(item)' :key="idx" :class="spCode.indexOf(item)!==-1 ? 'sp-code' : ''">{{item}}</view>
			 
			</view>
			<view class="vehicle-panel-row">
				
			  <view class='vehicle-panel-row-button' @click='vehicleTap(item)' v-for="(item,idx) in keyEnInput3" :key="idx" :class="spCode.indexOf(item)!==-1 ? 'sp-code' : ''">{{item}}</view>
				
				<view  class='vehicle-panel-row-button vehicle-panel-row-button-img' @click='vehicleTap("delete")'>
					<img src='https://image.bolink.club/yima/delete.png' class='vehicle-en-button-delete'>
				</view>
			 </view>
		  </view>
		</view>
		</view>
    </view>
</template>

/**
 * @params isShow 控制键盘显示隐藏
 * @prarms oinp   在键盘上显示输入的内容
 * @methods delete 删除按钮触发  this.str = this.str.slice(0, this.str.length-1)
 * @methods ok     确认 按钮触发
 * @methods exit   取消按钮触发
 * @methods inputchange 获取输入的内容
 */

<script>
	export default {
		props:[ "isShow", "oinp", "keyBoardType",'isTabBar'],
		data() {
			return {
				keyVehicle1: ["京", "沪", "浙", "粤", "苏", "鲁", "晋", "吉", "冀", "豫"],
			    keyVehicle2: ["川", "渝", "辽", "黑", "皖", "鄂", "湘", "赣", "闽"],
			    keyVehicle3: ["陕", "甘", "宁", "蒙", "津", "桂", "云", "贵"],
			    keyVehicle4: ["琼", "青", "新", "藏", "使","临"],
			    keyNumber: ["1","2","3","4","5","6","7","8","9","0"],
			    keyEnInput1: ["Q","W","E","R","T","Y","U","I","O","P"],
			    keyEnInput2: ["A","S","D","F","G","H","J", "K","L","港","澳"],
			    keyEnInput3: ["Z","X","C","V","B","N","M","学","领"],
				spCode: '港澳学领',
			};
		},
		updated () {
			console.log("oinp--->", this.oinp);
		},
		methods: {
			vehicleTap: function(event) {
			  console.log(event);
			  switch (event) {
				case "delete":
				  this.$emit("delete");
				  this.$emit("inputchange",event);
				  break;
				case "ok":
				  this.$emit("ok");
				  break;
				case "exit":
				  this.$emit("exit");
				  break;
				default:
				  this.$emit("inputchange", event);
			  }
			},
			colse_da() {
			  this.$emit("exit2");
			},
			check() {
			  if (this.keyBoardType == 1) {
				this.keyBoardType = 2;
			  } else if (this.keyBoardType == 2) {
				this.keyBoardType = 1;
			  }
			}
		}
	}
</script>

<style>
	
:host {
  width: 100%;
}
.panel-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  color: #333;
}
.vehicle-panel {
  width: 100%;
  height: 440rpx;
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 1000;
  background: #EEEEEE;
  padding-bottom: 20rpx;
  border-top: 2rpx solid #eee;
}
.tab_bar_height{
	bottom:140rpx;
}
.jik {
  width: 0.6rem;
  height: 0.8rem;
}
.text-board {
	padding: 16rpx;
	font-size: 34rpx;
	font-weight: 600;
}
.letter-board {
	padding: 16rpx;
	font-size: 44rpx;
}

.sp-code {
	font-size: 34rpx;
	font-weight: 700;
}

.vehicle-panel-row {
  display: flex;
  justify-content: center;
  align-items: center;
}
.vehicle-panel-row-button {
  background-color: #fff;
  margin: 4rpx;
  width: 64rpx;
  height: 74rpx;
  line-height: 74rpx;
  text-align: center;
  border-radius: 6rpx;
  box-shadow: 4rpx 6rpx 0rpx 0rpx rgba(137,138,141,0.30); 
}
.vehicle-panel-row-button:active {
	transform: scale(0.95);
	background-color: rgba(0,0,0,0.1);
}
.vehicle-panel-row-button-img {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 84rpx;
  background: #cccccc;
  border-radius: 6rpx;
  box-shadow: 4rpx 6rpx 0rpx 0rpx rgba(137,138,141,0.56); 
}
.vehicle-en-button-delete {
  width: 46rpx;
  height: 30rpx;
}
.vehicle-panel-ok {
  background-color: #2385FF;
  color: #fff;
  width: 5rem;
  height: 2.8rem;
  line-height: 2.8rem;
}
.topItem {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.exit {
	margin-right: 0.3rem;
	color: #2385FF;
	font-size: 16px;
	display: block;
	line-height: 1rem;
}
.check {
  margin-left: 0.3rem;
  color: #2385FF;
  font-size: 1rem;
  display: block;
  line-height: 1rem;
}
</style>
