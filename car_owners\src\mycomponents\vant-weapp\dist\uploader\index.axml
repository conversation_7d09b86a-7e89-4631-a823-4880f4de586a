<view class='uploader-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-uploader'>
    <view class='van-uploader__wrapper'>
      <view a:if='{{ previewImage }}' a:for='{{ lists }}' a:key='{{index}}' class='van-uploader__preview' data-index='{{ index }}' ref-numbers='{{ lists }}' onTap='antmoveAction' data-antmove-tap='onClickPreview'>
        <image a:if='{{ item.isImage }}' mode='{{ imageFit }}' src='{{ item.thumb || item.url }}' alt="{{ item.name || ('图片' + index) }}" class='van-uploader__preview-image' style='width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};' data-index='{{ index }}' onTap='antmoveAction' data-antmove-tap='onPreviewImage'>
        </image>
        <video a:elif='{{ item.isVideo }}' src='{{ item.url }}' poster='{{ item.thumb }}' autoplay='{{ item.autoplay }}' class='van-uploader__preview-image' style='width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};' data-index='{{ index }}' onTap='antmoveAction' data-antmove-tap='onPreviewVideo'>
        </video>
        <view a:else  class='van-uploader__file' style='width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};'>
          <van-icon name='description' class='van-uploader__file-icon' ref='saveChildRef1'>
          </van-icon>
          <view class='van-uploader__file-name van-ellipsis'>
            {{ item.name || item.url }}
          </view>
        </view>
        <view a:if="{{ item.status === 'uploading' || item.status === 'failed' }}" class='van-uploader__mask'>
          <van-icon a:if="{{ item.status === 'failed' }}" name='close' class='van-uploader__mask-icon' ref='saveChildRef2'>
          </van-icon>
          <van-loading a:else  custom-class='van-uploader__loading' ref='saveChildRef3'>
          </van-loading>
          <text a:if='{{ item.message }}' class='van-uploader__mask-message'>
            {{ item.message }}          </text>
        </view>        <view a:if='{{ deletable && item.deletable }}' data-index='{{ index }}' class='van-uploader__preview-delete' catchTap='antmoveAction' data-antmove-tap='deleteItem'>
          <van-icon name='cross' class='van-uploader__preview-delete-icon' ref='saveChildRef4'>
          </van-icon>
        </view>
      </view>      <block a:if='{{ isInCount }}'>
        <view class='van-uploader__slot' onTap='antmoveAction' data-antmove-tap='startUpload'>
          <slot>
          </slot>
        </view>
        <view a:if='{{ showUpload }}' class="van-uploader__upload {{ disabled ? 'van-uploader__upload--disabled': ''}}" style='width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};' onTap='antmoveAction' data-antmove-tap='startUpload'>
          <van-icon name='{{ uploadIcon }}' class='van-uploader__upload-icon' ref='saveChildRef5'>
          </van-icon>
          <text a:if='{{ uploadText }}' class='van-uploader__upload-text'>
            {{ uploadText }}          </text>
        </view>
      </block>
    </view>
  </view>
</view>