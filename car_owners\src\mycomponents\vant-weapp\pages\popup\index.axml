<view class='page-container-classname' ref='saveChildRef0'>
  <demo-block title='基础用法' ref='saveChildRef1'>
    <van-cell title='展示弹出层' is-link=" " onClick='showBasic' ref='saveChildRef2'>
    </van-cell>
    <van-popup show='{{ show.basic }}' custom-style='padding: 30px 50px' onClose='hideBasic' ref='saveChildRef3'>
      内容
    </van-popup>
  </demo-block>
  <demo-block title='弹出位置' ref='saveChildRef4'>
    <van-cell title='顶部弹出' is-link=" " onClick='showTop' ref='saveChildRef5'>
    </van-cell>
    <van-cell title='底部弹出' is-link=" " onClick='showBottom' ref='saveChildRef6'>
    </van-cell>
    <van-cell title='左侧弹出' is-link=" " onClick='showLeft' ref='saveChildRef7'>
    </van-cell>
    <van-cell title='右侧弹出' is-link=" " onClick='showRight' ref='saveChildRef8'>
    </van-cell>
    <van-popup show='{{ show.top }}' position='top' custom-style='height: 20%' onClose='hideTop' ref='saveChildRef9'>
    </van-popup>
    <van-popup show='{{ show.bottom }}' position='bottom' custom-style='height: 20%' onClose='hideBottom' ref='saveChildRef10'>
    </van-popup>
    <van-popup show='{{ show.left }}' position='left' custom-style='width: 20%; height: 100%' onClose='hideLeft' ref='saveChildRef11'>
    </van-popup>
    <van-popup show='{{ show.right }}' position='right' custom-style='width: 20%; height: 100%' onClose='hideRight' ref='saveChildRef12'>
    </van-popup>
  </demo-block>
  <demo-block title='关闭图标' ref='saveChildRef13'>
    <van-cell title='关闭图标' is-link=" " onClick='showCloseIcon' ref='saveChildRef14'>
    </van-cell>
    <van-cell title='自定义图标' is-link=" " onClick='showCustomCloseIcon' ref='saveChildRef15'>
    </van-cell>
    <van-cell title='图标位置' is-link=" " onClick='showCustomIconPosition' ref='saveChildRef16'>
    </van-cell>
    <van-popup show='{{ show.closeIcon }}' closeable=" " position='bottom' custom-style='height: 20%' onClose='hideCloseIcon' ref='saveChildRef17'>
    </van-popup>
    <van-popup show='{{ show.customCloseIcon }}' closeable=" " close-icon='close' position='bottom' custom-style='height: 20%' onClose='hideCustomCloseIcon' ref='saveChildRef18'>
    </van-popup>
    <van-popup show='{{ show.customIconPosition }}' closeable=" " close-icon-position='top-left' position='bottom' custom-style='height: 20%' onClose='hideCustomIconPosition' ref='saveChildRef19'>
    </van-popup>
  </demo-block>
  <demo-block title='圆角弹窗' ref='saveChildRef20'>
    <van-cell title='圆角弹窗' is-link=" " onClick='showRound' ref='saveChildRef21'>
    </van-cell>
    <van-popup show='{{ show.round }}' round=" " position='bottom' custom-style='height: 20%' onClose='hideRound' ref='saveChildRef22'>
    </van-popup>
  </demo-block>
</view>