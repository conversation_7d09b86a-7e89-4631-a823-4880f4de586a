<template>
	<view>
		
		<view v-if="isHome" style="background-color: #FBFBFB;margin-top: 20rpx;">
			<template v-for="(item, index) in infoData">
				<view
					:key="index" 
					@click="jumpInfoDetail(item)"
					class='home-info' 
				>
					<view style='position: relative' >
						<view class="home-info-image">
							<image :src="item.picUrl + '?imageView2/0/w/750'" mode="aspectFill" :lazy-load="true"></image>
						</view>
						
						<view class="home-info-card">
							<view style="padding: 22rpx 40rpx;height: 100%;" class="flex-1 flex-col juc">
								<view class="flex-row">
									<view class="home-info-title flex-1">{{item.title}}</view>
									<view class="flex-row alc jue heart-img">
										<image @click.stop="praiseClick(item, index)" v-if="item.praise==1" src="https://image.bolink.club/yima/info-heartBeat.png"></image>
										<image @click.stop="praiseClick(item, index)" v-else src="https://image.bolink.club/yima/info-heart.png"></image>
										<view class="marl-10">{{item.praiseNum || 0}}</view>
									</view>
								</view>
								
								<view class='flex-row alc falls-desc-name'>
									<view style="max-width: 155rpx;">
										<view class="of-ellipsis">{{item.source || item.typeName || ''}}</view>
									</view>
									<view class='flex-row flex-1 jue'>{{form.timestampToTime(item.createTime, "date")}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
		
		<!--  瀑布流布局  -->
		<view v-else class='falls-container'>
		
			<template v-for="(item, index) in infoData">
		
				<view 
					v-if="index % 2 == 0" 
					:key="index" 
					@click="jumpInfoDetail(item)"
					style='position: relative' 
					class='falls-card' 
					:class="isInfoList ? 'round-card' : ''"
				>
		
					<!-- <view class="falls-source" :style="'background-color:'+ colorArr[index%4] ">{{item.source || item.typeName || ''}}</view> -->
					
					<view class="falls-image" :class="isInfoList ? 'round-image' : ''">
						<image :src="item.picUrl + '?imageView2/0/w/200'" mode='widthFix' :lazy-load="true"></image>
					</view>
		
					<view class='falls-desc'>
						<view class='falls-desc-title'>{{item.title}}</view>
						<view class='flex-row alc falls-desc-name'>
							<view class="flex-1">
								<view class="of-ellipsis">{{item.source || item.typeName || ''}}</view>
							</view>
							
							<!-- <view class='flex-row flex-1 jue'>{{form.timestampToTime(item.createTime, "date")}}</view> -->
							
							<view class="flex-row alc jue heart-img">
								<image @click.stop="praiseClick(item, index)" v-if="item.praise==1" src="https://image.bolink.club/yima/info-heartBeat.png"></image>
								<image @click.stop="praiseClick(item, index)" v-else src="https://image.bolink.club/yima/info-heart.png"></image>
								<view class="marl-10">{{item.praiseNum || 0}}</view>
							</view>
						</view>
					</view> 
				</view>
				
				<!-- <template v-if="index!=0 && index % 60 == 0">
					<view class="falls-ad marb-30" style="overflow: hidden;">
						<ad-custom unit-id="adunit-64524fe7855df585"></ad-custom>
					</view>
				</template> -->
		
			</template>
		
			<template v-for="(item, index) in infoData">
		
				<view 
					v-if="index % 2 != 0" 
					:key="index" 
					@click="jumpInfoDetail(item)"
					style='position: relative' 
					class='falls-card'
					:class="isInfoList ? 'round-card' : ''"
				>
					
					<!-- <view class="falls-source" :style="'background-color:'+ colorArr[index%4] ">{{item.source || item.typeName || ''}}</view> -->
				
					<view class="falls-image" :class="isInfoList ? 'round-image' : ''">
						<image :src="item.picUrl + '?imageView2/0/w/200'"  mode='widthFix' :lazy-load="true"></image>
					</view>
				
					<view class='falls-desc'>
						<view class='falls-desc-title'>{{item.title}}</view>
						<view class='flex-row alc falls-desc-name'>
							<view class="flex-1">
								<view class="of-ellipsis">{{item.source || item.typeName || ''}}</view>
							</view>
							<!-- <view class='flex-row flex-1 jue'>{{form.timestampToTime(item.createTime, "date")}}</view> -->
							
							<view class="flex-row alc jue heart-img">
								<image @click.stop="praiseClick(item, index)" v-if="item.praise==1" src="https://image.bolink.club/yima/info-heartBeat.png"></image>
								<image @click.stop="praiseClick(item, index)" v-else src="https://image.bolink.club/yima/info-heart.png"></image>
								<view class="marl-10">{{item.praiseNum || 0}}</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- <template v-if="index!=0 && index % 60 == 0">
					<view class="falls-ad marb-30" style="overflow: hidden;">
						<ad-custom unit-id="adunit-64524fe7855df585"></ad-custom>
					</view>
				</template> -->
		
			</template>
			
		</view>
	</view>
</template>

<script>
	import form from '../../common/utils/form.js';
	export default {
		props: {
			isHome: {
				type: Boolean,
				default: false
			},
			infoData: {
				type: Array
			},
			isInfoList: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				form: form,
				colorArr: ['#F98B5A', '#FC6B65', '#916DF8', '#308BFA']
			};
		},
		created () {
		},
		methods: {
			praiseClick (item, index) {
				this.$emit('praiseClick', item, index)
			},
			jumpInfoDetail (item) {
				let startTime = new Date().getTime();
				// uni.setStorageSync("infoDetail", item)
				uni.navigateTo({
					url: `/pagesA/index/infoDetail?id=${item.id}`,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: '点击单个资讯',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '资讯详情页面',
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: startTime,
							endTime: new Date().getTime()
						});
					}
				})
			}
		}
	}
</script>

<style lang="less">
	.info-item {
		padding: 20rpx 0;
		border-bottom: 2rpx solid #eee;
	}
	.info-img {
		width: 200rpx;
		height: 140rpx;
	}
	.info-img image {
		width: 100%;
		height: 100%;
	}
	.info-title {
		font-size: 28rpx;
		color: #000;
		margin-top: 10rpx;
	}
	
	/*  瀑布流布局 */
	.falls-container{
	    column-count: 2; 
	    column-gap: 20rpx;
	    padding-left: 30rpx;
	    padding-right: 30rpx;
	    padding-top: 20rpx;
	}
	.falls-card {
		box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(131, 131, 131, 0.15);
	    background-color: #fff;
	    border-radius: 10rpx;
	    /* 防止元素卡在列中*/
	    -webkit-column-break-inside: avoid;  
	    page-break-inside: avoid; 
	    break-inside: avoid;
	}
	.falls-source {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		background-color: #F98B5A;
		width: 140rpx;
		height: 44rpx;
		line-height: 44rpx;
		text-align: center;
		border-radius: 10rpx 0 10rpx 0;
		font-size: 24rpx;
		font-weight: 300;
		color: #FFFFFF;
	}
	.falls-ad {
		width: 324rpx!important;
		height: auto;
	}
	.falls-image {
		height: auto;
	}
	.falls-image image{
	    width: 100%;
		height: auto;
	    border-radius: 10rpx 10rpx 0 0;  
	}
	.round-card {
		border-radius: 60rpx 10rpx 10rpx 10rpx;
	}
	.round-image image {
		border-radius: 60rpx 10rpx 0 0;
	}
	.falls-desc {
	    margin-bottom: 30rpx;
	    padding: 10rpx 20rpx 20rpx 20rpx;
	    min-height: 50rpx;
	}
	.falls-desc-title {
		overflow: hidden;
	    display: -webkit-box;
	    -webkit-box-orient: vertical;
	    -webkit-line-clamp: 2;
		font-size: 26rpx;
		color: #333333;
	    text-align: left;
	}
	.falls-desc-name{
	    margin-top: 30rpx;
	    font-size: 24rpx;
	    color: #999999;
	}
	
	
	.home-info {
		margin-left: 34rpx;
		margin-right: 34rpx;
		margin-bottom: 160rpx;
	}
	.home-info-image image{
		width: 100%;
		height: 360rpx;
		border-radius: 40rpx 40rpx 0px 0px;
	}
	.home-info-card {
		font-size: 28rpx;
		position: absolute;
		bottom: -120rpx;
		width: 100%;
		height: 170rpx;
		background: #ffffff;
		border-radius: 40rpx;
		box-shadow: 0px 4rpx 50rpx 0px rgba(0,0,0,0.08); 
	}
	.home-info-title {
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		font-size: 32rpx;
		color: #333333;
		font-weight: 600;
		text-align: left;
	}
</style>
