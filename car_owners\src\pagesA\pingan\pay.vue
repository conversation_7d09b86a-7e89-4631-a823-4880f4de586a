<template>
	<view class="pingan">
		
		<view style="position: relative;">
			<image style="width: 100%;height: 694rpx" src="../static/pingan_new/top-bg.jpg"></image>
			<view class="home-icon" @click="goHome">
				<image style="width: 32rpx;height: 28rpx" src="https://image.bolink.club/yima/pingan-home.png"></image>
			</view>
			
			<view class="flex-row alc juc mart-30 padb-30 my-logo">
				<image src="../static/pingan_new/pingan.png" style="width: 142rpx;height: 55rpx;"></image>
				<view class="content-box-bor"></view>
				<image src="../static/pingan_new/chezhu.png" style="width: 142rpx;height: 55rpx;"></image>
			</view>
		</view>
		
		<view class="main-cotain">
			<view class="bank-info">
				<view class="flex-row">
					<image class="bank-info-logo" src="../static/pingan_new/logo.png"></image>
					<view class="flex-row flex-1 bank-info-nav">
						<view class="bank-info-title">平安银行</view>
						<view class="flex-1 jue">
							<view class="bank-info-btn">开通成功</view>
						</view>
					</view>
				</view>
				<view class="bank-info-content">
					<view class="bank-info-num">{{accountNoPage}}</view>
					<view class="mart-10 bank-info-add">开户行：{{'平安银行佛山分行'}}</view>
				</view>
			</view>
			
			<view class="input-box">
				<view style="position: relative;">
					<image src="../static/pingan_new/bg-line.png" style="width: 100%;height: 72rpx;"></image>
					<view class="top-bg-desc">支付3元，领取10元停车券</view>
				</view>
				<view class="input-box-content">
					<view class="flex-row marb-30" style="color: #666;font-size: 30rpx;">
						手机号：{{phone}}
					</view>
					<view class="flex-row alc">
						<view class="pingan-input-par" style="padding-left: 70rpx;">
							<input 
								class="pingan-input" 
								type="number" 
								v-model.trim="code" 
								maxlength="6"  
								style="width: 200rpx;"  
								placeholder="验证码" 
								placeholder-style="color: #ABABAB;"
							/>
						</view>
						<view class="flex-1 jue" style="margin-bottom: 25rpx;">
							<van-button
								customClass="my-submit-btn"
								@click="codeBtnClick" 
								size="small"
								:disabled="loadingCode"
								round 
								custom-style="width: 260rpx;height: 84rpx"
								color="#F65C27"
							>{{btnText}}</van-button>
						</view>
					</view>
					
					
					<view class="rule-checkbox">
						<view class="flex-row">
							<view>
								<van-checkbox custom-class="my-checkbox" style="block: inline" :value="true" :checked-color="checked1 ? '#FE6121': '#D7D7D7'" icon-size="30rpx" shape="square" @click.native="(e)=>{checked1=!checked1}">
								</van-checkbox>
							</view>
							<view>
								<span @click="()=>{checked1=!checked1}">我已阅读并同意</span>
								<span class="rule-item" @click.stop="ruleView(1)">《平安银行快捷支付业务服务协议》</span>
							</view>
						</view>
					</view>
					<view class="pingan-btn">
						<van-button 
							customClass="my-submit-btn"
							@click="btnClick" 
							:loading="loading"
							loading-text="加载中..."
							:disabled="disabled"
							block 
							round 
							color="#F65C27"
						>立即支付（￥3）</van-button>
					</view>
				</view>
			</view>
		</view>
		
		
		<van-dialog id="van-dialog" />
	</view>
</template>

<script>
	import apis from "../../common/apis/index";
	import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
	
	export default {
		data() {
			return {
				thirdId: '', // 查询资格时返回的id
				otpOrderNo: '', // 短信单号
				businessNo: '', // 业务号
				accountNo: '', // 卡号
				accountNoPage: '', // 页面显示的卡号
				codeSuccess: false, // 是否成功获取验证码
				phone: '',
				code: '',
				timer: null,
				payTimer: null,
				loadingCode: false,
				loading: false,
				loadingText: 60,
				btnText: '获取验证码',
				btnTimber: '获取验证码',
				checked1: false,
				subscribeFlag: false
			}
		},
		onLoad (options) {
			apis.homeApis.getPinganUserInfo().then((res)=> {
				if (res.status === 200 && res.data) {
					this.phone = res.data.mobileNo;
					this.thirdId = res.data.thirdId;
					this.accountNo = res.data.accountNo;
					this.accountNoPage = res.data.accountNo ? res.data.accountNo.replace(/(.{4})/g, "$1 ") : '----';
				} else {
					uni.showToast({
						title: res.msg || '系统繁忙，请稍候再试',
						icon: 'none',
						duration: 5000
					});
				}
			}).catch((err) => {
				uni.showToast({
					title: '系统繁忙，请稍候再试',
					icon: 'none'
				});
			})
			
		},
		onUnload () {
			clearInterval(this.timer);
		},
		computed: {
			disabled () {
				let flag = true;
				if (this.codeSuccess && this.checked1) {
					flag = false;
				}
				return flag;
			}
		},
		methods: {
			goHome () {
				uni.reLaunch({ 
					url: '/pages/park/index',
				})
			},
			ruleView (type) {
				uni.showLoading({
					title: '加载中...'
				})
				console.log('ruleView')
				let url = 'https://mweb.bolink.club/pingan/'
				if (type === 1) {
					url += 'pingan_fu.pdf';
				}
				wx.downloadFile({
				  url: url,
				  success: (res) => {
					  uni.hideLoading();
				    const filePath = res.tempFilePath
				    wx.openDocument({
				      filePath: filePath,
				      success: (res) => {
						this.checked1 = true;
				      }
				    })
				  },
				  fail: () => {
					  uni.showToast({
					  	title: '打开失败，请稍候再试',
					  	icon: 'none'
					  });
				  }
				})
			},
			// 获取验证码
			codeBtnClick () {
				if (!this.phone) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				}
				this.loadingCode = true;
				let params = {
					thirdId: this.thirdId,
					mobileNo: this.phone,
					scene: 'TI001',
					transAmt: 3
				};
				apis.homeApis.getcheckcode(params).then((res)=> {
					if (res.status === 200) {
						this.otpOrderNo = res.data.otpOrderNo;
						this.businessNo = res.data.businessNo;
						this.thirdId = res.data.thirdId;
						this.codeSuccess = true;
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						});
						this.timer = setInterval(() => {
							this.loadingText = this.loadingText - 1;
							this.btnText = '倒计时' + this.loadingText;
							if (this.loadingText === 0) {
								this.btnText = this.btnTimber;
								this.loadingCode = false;
								this.loadingText = 60;
								clearInterval(this.timer)
							}
						}, 1000)
					} else {
						this.loadingCode = false;
						// uni.showToast({
						// 	title: res.msg || '系统繁忙，请稍候再试',
						// 	icon: 'none',
						// 	duration: 5000
						// });
					}
				}).catch(() => {
					this.loadingCode = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none'
					});
				})
			},
			// 点击支付按钮
			btnClick () {
				if (!this.phone) {
					uni.showToast({
						title: '请先输入手机号',
						icon: "none"
					})
					return;
				} else if (!this.codeSuccess) {
					uni.showToast({
						title: '请先获取验证码',
						icon: "none"
					})
					return;
				} else if (!this.code) {
					uni.showToast({
						title: '请先输入验证码',
						icon: "none"
					})
					return;
				} else if (!this.checked1) {
					uni.showToast({
						title: '请先勾选同意',
						icon: "none"
					})
					return;
				}
				let tmplId = getApp().globalData.tmplIds[3];
				// 就首次支付弹出订阅
				if (!this.subscribeFlag && getApp().globalData.myTmplIds.indexOf(tmplId) == -1) {
					uni.requestSubscribeMessage({
						tmplIds: [tmplId],
						success: (res) => {
							let status = 2;
							if (res[tmplId] == "accept") { // 字段就是tmplIds模板id
								status = 1;
							}
							let params = [{
								priTmplId: tmplId,
								openid: uni.getStorageSync('openId'),
								status: status
							}];
							apis.homeApis.subscribeSta(params).then((res) => {
							})
						},
						fail: (err) => {
							console.log('requestSubscribeMessage-err-->',err)
						},
						complete: () => {
							this.subscribeFlag = true;
							this.commonTransferIn();
						},
					})
				} else {
					this.commonTransferIn();
				}
			},
			commonTransferIn () {
				this.loading = true;
				let params = {
					thirdId: this.thirdId,
					otpValue: this.code,
					businessNo: this.businessNo,
					otpOrderNo: this.otpOrderNo,
					transAmt: 3
				}
				apis.homeApis.commonTransferIn(params).then((res) => {
					if (res.status === 200) {
						if (res.data.status == 3) {
							this.loading = false;
							uni.showToast({
								title: '扣款失败，请稍候再试',
								icon: 'none',
								duration: 5000
							});
						} else {
							uni.reLaunch({
								url: `/pagesA/pingan/paySuccess?thirdId=${params.thirdId}&businessNo=${params.businessNo}`,
								complete: (res) => {
									this.loading = false;
								}
							})
						}
					} else {
						this.loading = false;
						let msg = res.msg && JSON.parse(res.msg)
						if (msg.responseCode == '61000002') {
							uni.showToast({
								title: '验证码错误',
								icon: 'none',
								duration: 5000
							});
						} else {
							uni.showToast({
								title: msg.responseMsg || '系统繁忙，请稍候再试',
								icon: 'none',
								duration: 5000
							});
						}
					}
				}).catch(()=>{
					this.loading = false;
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none',
						duration: 5000
					});
				});
			}
			
		}
	}
</script>

<style scoped>
	.pingan {
		position: relative;
		width: 100%;
		background-color: #fff;
	}
	.main-cotain {
		width: 100%;
		position: absolute;
		top: 300rpx;
	}
	.my-logo {
		position: absolute;
		top: 200rpx;
		left: calc(50% - 164rpx)
	}
	
	.bank-info {
		height: 246rpx;
		border-radius: 16rpx;
		background-color: #fff;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
		padding: 30rpx 40rpx;
		margin: 30rpx;
		font-size: 26rpx;
		color: #A0A0A0;
	}
	.bank-info-nav {
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #eee;
	}
	.bank-info-logo {
		width: 86rpx;
		height: 50rpx;
		margin-right: 10rpx;
	}
	.bank-info-title {
		margin-left: 10rpx;
		font-size: 36rpx;
		color: #666;
	}
	.bank-info-btn {
		margin-left: 16rpx;
		font-size: 24rpx;
		text-align: center;
		color: #fff;
		width: 167rpx;
		height: 44rpx;
		line-height: 44rpx;
		background: linear-gradient(135deg,#FED47A 0%, #FFBA49);
		border-radius: 20rpx 0px 20rpx 0rpx;
	}
	.bank-info-content {
		padding-top: 20rpx;
		font-size: 28rpx;
		color: #999;
	}
	.bank-info-num {
		color: #333;
		font-size: 44rpx;
		font-weight: 500;
		letter-spacing: 2rpx;
	}
	.bank-info-add {
		color: #999;
		font-size: 24rpx;
	}
	
	.input-box {
		margin: 40rpx 30rpx 40rpx 30rpx;
		border-radius: 20rpx;
		background-color: #fff;
		box-shadow:0px 0px 8rpx 0px rgba(0,0,0,0.12);
	}
	.top-bg-desc {
		font-size: 30rpx;
		font-weight: 800;
		color: #D14B22;
		line-height: 72rpx;
		width: 100%;
		position: absolute;
		top: 0rpx;
		text-align: center;
	}
	.input-box-content {
		padding: 40rpx 60rpx 60rpx 60rpx;
	}
	.pingan-btn-title {
		padding: 10rpx 10rpx 24rpx 10rpx;
		color: #333;
		font-size: 40rpx;
		margin-bottom: 10rpx;
	}
	
	.top-redbag {
		/* position: absolute;
		bottom: 150rpx;
		left: 100rpx; */
		/* background-color: #fae6cd; */
		border-radius: 12rpx;
	}
	.top-redbag-content {
		padding: 20rpx;
	}
	.top-redbag-item {
		background-color: #F5D39E;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 24rpx;
		color: #A07137;
	}
	.top-redbag-price {
		font-size: 30rpx;
	}
	.top-redbag-num {
		font-size: 52rpx;
	}
</style>
<style>
	.my-checkbox {
		display: inline-block!important;
		vertical-align: middle;
		width: 50rpx;
	}
</style>
