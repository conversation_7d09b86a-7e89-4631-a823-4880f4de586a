{"name": "u-draw-poster uniVue2，3适用 海报绘制工具", "version": "2.1.0-bate.1", "description": "全端支持，内置多种海报绘制方法、表单绘制、二维码生成，图片裁剪。原生开发体验，上手快，不污染组件数据", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "esno scripts/build.ts", "publish": "esno scripts/publish.ts", "compress": "esno scripts/compress.ts", "clean": "rimraf dcloud dist template/js_sdk"}, "files": ["dist", "lib"], "author": "Mr.<PERSON>", "license": "ISC", "dependencies": {"@dcloudio/types": "^2.5.1", "@tuimao/uni-utils": "^0.3.0", "@types/lodash-es": "^4.17.4", "lodash": "^4.17.21", "miniprogram-api-typings": "^3.1.3"}, "prettier": "@tuimao/eslint/prettier.js", "devDependencies": {"@tuimao/eslint": "^1.2.0-bata.3", "@types/archiver": "^5.1.1", "@types/fs-extra": "^9.0.12", "@types/node": "^14.14.9", "archiver": "^5.3.0", "assert": "^2.0.0", "consola": "^2.15.3", "eslint": "^7.32.0", "esno": "^0.9.1", "execa": "^5.1.1", "fs-extra": "^10.0.0", "rimraf": "^3.0.2", "tern": "^0.24.3", "typescript": "^4.0.5", "vue": "^2.6.14"}, "id": "u-draw-poster", "keywords": ["海报", "绘制", "分享", "小程序", "canvas"]}