<template>
	<view class="search-park">
			<!-- #ifndef MP-ALIPAY -->
		<view class="search-header" :style="{ height: (statusBarHeight + navHeight) + 'px' }">
			<!-- 顶部菜单 start -->
			<view class="nav-bar-center-default" :style="{ top: statusBarHeight + 'px', height: navHeight + 'px' }">
				<view class="title" :style="{ height: navHeight + 'px', 'line-height': navHeight + 'px' }">
					<view class="flex jus alc padl-40" :style="{ height: '100%', width: navHeight + 'px', 'line-height': navHeight + 'px' }"
						@click="toIndex" hover-class="park-hover-class">
						<van-icon name="arrow-left" size="36rpx" />
					</view>
					<view class="title--center">停车缴费</view>
				</view>
			</view>
			<view class="flex100" :style="{ height: (statusBarHeight + navHeight + 4) + 'px' }" />
			<!-- 顶部菜单 end -->
		</view>
			<!-- #endif -->
		<view class="my-record-out">
			<view class="flex-row alc" style="padding: 0 40rpx 10rpx 44rpx;">
				<view style="font-size: 34rpx;font-weight: 500;color: #333">停车费用查询</view>
				<view class="flex-1 jue">
					<button>
						<view class="my-record" @click="handleCarNumber">
							{{isSpecial?'普通车牌':'特殊车牌'}}
							<van-icon name="arrow" size="20rpx" color="#999" />
						</view>
					</button>
				</view>
			</view>

			<swiper @change="swiperChange" class="my-swiper" :style="showPark ? 'height: 350rpx' : 'height: 350rpx'"
				:previous-margin="showPark ? preMargin : '20rpx'" :next-margin="showPark ? '30rpx' : '20rpx'">

				<swiper-item v-if="showPark" v-for="(item, index) in parkInfo" :key="item.id" class="flex alc">
					<!-- #ifdef MP-ALIPAY || APP-PLUS -->
					<view class="park-popup flex-col flex-1" :class="'park-popup-bg' + (index % 4)" style="margin-right:  30rpx">
						<!-- #endif -->
						<!-- #ifdef MP-WEIXIN -->
						<view class="park-popup flex-col flex-1" :class="'park-popup-bg' + (index % 4)" style="margin-right:  30rpx"
							:style="{ height: current == index ? '320rpx' : '280rpx' }">
							<!-- #endif -->
							<view class="park-content flex-col flex-1">
								<view style="font-size: 72rpx;font-weight: bold;letter-spacing: 8rpx;">
									{{ item.plateNumber }}</view>
								<view class="park-name flex-row flex-1 ale mart-20">
									<view class="of-clamp1">{{ item.parkName }}</view>
								</view>
								<view class="flex-row flex-1 ale mart-10">入场：{{ form.timestampToTime(item.inTime) }}
								</view>
								<template v-if="item.ispre == 1">
									<view class="flex-row flex-1 ale mart-10" style="font-size: 24rpx;">
										已预付，如已出场，还请忽略此停车信息</view>
								</template>
							</view>
						</view>
				</swiper-item>

				<swiper-item>
					<view class="top-card flex-col">
						<view class="top-card-title">请输入车牌号</view>
						<view class="flex-row mart-10 marb-40 padr-10" v-if="!isSpecial">
							<view v-for="(item, index) in [1, 2, 3, 4, 5, 6, 7, 8]" :key="index">
								<view class="my-license-item"
									:class="[{ 'my-license-current': index == currentIndex }, { 'zfbWidth': showPark }, { 'zfbWidth55': !showPark }]"
									@click="licenseClick(index)">
									<view :class="[{ 'my-license-new': index == 7 && !strArr[7] }]">
										{{ strArr[index] || (index == 7 ? "新能源" : "") }}
									</view>
								</view>
							</view>
						</view>
						<view class="car-number" v-if="isSpecial">
							<input class="car-number-input" maxlength="20" v-model.trim="licenseNum" minlength="3" />
						</view>
						<scroll-view style="white-space: nowrap;" scroll-x="true" class="hidden-scrollbar">
							<view class="caNumber" 
								:key="index"
								v-for="(item,index) in myLicense" 
								@click="myLicenseClick(item)"
								hover-class="park-hover-class"
								:style="{'color': item === avtiveLicense ? '#007AFF' : ''}">
								{{item}}
								<image v-if="item.unlicense" class="unlicensedIcon" src="https://image.bolink.club/yima/unlicensed_icon.png" ></image>
							</view>
						</scroll-view>
					</view>
				</swiper-item>
			</swiper>

			<view style="text-align: center;margin-top: 40rpx;">
				<button @click="checkPhone" class="my-button" :style="showOpacity ? 'opacity: 0.6' : ''">停车费用查询</button>
			</view>
		</view>
		<view class="tip" @click="jumpRecord">缴费记录</view>
		<keyword :isShow="keyState" @exit="exit" @inputchange="getKey" :keyBoardType="keyBoardType" :oinp="str" @ok="confirm" />
		<!-- #ifdef MP-ALIPAY -->
		<template v-if="!isCloseAdvertising">
			<view :class="advertisingEnd ? 'advertising advertising-containr' : 'advertising-containr'">
				<riding-risk-component 
					ref="ridingRisk"
					style="width:100vw;" 
					onAdLoad="onAdLoad" 
					onSuccess="onSuccess"
					onClose="onClose"
					onAdError="onAdError"
					spaceCode="50_2023102525000067972"
					onInsuranceStatus="onInsuranceStatus"
				>
				</riding-risk-component>
				<van-icon @click="closeAdvertising" v-if="isShowAdvertising && !advertisingEnd" name="cross" class="icon-cross" color="#000" size="25px" />
			</view>
		</template>
		<!-- #endif -->
	</view>
</template>

<script>
// #ifdef MP-ALIPAY
const { requestSubscribeMessage } = requirePlugin('subscribeMsg');
const parkingPlugin = requirePlugin("parkingMsg");
// #endif
import apis from "../../common/apis/index";
import util from "../../common/utils/util";
import keyword from '../../components/keyword/keyword.vue';
import form from '../../common/utils/form';
let app = getApp();
export default {
	components: {
		keyword,
	},
	data() {
		return {
			form: form,
			navHeight: 40,
			statusBarHeight: 47,
			keyState: false, // 车牌键盘-显示不显示
			showPark: false,
			keyBoardType: 1, // 1-省份简写键盘 2-中英文键盘
			str: "", // 输入的车牌号
			maxIndex: 6,
			parkInfo: [],
			strArr: [" ", " ", " ", " ", " ", " ", " "], // 切割的车牌号数组
			current: 0, // 当前车牌下标
			currentIndex: null, // 当前开始输入的下标
			myLicense: '', // 我的常用车牌
			licenseNum: '', // 车牌号
			preMargin: "40rpx", // swiper 前边距
			nextMargin: "60rpx", // swiper 后边距
			showMyLicense: true,
			mobile: uni.getStorageSync('mobile'),
			userInfo: app.globalData.userInfo, // 用户信息
			avtiveLicense: '',
			isSpecial:false,
			isShowAdvertising: false,
			advertisingEnd: false,
			isCloseAdvertising: false, //关闭勾选框广告
		}
	},

	onLoad(options) {
		console.log('options----->', options);
		getApp().getSystemInfo().then(res => {
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight || res.titleBarHeight;
		});
		// this.existorder();
		this.getUserPlateNumber()
		
		// #ifdef MP-WEIXIN
		this.isShowAdvertising = false	
		this.advertisingEnd = false
		// #endif

		// #ifdef MP-ALIPAY
		this.$scope.onAdLoad = this.onAdLoad.bind(this)
		this.$scope.onSuccess = this.onSuccess.bind(this)
		this.$scope.onClose = this.onClose.bind(this)
		this.$scope.onAdError = this.onAdError.bind(this)
		this.$scope.onInsuranceStatus = this.onInsuranceStatus.bind(this)
		// #endif
	},
	computed: {
		showOpacity() {
			let flag = false;
			if (this.showPark) {
				flag = this.current == this.parkInfo.length && (this.isSpecial?this.licenseNum.length < 3:this.licenseNum.length < 7);
			} else {
				flag = this.isSpecial?this.licenseNum.length < 3:this.licenseNum.length < 7;
			}
			return flag;
		}
	},
	methods: {
		closeAdvertising() {
			this.isShowAdvertising = false
			this.advertisingEnd = false
			this.isCloseAdvertising = true
		},
		onAdLoad(interstitialAd, spaceCode) {
			interstitialAd.show({
				spaceCode,
				rtaExtMap: {
					touch_point_location: 'order_page',
					insurance_scene_code:'ma_shang_sparking_lot'
				},
			});
		},
		onSuccess() {
			console.log("加载成功广告")
			this.isShowAdvertising = true
			this.advertisingEnd = false
		},
		onClose() {
			console.log("广告关闭成功")
			this.isShowAdvertising = false
			this.advertisingEnd = false
		},
		onAdError(err) {
			console.log(err, "广告加载失败")
			this.isShowAdvertising = false
			this.advertisingEnd = false
		},
		onInsuranceStatus(res) {
			console.log(res, "结束弹框")
			this.advertisingEnd = true
		},
		handleCarNumber() {
			this.isSpecial = !this.isSpecial
			this.licenseNum=''
			this.strArr=[]
			this.avtiveLicense=''
			if(this.isSpecial){
				this.keyState=false
				this.currentIndex=null
			}
		
		},
		// 返回首页
		toIndex() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
		// 获取用户信息的回调
		getUserProfile(e) {
			app.updataUserInfo().then(() => {
				this.userInfo = app.globalData.userInfo;
				// this.existorder();
			});
		},
		// 取消车牌键盘
		exit() {
			// this.$emit('getLicense', this.str);
			this.licenseNum = this.str;
			this.keyState = false;
		},
		// 获取键盘的值
		getKey(val) {
			if ((this.str.length > this.maxIndex + 1) && val != "delete") {
				return false
			}
			if (val == 'delete') {

				// 当前位置无值则删除前一位，有值则置空
				if (this.strArr[this.currentIndex] == " " || this.strArr[this.currentIndex] == undefined || this.strArr[this.currentIndex] == "") {
					this.strArr.splice(this.currentIndex - 1, 1, " ");
					this.currentIndex = this.currentIndex - 1;
				} else {
					this.strArr.splice(this.currentIndex, 1, this.currentIndex === 7 ? "" : " ");
					this.currentIndex = this.currentIndex;
				}

			} else {
				this.strArr.splice(this.currentIndex, 1, val);
				this.currentIndex = this.currentIndex + 1;
			}

			if (this.currentIndex > this.maxIndex) {
				this.currentIndex = this.maxIndex;
			} else if (this.currentIndex < 0) {
				this.currentIndex = 0;
			}

			this.str = this.strArr.join("").trim();
			this.licenseNum = this.str;

			if (this.str.length == 0 || this.currentIndex == 0) {
				this.keyBoardType = 1;
			} else {
				this.keyBoardType = 2;
			}

			// // 截取得到单个字符数组
			// this.strArr = this.str.split("");
		},
		confirm(e) {
			// this.$emit('getLicense', this.str);
			this.licenseNum = this.str;
			this.keyState = false;
		},
		// 点击缴费记录
		jumpRecord() {
			// this.existorder(3);
			this.activityClick_c(3);
		},
		// 停车信息
		existorder(type) {
			apis.homeApis.existorder().then((res) => {
				if (res.data && res.data.length > 0 && this.mobile) {
					this.parkInfo = res.data;
					this.showPark = true;
				} else {
					this.showPark = false;
				}
			});
			if (type) {
				this.activityClick_c(type);
			}
		},
		//顶部滑动
		swiperChange(e) {
			let current = e.detail.current;
			let len = this.parkInfo.length;
			this.current = current;

			if (current == 0) {
				this.preMargin = "40rpx";
				this.nextMargin = "60rpx";
			} else if (current == len) {
				this.preMargin = "40rpx";
				this.nextMargin = "40rpx";
			} else {
				this.preMargin = "40rpx";
				this.nextMargin = "60rpx";
			}
		},
		// 点击不同位置的输入框
		licenseClick(index) {
			console.log('点击车牌框', index)
			if (this.showMyLicense && this.mobile) {
				// #ifdef MP-WEIXIN
				this.bindCarNumberTips(null, () => {
					// #endif
					if (index == 0) {
						this.keyBoardType = 1;
					} else {
						this.keyBoardType = 2;
					}
					this.currentIndex = index;
					if (index == 7) {
						this.maxIndex = index;
					}
					this.keyState = true;
					this.showMyLicense = false
					// #ifdef MP-WEIXIN
				});
				// #endif

				// Dialog.confirm({
				//   title: '提示',
				//   message: '绑定车牌，查询停车费更便捷哦！',
				// 	cancelButtonText:'取消',
				// 	confirmButtonText:'去绑定',
				// 	confirmButtonColor:'#1677FF'
				// }).then(() => {
				// 		let startTime = new Date().getTime();
				// 		let url = `/pagesD/car/carSubscribe?fromTo=park`;
				//     uni.navigateTo({
				//     	url: url,
				//     	complete: (res) => {
				//     		app.eventRecord({
				//     			keyWord: '我的-顶部-车辆滚动卡片',
				//     			clickType: 'Button',
				//     			jumpType: '本程序页面',
				//     			jumpDesc: '编辑车辆信息页',
				//     			result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
				//     			startTime: startTime,
				//     			endTime: new Date().getTime()
				//     		});
				//     	}
				//     })
				//   })
				// .catch(() => {
				// this.showMyLicense = true;
				// 		    let len = this.str.length;
				// 		    	if (index == 0) {
				// 		    		this.keyBoardType = 1;
				// 		    	} else {
				// 		    		this.keyBoardType = 2;
				// 		    	}

				// 		    	this.currentIndex = index;
				// 		    	if (index == 7) {
				// 		    		this.maxIndex = index;
				// 		    	}

				// 		    	this.keyState = true;
				// 					this.showMyLicense=false
				// });
			} else {
				let len = this.str.length;
				// if (len == 0 || index == 0) {
				if (index == 0) {
					this.keyBoardType = 1;
				} else {
					this.keyBoardType = 2;
				}
				this.currentIndex = index;
				if (index == 7) {
					this.maxIndex = index;
				}

				this.keyState = true;
			}

		},
		//点击常用车牌时
		myLicenseClick(num) {
			this.licenseNum = num;
			this.avtiveLicense = num;
			if(num.length>8){
				this.isSpecial=true
			}
			if (this.licenseNum) {
				this.str = this.licenseNum;
				let temp = this.str.split("");
				for (let i = 0; i < this.licenseNum.length; i++) {
					this.strArr[i] = temp[i];
				}
				this.strArr[7] = temp.length > 7 ? temp[7] : ''
				this.keyBoardType = 2;
			}
		},
		async checkPhone(row){
			let res=await util.getMobile()
			if(res.flag===0){
				row=JSON.stringify(row)
				uni.setStorageSync("jumpRow",row)
				uni.navigateTo({
					url: `/pagesA/agreementLogin/login?isEmpower=true&jumpType=1&activeName=control`,
				})
			}else{
				this.costQuery();
			}
		},	
		// 点击费用查询
		costQuery() {
			// #ifdef MP-WEIXIN
			app.subscription().then(res => {
				// #endif
				// #ifdef MP-ALIPAY
				parkingPlugin.parkingMessageAuth();
				// #endif
				if (!this.showPark) {
					this.query();
				} else {
					let len = this.parkInfo.length;
					if (this.current == len) {
						this.query();
					} else {
						let item = this.parkInfo[this.current];
						let params = {
							unionId: item.unionId,
							parkId: item.parkId,
							orderId: item.orderId,
							inTime: item.inTime,
							plateNumber: item.plateNumber
						};
						let type = 2;
						this.getParkOrder(type, params);
					}
				}
				// #ifdef MP-WEIXIN
			})
			// #endif
		},
		// 临时停车费用查询
		query() {
			if ((this.licenseNum &&!this.isSpecial &&form.isLicensePlate(this.licenseNum))||(this.licenseNum &&this.isSpecial&&this.licenseNum.length>=3)) {
				// app.subscription().then(res => {
				// 	let params = {
				// 		plateNumber: this.licenseNum
				// 	};
				// 	let type = 1;
				// 	this.getParkOrder(type, params);
				// })
				let params = {
					plateNumber: this.licenseNum
				};
				let type = 1;
				this.getParkOrder(type, params);
			}else{
				uni.showToast({
					title: "输入车牌号有误,请重新输入",
					icon: "none"
				});
			}
		},
		// 获取订单信息
		getParkOrder(type, params) {
			// if (type == 1) { // 手动输入-临停费用查询
			apis.homeApis.querybyplatenumber(params).then((res) => {

				if (res.status === 200) {
					let data = res.data
					if (data.orderList.length > 0 && data.orderList.length == 1) {
						this.parkOrderInfo = data.orderList[0];
						this.parkOrderInfo.isView = data.isView == 1 ? true : false
						this.parkOrderInfo.toDetail = true
					} else if (data.orderList.length > 0) {
						this.parkOrderInfo.toDetail = false
					} else {
						this.parkOrderInfo = null
					}
					this.jumpToPay(params, '停车缴费页-临停费用查询');
					app.eventRecord({
						keyWord: '停车缴费页-手动输入车牌号查询',
						clickType: 'Button',
						jumpType: '本程序页面',
						jumpDesc: '停车缴费页',
						result: '成功',
						startTime: new Date().getTime(),
						endTime: new Date().getTime()
					});
				}
				else if (res.status === 213) {
					// this.bindCarNumberTips(res.msg || '您还未绑定过该车牌，请绑定后再查询');
				}
				else {

				}
			});

		},
		activityClick_c(type) {
			let startTime = new Date().getTime();
			if (type == 1) {
				getApp().jumpForwardUrl('travelMall-停车缴费页-畅由banner', '图片');
			} else if (type == 2) {
				getApp().jumpForwardUrl('travelMall-停车缴费页-畅由弹窗', '图片');
			} else if (type == 3) { // 点击缴费信息授权后的-自动跳转
				uni.navigateTo({
					url: `/pagesC/pay/carOrderList`,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: '点击停车缴费页-顶部缴费记录',
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: '缴费记录页面',
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: startTime,
							endTime: new Date().getTime()
						});
					}
				})
			}
		},
		// 绑定车牌提醒
		bindCarNumberTips(text, callback) {
			let message = text || '绑定车牌，查询停车费更便捷哦！'
			// #ifdef MP-ALIPAY
			uni.showModal({
				title: '提示',
				content: message,
				confirmText: '去绑定',
				success: function (res) {
					if (res.confirm) {
						let startTime = new Date().getTime();
						let url = `/pagesD/car/carSubscribe?fromTo=park`;
						uni.navigateTo({
							url: url,
							complete: (res) => {
								app.eventRecord({
									keyWord: '我的-顶部-车辆滚动卡片',
									clickType: 'Button',
									jumpType: '本程序页面',
									jumpDesc: '编辑车辆信息页',
									result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
									startTime: startTime,
									endTime: new Date().getTime()
								});
							}
						})
					} else if (res.cancel) {
						if (callback) callback();
					}
				}
			});
			// #endif
			// #ifdef MP-WEIXIN
			Dialog.confirm({
				title: '提示',
				message: message,
				cancelButtonText: '取消',
				confirmButtonText: '去绑定',
				confirmButtonColor: '#1677FF',
			})
				.then(() => {
					let startTime = new Date().getTime();
					let url = `/pagesD/car/carSubscribe?fromTo=park`;
					uni.navigateTo({
						url: url,
						complete: (res) => {
							app.eventRecord({
								keyWord: '我的-顶部-车辆滚动卡片',
								clickType: 'Button',
								jumpType: '本程序页面',
								jumpDesc: '编辑车辆信息页',
								result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
								startTime: startTime,
								endTime: new Date().getTime()
							});
						}
					})
				})
				.catch(() => {
					if (callback) callback();
				});
			// #endif
		},
		jumpToPay(params, msg) {
			let startTime = new Date().getTime();
			if (this.parkOrderInfo) {
				uni.setStorageSync("lastCarNumber", params.plateNumber)
				this.keyState = false;
				let url = this.parkOrderInfo.toDetail ? `/pagesC/park/pay?plateNumber=${params.plateNumber}&parkInfo=${encodeURIComponent(JSON.stringify(this.parkOrderInfo))}` : `/pagesC/park/morePay?plateNumber=${params.plateNumber}&parkInfo=${JencodeURIComponent(JSON.stringify(this.parkOrderInfo))}`
				uni.navigateTo({
					url: url,
					complete: (res) => {
						getApp().eventRecord({
							keyWord: msg,
							clickType: 'Button',
							jumpType: '本程序页面',
							jumpDesc: this.parkOrderInfo.toDetail ? '停车订单支付页面' : '停车订单列表',
							result: res.errMsg == 'navigateTo:ok' ? '成功' : '失败',
							startTime: startTime,
							endTime: new Date().getTime()
						});
					}
				})
			} else {
				setTimeout(() => {
					uni.showToast({
						title: "暂未查询到相关停车订单",
						icon: "none",
						duration: 4000
					});
				}, 200)
			}
		},
		getUserPlateNumber() {
			apis.homeApis.getUserPlateNumber().then(res => {
				let data = res.data
				if (data.length > 0 && data.length == 1) {
					this.showMyLicense = false
					this.myLicense = []
					this.licenseNum = data[0]
					this.str = this.licenseNum;
					let temp = this.str.split("");
					for (let i = 0; i < this.licenseNum.length; i++) {
						this.strArr[i] = temp[i];
					}
				} else if (data.length > 0) {
					this.showMyLicense = false
					this.myLicense = data
				} else {
					this.showMyLicense = true
					this.myLicense = []
					this.strArr = [" ", " ", " ", " ", " ", " ", " "]
					this.licenseNum = uni.getStorageSync("shortName") || this.licenseNum;
					this.str = this.licenseNum;
					let temp = this.str.split("");
					for (let i = 0; i < this.licenseNum.length; i++) {
						this.strArr[i] = temp[i];
					}
					this.keyBoardType = 2;
				}
				if (uni.getStorageSync("lastCarNumber")) {
					this.licenseNum = uni.getStorageSync("lastCarNumber")
					uni.removeStorageSync("lastCarNumber")
					this.str = this.licenseNum;
					let temp = this.str.split("");
					for (let i = 0; i < this.licenseNum.length; i++) {
						this.strArr[i] = temp[i];
					}
					this.keyBoardType = 2;
				}
			})
		},
	}
}
</script>

<style lang="scss" scoped>
.search-park {
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: #F8F8F8;

	.search-header {
		// background-image: linear-gradient(to right, #2F5AE5, #468CFE);
		background-color: #468CFE;
		width: 100%;

	}



}

.my-record-out {
	background-color: #fff;
	margin: 20rpx 28rpx 20rpx;
	padding-bottom: 10rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 34rpx 0 rgba(0, 0, 0, 0.1);
}

.my-record {
	color: #999;
	font-size: 28rpx;
	letter-spacing: 2rpx;
}

.my-swiper {
	width: 100%;
}

.park {
	width: 100%;
	background-color: #fff;
}

.top-card {
	height: 330rpx;
	margin: 10rpx 6rpx 6rpx 6rpx;
	padding: 0 0 20rpx 0;
	font-size: 28rpx;
	color: #999;
	// background-color: #F3F6FF;
	border-radius: 20rpx;
	box-shadow: 0px 4rpx 16rpx 0px rgba(49, 144, 251, 0.20);
}

.top-card-title {
	font-size: 32rpx;
	padding-top: 40rpx;
	padding-left: 30rpx;
	padding-bottom: 30rpx;
	color: #666;
}

.toggleList {
	width: 100%;
	display: flex;
}

.top-card-normal {
	width: 120rpx;
	font-size: 24rpx;
	font-family: PingFang-SC-Medium, PingFang-SC;
	font-weight: 500;
	color: #666666;
	margin: 20rpx 20rpx 10rpx 8rpx;
}

.toggleNumList {
	display: flex;
	margin: 10rpx 0rpx 10rpx 0rpx;
	flex-wrap: wrap;
}

.toggleNum {
	width: 155rpx;
	font-size: 24rpx;
	text-align: center;
	height: 40rpx;
	background: #FAFAFB;
	border-radius: 22rpx;
	border: 1px solid #E2E2E2;
	font-weight: 500;
	color: #2F2F2F;
	margin-right: 10rpx;
	margin-top: 10rpx;
}

.park-popup {
	height: 320rpx;
	margin: 4rpx;
	font-size: 26rpx;
	color: #fff;
	background-color: #308EFC;
	border-radius: 20rpx;
}


.park-name {
	font-size: 28rpx;
	font-weight: 500;
	// color: #cde5ff;
}

.my-button {
	width: 380rpx;
	color: #fff;
	font-size: 32rpx !important;
	letter-spacing: 4rpx;
	box-shadow: 0px 4rpx 20rpx 0px rgba(49, 144, 251, 0.20);
	background-color: #459EFF;
	border-radius: 20px;
	padding: 10px 50px;
}

.activity-view {
	width: 100%;
	text-align: center;
	position: relative;
	top: 25%;
}
.my-license-item{
	// #ifdef MP-ALIPAY
	width:56rpx;	
	// #endif 
	
}
.top-card-normal{
	// #ifdef MP-ALIPAY
	width:80rpx;	
	// #endif 
	
}
.hidden-scrollbar{
	padding:0 10rpx;
}
.caNumber{
	position: relative;
	display: inline-block;
	min-width: 180rpx;
	height: 48rpx;
	line-height: 48rpx;
	margin-right: 16rpx;
	background: #EEF8FE;
	text-align: center;
	font-size: 26rpx;
	font-family: PingFang-SC-Bold, PingFang-SC;
	font-weight: bold;
	color: #242E3E;
	border-radius: 32rpx;
	padding:0 10rpx;
	.unlicensedIcon{
		position: absolute;
		top: 0;
		right: 0;
		width: 20rpx;
		height: 17rpx;
	}
}
	.tip {
		text-align: center;
		font-size: 28rpx;
		color: #308EFC;
	}

	.car-number {
		font-size: 28rpx;
		border: 1rpx solid #e8e4e4;
		height: 80rpx;
		line-height: 80rpx;
		display: flex;
		color: #4a556b;
		margin: 10rpx 50rpx 50rpx 50rpx;

		.car-number-input {
			height: 70rpx;
			line-height: 70rpx;
			padding: 0 10rpx;
			display: inline-block;
			width: 100%;
		}
	}
	.advertising-containr {
		position: relative;
	}
	.advertising {
		position: fixed;
		bottom: -190rpx;
		left: 0;
		z-index: 99;
		width: 100vw !important;
	}
	.icon-cross {
		position: absolute;
		right: 30rpx;
		top: 190rpx;
	}
</style>
