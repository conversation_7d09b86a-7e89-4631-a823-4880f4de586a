.action-sheet-index {
    display: block;
    height: initial;
}
@import "../common/index.acss";

.action-sheet-index .van-action-sheet {
    max-height: 90% !important;
    max-height: var(--action-sheet-max-height, 90%) !important;
    color: #323233;
    color: var(--action-sheet-item-text-color, #323233);
}

.action-sheet-index .van-action-sheet__cancel,
.action-sheet-index .van-action-sheet__item {
    padding: 14px 16px;
    text-align: center;
    font-size: 16px;
    font-size: var(--action-sheet-item-font-size, 16px);
    line-height: 22px;
    line-height: var(--action-sheet-item-line-height, 22px);
    background-color: #fff;
    background-color: var(--action-sheet-item-background, #fff);
}

.action-sheet-index .van-action-sheet__cancel--hover,
.action-sheet-index .van-action-sheet__item--hover {
    background-color: #f2f3f5;
    background-color: var(--active-color, #f2f3f5);
}

.action-sheet-index .van-action-sheet__cancel:after,
.action-sheet-index .van-action-sheet__item:after {
    border-width: 0;
}

.action-sheet-index .van-action-sheet__cancel {
    color: #646566;
    color: var(--action-sheet-cancel-text-color, #646566);
}

.action-sheet-index .van-action-sheet__gap {
    display: block;
    height: 8px;
    height: var(--action-sheet-cancel-padding-top, 8px);
    background-color: #f7f8fa;
    background-color: var(--action-sheet-cancel-padding-color, #f7f8fa);
}

.action-sheet-index .van-action-sheet__item--disabled {
    color: #c8c9cc;
    color: var(--action-sheet-item-disabled-text-color, #c8c9cc);
}

.action-sheet-index
    .van-action-sheet__item--disabled.van-action-sheet__item--hover {
    background-color: #fff;
    background-color: var(--action-sheet-item-background, #fff);
}

.action-sheet-index .van-action-sheet__subname {
    margin-top: 8px;
    margin-top: var(--padding-xs, 8px);
    font-size: 12px;
    font-size: var(--action-sheet-subname-font-size, 12px);
    color: #969799;
    color: var(--action-sheet-subname-color, #969799);
    line-height: 20px;
    line-height: var(--action-sheet-subname-line-height, 20px);
}

.action-sheet-index .van-action-sheet__header {
    text-align: center;
    font-weight: 500;
    font-weight: var(--font-weight-bold, 500);
    font-size: 16px;
    font-size: var(--action-sheet-header-font-size, 16px);
    line-height: 48px;
    line-height: var(--action-sheet-header-height, 48px);
}

.action-sheet-index .van-action-sheet__description {
    text-align: center;
    padding: 20px 16px;
    padding: 20px var(--padding-md, 16px);
    color: #969799;
    color: var(--action-sheet-description-color, #969799);
    font-size: 14px;
    font-size: var(--action-sheet-description-font-size, 14px);
    line-height: 20px;
    line-height: var(--action-sheet-description-line-height, 20px);
}

.action-sheet-index .van-action-sheet__close {
    position: absolute !important;
    top: 0;
    right: 0;
    line-height: inherit !important;
    padding: 0 16px;
    padding: var(--action-sheet-close-icon-padding, 0 16px);
    font-size: 22px !important;
    font-size: var(--action-sheet-close-icon-size, 22px) !important;
    color: #c8c9cc;
    color: var(--action-sheet-close-icon-color, #c8c9cc);
}

.action-sheet-index .van-action-sheet__loading {
    display: -webkit-flex !important;
    display: flex !important;
}
