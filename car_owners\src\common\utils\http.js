import BaseConfig from '../config/index.config.js'
import util from './util.js'
import apis from '../apis/index.js'
let source=localStorage.getItem('h5_source')
if(source=='zfb'){
	/* BaseConfig.parkAppid=BaseConfig.baseUrl.includes('mt.bolink.club')?'2017080408033352':'wxc4eced1c73751e0b' */
}


const HTTP_STATUS = {
	SUCCESS: 200,
	CREATED: 201,
	ACCEPTED: 202,
	NOT_LOGGED: 203,
	CLIENT_ERROR: 400,
	AUTHENTICATE: 301,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504
}
// 刷新TOKEN
let isRefreshing = true;
// 请求队列
let subscribers = [];
function onAccessTokenFetched() {
	subscribers.forEach((callback) => {
		callback();
	})
	subscribers = [];
}

function addSubscriber(callback) {
	subscribers.push(callback)
}
/*
 * 接口请求封装
 *
 * @param  {url}    string      接口路径
 * @param  {method} string      请求类型
 * @param  {data}   object      接口请求数据
 * @type  检查token,默认没有token会先调登录接口
 * @mask  请求接口显示的loading效果是否有 mask = true 表示期间不可操作
 * @loading  请求接口时是否显示loading效果
 */
function request(url, data, type = true, mask = false, loading=true, method = "POST", isCompleteUrl=false,contentType) {
	let isInit = type,newdata = {},newArr = []
	 newdata = data || {};

	if (isCompleteUrl) {
		url = url;
	} else if(url.indexOf('unionapi')==0){
		url = `${BaseConfig.advertisementUrl}/${url}`;
	}else {
		url = `${BaseConfig.baseUrl}/${url}`;
	}
	
	// #ifdef APP-PLUS
	let token = wx.getStorageSync('apptoken') || "";
	// #endif
	// #ifndef APP-PLUS
	let token = wx.getStorageSync('token') || "";
	// #endif
	if (token) {
		isInit = false;
	}
	return new Promise((resolve, reject) => {
		if(isInit && !token){
			// #ifdef MP-ALIPAY || MP-WEIXIN 
			util.initData().then(res => {
				isInit = false;
				http(url, newdata, res.data.token, mask, loading, method, contentType).then((res) => {					
					resolve(res)
				}).catch((res) => {
					reject(res)
				})
			}).catch((err)=>{

				
			})
			// #endif
			// #ifdef APP-PLUS
			getApp().logOut()
			return false
			// #endif
		}
		!isInit && http(url, newdata, token, mask, loading, method, contentType).then((res) => {
			// #ifdef H5
			if(res.code==40010){
				getApp().logOut()
			}
			// #endif
			resolve(res)
		}).catch((res) =>{
			reject(res)
		})
	})
}

function http(url, data, token, mask, loading, method,contentType,callback){
	let _ContentType = (method == 'GET'? "application/x-www-form-urlencoded" : contentType? contentType:"application/json");
	let header = {
		"Content-Type": _ContentType,
		'copyright': BaseConfig.copyright
		// #ifdef MP-ALIPAY
		,'source':'zfb',
		'type':'2',
		'appid': uni.getAccountInfoSync().miniProgram.appId
		// #endif
		// #ifdef MP-WEIXIN
		,'source':'wx',
		'type':'1',
		'appid': uni.getAccountInfoSync().miniProgram.appId
		// #endif
		// #ifdef H5
		,'source': localStorage.getItem('h5_source'),
		'type':'3',
		'appid': BaseConfig.parkAppid
		// #endif
	};
	if (token) {
		// #ifdef APP-PLUS
		header['apptoken'] = uni.getStorageSync('apptoken') || token;
		// #endif
		// #ifndef APP-PLUS
		header['token'] = wx.getStorageSync('token') || token;
		// #endif
	}
	return new Promise((resolve, reject) => {
		if (loading) {
			uni.showLoading({
				title: '加载中...',
				mask: mask
			});
		}
		uni.request({
			url: url,
			data: data || {},
			method: method || 'POST',
			// #ifdef APP-PLUS
			sslVerify: false,
			// #endif
			header: header,
			success: function(res) {
				// #ifdef APP-PLUS
				console.log('接口：',url,'-->结果：',res,'-->参数：',data)
				// #endif
				if (loading) uni.hideLoading();
				if (callback) return callback(res.data);
				if (res.statusCode == HTTP_STATUS.SUCCESS) {
					// #ifdef APP-PLUS
					if(res.data.status==203){
						getApp().logOut()
						return false
					}
					// #endif
					if(res.data.status == 200 || res.data.status == -1){
						resolve(res.data);
					}
					else if (res.data.status == 213) {
						// 提示错误信息
						util.showToast(res.data.msg || '抱歉，服务器出小差了')
						resolve(res.data);
					}
					else if (res.data.status === HTTP_STATUS.NOT_LOGGED) {
						if(BaseConfig.h5Type==="park"){
							// util.parkH5Login(BaseConfig.parkAppid)
						}else{
							addSubscriber(()=> {
								http(url, data, token, mask, loading, method,contentType, resolve)
							})
							if (isRefreshing) {
								doRequest().then(res => {
									// 依次去执行缓存的接口
									onAccessTokenFetched();
									isRefreshing = true;
								})
							}
							isRefreshing = false;
						}
					}
					else {
						resolve(res.data);
					}
				} else {
					// util.showToast(res.errMsg)
					reject(res.statusCode, res.errMsg);
				}
			},
			fail: function(res) {
				setTimeout(() => {
					uni.hideLoading();
					// util.showToast('服务器未响应！')
					reject(res.statusCode, res.errMsg);
				},5000)
			}
		});
	})
	
}
// 刷新token
function doRequest(response) {
	return new Promise((resolve, reject) =>{
		// #ifdef MP-WEIXIN || MP-ALIPAY
		uni.login({
			success: (result) => {
				// 每次打开小程序刷新一遍，防止session_key过期
				apis.homeApis.getOpenId({code: result.code}).then(res => {
					if (res.status === 200) {
						uni.setStorageSync('userUnionId', res.data.unionId)
						res.data.userId&&uni.setStorageSync('uid', res.data.userId)
						uni.setStorageSync('token', res.data.token)
						uni.setStorageSync('session_key',res.data.session_key);
						resolve(res);
					}
					else {
						reject(res);
					}
					
				}).catch(res => {
					reject(res);
				})
			},
			fail: (err) => {
				reject(err);
			}
		})
		// #endif
	})
}
/*
 * 文件上传封装
 *
 * @param  {url}          string      接口路径
 * @param  {tempFilePath} string      文件路径
 * @param  {data}         object      额外数据径
 */
function upload(url, tempFilePath, fileName, data) {
	if (!(String(url).match('http://') || (String(url).match('https://')))) {
		url = `${BaseConfig.baseUrl}/${url}`
	}
	return new Promise((resolve, reject) => {
		uni.showLoading({
			title: '上传中...',
			mask: true
		});
		uni.uploadFile({
			url: url, //仅为示例，非真实的接口地址
			filePath: tempFilePath,
			name: fileName || 'file',
			formData: data,
			// #ifndef H5
			header: {
				"Content-Type": "multipart/form-data" ,
				"token": wx.getStorageSync('token') || '',
				'copyright': BaseConfig.copyright
			},
			// #endif
			success: (res) => {
				console.log("---------->>>>>", res);
				res.data = JSON.parse(res.data);
				if (res.statusCode == HTTP_STATUS.SUCCESS) {
					if(res.data.status == 200){
						resolve(res.data);
						uni.hideLoading();
					} else {
						// util.showToast(res.data.msg)
						reject(res.data);
					}
				} else {
					// util.showToast(res.errMsg)
					reject(res);
				}
			},
			fail: (res) => {
				setTimeout(() => {
					uni.hideLoading(res);
					util.showToast({title: res.errMsg,icon: 'none'})
					reject(res.statusCode, res.errMsg);
				},2000)
			}
		});
	})
}

function get(url, data, resbook) {
	return request(url, 'GET', data, resbook);
}

function post(url, data, resbook) {
	return request(url, 'POST', data, resbook);
}

export default {
	request,
	upload
}
