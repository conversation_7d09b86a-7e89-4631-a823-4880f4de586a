<view class='rate-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='van-rate {{customClass}}' catchTouchMove='antmoveAction' data-antmove-touchmove='onTouchMove'>
    <view class='van-rate__item' a:for='{{ innerCountArray }}' a:key='{{index}}' style="padding-right: {{ index !== count - 1 ? utils.addUnit(gutter) : '' }}" ref-numbers='{{ innerCountArray }}'>
      <van-icon name='{{ index + 1 <= innerValue ? icon : voidIcon }}' class='van-rate__icon' style='font-size: {{ utils.addUnit(size) }}' custom-class='{{iconClass}}' score='{{ index }}' color='{{ disabled ? disabledColor : index + 1 <= innerValue ? color : voidColor }}' onClick='onSelect' ref='saveChildRef1'>
      </van-icon>
      <van-icon a:if='{{ allowHalf }}' name='{{ index + 0.5 <= innerValue ? icon : voidIcon }}' class="{{ utils.bem('rate__icon', ['half']) }}" style='font-size: {{ utils.addUnit(size) }}' custom-class='{{iconClass}}' score='{{ index - 0.5 }}' color='{{ disabled ? disabledColor : index + 0.5 <= innerValue ? color : voidColor }}' onClick='onSelect' ref='saveChildRef2'>
      </van-icon>
    </view>
  </view>
</view>