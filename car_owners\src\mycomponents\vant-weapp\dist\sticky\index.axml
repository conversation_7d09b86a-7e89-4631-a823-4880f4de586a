<view class='sticky-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <import-sjs from='./index.sjs' name='computed'>
  </import-sjs>
  <import-sjs from='../wxs/utils.sjs' name='utils'>
  </import-sjs>
  <view class='{{customClass}} van-sticky' style='{{ computed.containerStyle({ fixed, height, zIndex }) }}'>
    <view class="{{ utils.bem('sticky-wrap', { fixed }) }}" style='{{ computed.wrapStyle({ fixed, offsetTop, transform, zIndex }) }}'>
      <slot>
      </slot>
    </view>
  </view>
</view>