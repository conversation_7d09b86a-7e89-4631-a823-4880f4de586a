{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../packages/core/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAA;AACjC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,OAAO,EAAE,SAAS,EAAmC,MAAM,UAAU,CAAA;AAkG9E,KAAK,UAAU,aAAa,CAAC,GAAG,IAAW;IACzC,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE;QACrB,MAAM,QAAQ,GAAsB;YAClC,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,SAAS;YACxB,IAAI,EAAE,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACrD,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,KAAK;SACf,CAAA;QACD,IAAI,UAA6B,CAAA;QACjC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,UAAU,GAAG,IAAI,CAAC,CAAC,CAAQ,CAAA;SAC5B;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,UAAU,GAAQ,IAAI,CAAC,CAAC,CAAC,CAAA;YACzB,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;SAC9B;aAAM;YACL,UAAU,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;SACnC;QACD,MAAM,OAAO,GAAG,EAAE,GAAG,QAAQ,EAAE,GAAG,UAAU,EAAE,CAAA;QAC9C,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACpD,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;YACzB,OAAO,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAA;SAC1C;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;YAC5B,OAAO,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAA;SAC7D;QACD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,OAAO,CAAC,OAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,UAAU,CAAA;YAC/D,OAAO,CAAC,OAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,UAAU,CAAA;SAChE;QACD,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,CAAC,IAAI,CACV,uDAAuD,CACxD,CAAA;YACD,OAAO,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAA;SAC7E;QACD,OAAO,OAAO,CAAA;IAChB,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,KAAK,GAAG,eAAe,EAAE,CAAA;IAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAqB,CAAA;IACxD,MAAM,EAAE,GAA8B,EAAE,QAAQ,EAAE,CAAA;IAClD,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,CAAA;IAC1B,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,CAAA;IACpC,IAAI,MAAM,GAAW,EAAE,CAAA;IACvB,IAAI,MAAM,GAAG,KAAK,CAAA;IAElB,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE;QACvB,IAAI,EAAE,CAAC,cAAc;YAAE,OAAO,EAAE,CAAC,cAAc,CAAA;QAC/C,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,EAAO;YAClF,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;QACF,MAAM,MAAM,GAAiB,SAAU,EAAE,IAAI,IAAI,EAAE,CAAA;QACnD,MAAM,GAAG,GACP,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC;YACzB,GAAG,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAA;SAChF;QACD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAA;IACxB,CAAC,CAAA;IAED,MAAM,MAAM,GAAG,KAAK,IAAI,EAAE;QACxB,IAAI,QAAQ,CAAC,OAAO;YAAE,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,EAAG,QAAQ,CAAC,OAAe,CAAC,MAAM,EAAE,CAAC,CAAA;QAElF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACvB,MAAM,IAAI,GAAc,EAAE,CAAA;QAC1B,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;SACxB;QACD,MAAM,GAAG,EAAE,CAAA;QACX,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;QAEtC,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC5B,EAAE,CAAC,GAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;YAC7B,CAAC,CAAC,CAAA;SACH;QAED,IAAI,QAAQ,CAAC,OAAO;YAAE,GAAG,CAAC,WAAW,EAAE,CAAA;QAEvC,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;IAED,MAAM,eAAe,GAAG,KAAK,EAAE,SAAS,GAAG,EAAE,EAAE,EAAE;QAC/C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,MAAM,EAAE,CAAC,MAAO,EAAE,CAAA;QACzC,IAAI,MAAM,EAAE;YACV,MAAM,GAAG,KAAK,CAAA;YACd,OAAO,OAAO,CAAC,MAAM,EAAE,CAAA;SACxB;QACD,IAAI,QAAQ,CAAC,OAAO;YAAE,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,EAAG,QAAQ,CAAC,OAAe,CAAC,MAAM,EAAE,CAAC,CAAA;QAElF,MAAM,OAAO,GAA4C,SAAS,CAAA;QAElE,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE;YAC1B,CAAC;YAAM,OAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,CAAA;SACnC;QACD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;YAC/B,CAAC;YAAM,OAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAA;SACjC;QACD,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,EAAE;gBACxB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;gBACzB,QAAQ,CAAC,OAAO,IAAI,GAAG,CAAC,WAAW,EAAE,CAAA;gBACrC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YAC9B,CAAC,CAAA;YACD,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,EAAE;gBACrB,MAAM,CAAC,GAAG,CAAC,CAAA;gBACX,QAAQ,CAAC,OAAO,IAAI,GAAG,CAAC,WAAW,EAAE,CAAA;gBACrC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YAC9B,CAAC,CAAA;YACD,GAAG,CAAC,oBAAoB,CAAM,OAAO,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IAED,MAAM,IAAI,GAAG,KAAK,EAAE,IAAc,EAAE,EAAE;QACpC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;QAC5B,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YACrB,IAAI;gBACF,EAAE,CAAC,GAAI,CAAC,IAAI,EAAE,CAAA;gBACd,MAAM,IAAI,CAAC,EAAE,CAAC,GAAI,CAAC,CAAA;gBACnB,EAAE,CAAC,GAAI,CAAC,OAAO,EAAE,CAAA;gBACjB,OAAO,IAAI,CAAA;aACZ;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,uBAAuB,CAAC;oBACtD,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,SAAS,EAAE,KAAK,CAAC,CAAA;gBAC9C,OAAO,KAAK,CAAA;aACb;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IAED,MAAM,IAAI,GAAG,GAAG,EAAE;QAChB,MAAM,GAAG,EAAE,CAAA;QACX,MAAM,GAAG,IAAI,CAAA;IACf,CAAC,CAAA;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;IAEvD,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAA;IACjE,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;IAE/D,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;IAErB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,KAAK,EAAE,CAAA;IAErC,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAA;IAC1D,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;IACpD,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAA;IAC1D,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC,CAAA;IAC5E,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;IACtD,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;IACtD,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;IAEvD,EAAE,CAAC,MAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAA;IACtC,EAAE,CAAC,MAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAA;IAExC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IAEjB,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IAE9C,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAA;IAE1B,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC/B,IAAI,CAAC,QAAQ,GAAG;QACd,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QACvB,EAAE,CAAC,IAAK,EAAE,CAAA;QACV,SAAS,EAAE,CAAA;QACX,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IACrB,CAAC,CAAA;IACD,OAAO,EAAE,CAAA;AACX,CAAC;AAED,aAAa,CAAC,GAAG,GAAG,SAAS,CAAA;AAE7B,OAAO,EAAE,aAAa,EAAoB,CAAA"}