<template>
	<view>
		<view class="my-license flex-row mart-10">
			
			<view
				v-for="(item,index) in [1,2,3,4,5,6,7,8]" 
				:key="index" 
				class="my-license-item"
				:class="[{'my-license-current': index==currentIndex }]"
				@click="licenseClick(index)"
			>
				<view :class="[{'my-license-new': index==7&&!strArr[7]}]">
					{{strArr[index] || (index==7 ? "新能源" : "")}}
				</view>
			</view>
			
		</view>
		<!-- 车牌键盘 -->
		<keyword :isShow="keyState" @exit="exit" @inputchange="getKey" :keyBoardType="keyBoardType" :oinp="str" @ok="confirm"/>
	</view>
</template>

<script>
	import keyword from '../keyword/keyword.vue'
	
	export default {
		props: [
			"default",
		],
		components: {
			keyword
		},
		data() {
			return {
				keyState: false, // 车牌键盘-显示不显示
				keyBoardType: 1, // 1-省份简写键盘 2-中英文键盘
				str: "", // 输入的车牌号
				strArr: [" ", " ", " ", " ", " ", " ", " "], // 切割的车牌号数组
				currentIndex: null, // 当前开始输入的下标
				maxIndex: 6, //下标从0开始，6-普通车牌 7-新能源车牌
			};
		},
		watch:{
			default:{
				handler(value){
					if (value) {
					this.str = value;
					let temp = this.str.split("");
					this.strArr=[]
					for (let i = 0;i < value.length;i++) {
						this.strArr[i] = temp[i];
					}
					if (this.strArr.length==8) {
						this.maxIndex = 7;
					}
					this.keyBoardType = 2;
				}

				}

			}
		},
		created () {
			if (this.default) {
				this.str = this.default;
				let temp = this.str.split("");
				for (let i = 0;i < this.default.length;i++) {
					this.strArr[i] = temp[i];
				}
				this.keyBoardType = 2;

			}
			

			
		},
		watch:{
			default(){
				if (this.default) {
					this.str = this.default;
					let temp = this.str.split("");
					if(this.default.length < 7){
						return false
					}
					for (let i = 0;i < this.default.length;i++) {
						this.strArr[i] = temp[i];
					}
					this.keyBoardType = 2;
				}else{
					this.strArr = [" ", " ", " ", " ", " ", " ", " "]
				}
			}
		},
		methods: {
			// 点击不同位置的输入框
			licenseClick (index) {
				let len = this.str.length;
				// if (len == 0 || index == 0) {
				if (index == 0) {
					this.keyBoardType = 1;
				} else {
					this.keyBoardType = 2;
				}
				
				// if (index > len) {
				// 	this.currentIndex = len;
				// } else {
				// 	this.currentIndex = index;
				// }
				this.currentIndex = index;
				if (index == 7) {
					this.maxIndex = index;
				}
				
				this.keyState = true;
			},
			// 取消车牌键盘
			exit(){
				this.$emit('getLicense', this.str);
				this.keyState = false;
			},
			// 获取键盘的值
			getKey(val){

				if ((this.str.length > this.maxIndex+1) && val != "delete") {
				  return false
				}
				if(val == 'delete'){					
					// if (this.currentIndex == 0) { // 针对首位删除置为空
					// 	this.strArr.splice(this.currentIndex - 1, 1, " ");
					// } else {
					// 	this.strArr.splice(this.currentIndex, 1);
					// }
					// 当前位置无值则删除前一位，有值则置空
					if (this.strArr[this.currentIndex] == " " || this.strArr[this.currentIndex] == undefined || this.strArr[this.currentIndex] == "") {
						this.strArr.splice(this.currentIndex - 1, 1, " ");
						this.currentIndex = this.currentIndex - 1;
					} else {
						this.strArr.splice(this.currentIndex, 1, this.currentIndex === 7 ? "" : " ");
						this.currentIndex = this.currentIndex;
					}
					
				} else {
					this.strArr.splice(this.currentIndex, 1, val);
					this.currentIndex = this.currentIndex + 1;
				}
				
				if (this.currentIndex > this.maxIndex) {
					this.currentIndex = this.maxIndex;
				} else if (this.currentIndex < 0) {
					this.currentIndex = 0;
				}
				
				// console.log("this.strArr", this.strArr);
				this.str = this.strArr.join("").trim();
				// console.log("this.str", this.str);
				this.$emit('getLicense', this.str);
				
				if(this.str.length == 0 || this.currentIndex == 0) {
					this.keyBoardType = 1;
				} else {
					this.keyBoardType = 2;
				}
				
				// // 截取得到单个字符数组
				// this.strArr = this.str.split("");
			},
			// 车牌点击确认
			confirm(e){
				this.$emit('getLicense', this.str);
			    this.keyState = false;
			},
		}
	}
</script>
