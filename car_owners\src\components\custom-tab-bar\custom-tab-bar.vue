<template>
	<view>
		<view class="tabbar">
			<view class="tabbar_box_show"></view>
			<view class="navigator">
				<view class="navigator-item" v-for="(item, index) in tabBar.list" :key="item.pagePath"
					@click.self="switchTab(item, index)" data-type="tabbar">
					<template v-if="index === 0 || index === 2">
						<image class="icon" :src="currentIndex === index ? item.selectedIconPath : item.iconPath" data-type="tabbar">
						</image>
						<text :class="['item-text', { 'text-active': currentIndex === index }]" data-type="tabbar">{{ item.text }}</text>
					</template>
					<template v-else>
						<image src="/static/tabbar/scan.png" class="scanning_code" data-type="tabbar">
						</image>
					</template>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
import power from '../../common/utils/power.js';
import util from '../../common/utils/util';
export default {
	name: "custom-tab-bar",
	props: {
		currentIndex: { //当前显示的页面下标
			type: Number,
			default: 0
		}

	},
	data() {
		return {
			power,
			util,
			selectedIndex: 0, //当前跳转的tabbar页面
			tabBar: {
				list: [{
					"pagePath": "pages/index/index",
					"text": "首页",
					"iconPath": "/static/tabbar/discover.png",
					"selectedIconPath": "/static/tabbar/discover-selected.png"
				},
				{
					"pagePath": "pages/index/scan",
					"text": "",
					"iconPath": "/static/tabbar/scan.png",
					"selectedIconPath": "/static/tabbar/scan.png",
					"navigationStyle": "custom",
					"transparentTitle": "always",
					"titlePenetrate": "YES"
				},
				{
					"pagePath": "pages/user/index",
					"text": "我的",
					"iconPath": "/static/tabbar/member.png",
					"selectedIconPath": "/static/tabbar/member-selected.png",
					"navigationStyle": "custom",
					"transparentTitle": "always",
					"titlePenetrate": "YES"
				}
				]
			}, //自定义tabbar

		};
	},
	computed: {

	},
	methods: {
		// 调整后，1、调换入参顺序，方便传值，2、循环判断一下index，
		switchTab(item, index) {
			this.$set(this, 'selectedIndex', index)
			if (item.pagePath === 'pages/index/scan') {
				this.handleScanQr()
				//点击扫一扫 直接调用方法，无需跳转页面
				return
			}
			let url = '/' + item.pagePath
			uni.switchTab({
				url,

			})

		},

		// 打开扫码功能
		async handleScanQr() {
			const that = this
			// #ifdef APP-PLUS
			// 获取权限定位信息
			let resp = await power.requestAndroidPermission('android.permission.CAMERA');
			if (resp == -1) {
				return this.modify();
			}
			// #endif
			// #ifdef MP-WEIXIN || MP-ALIPAY  || APP-PLUS
			uni.scanCode({
				success: (res) => {
					console.log(res)
					util.dealQrcode(res.result || res).then(result => {
						console.log(result)
						if (result && result.code == 200) {
							console.log(result)
							that.getPileMessage()
						}
					}).catch(err => {
						uni.showToast({
							title: err,
							duration: 3000,
							icon: 'none'
						})
					})
				}
			})
			// #endif
			// #ifdef H5
			uni.chooseImage({
				sizeType: ['original'],
				count: 1,
				success(res) {
					uni.showLoading({
						title: '加载中......'
					})
					const tempFilePaths = res.tempFilePaths[0]
					qrcode.decode(tempFilePaths)
					qrcode.callback = code => {
						if (code == "error decoding QR Code") {
							uni.showToast({
								title: "无效的二维码，请重新上传!",
								duration: 2000,
								icon: 'none'
							})
							uni.hideLoading();
						} else {
							util.dealQrcode(code).then(result => {
								console.log(result)
								if (result && result.code == 200) {
									console.log(result)
									that.getPileMessage()
								}
							}).catch(err => {
								uni.showToast({
									title: err,
									duration: 3000,
									icon: 'none'
								})
								uni.hideLoading();
							})
						}
					}
				},
				fail: (err) => {

				}
			});
			// #endif
		},

		/* 获取充电桩信息 */
		getPileMessage() {
			util.getPileMessage(0, 2).then().catch(err => {
				if (err && err.code == 4002) {
					uni.removeStorageSync('cloudToken')
					this.getPileMessage()
				} else {
					uni.showToast({
						title: err,
						icon: 'none',
						duration: 3000
					});
				}
			})
		},

		// // 授权提示
		modify(type) {
			let title = "请打开位置权限，否则功能将无法正常使用";
			(type == 2) && (title = "请打开相机权限，否则功能将无法正常使用")
			uni.showModal({
				title: title,
				content: this.modify_content,
				confirmText: '前往开启',
				success: function (res) {
					if (res.confirm) {
						power.gotoAppPermissionSetting(); //动态修改权限
					}
				}
			});
		},


	}
}
</script>


<style scoped lang="less">
.tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	height: 100rpx;
	background: #FFFFFF;

}

.tabbar_box_show {
	width: 100%;
	height: 100rpx;
	position: absolute;
	top: -65rpx;
	background-image: url(http://image.bolink.club/Fqqe_yoCTpiETLAa3s25Y0sk7EXD);
	background-size: 100% 174rpx;
}

.navigator {
	display: flex;
	justify-content: space-around;
	width: 85%;
	margin: 0 auto;
	padding: 10rpx 0;
	position: relative;
	align-items: center;
}

.navigator-item {
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 50rpx;
	height: 100%;
}

.item-text {
	color: #999999;
	font-size: 20rpx;
	font-weight: 500;
	margin-top: 5rpx;
}

.text-active {
	color: #049558 !important;
}

.icon {
	width: 48rpx;
	height: 48rpx;
}

.scanning_code {
	position: absolute;
	top: 35rpx;
	transform: translateY(-50%);
	width: 140rpx;
	height: 140rpx;
	border-radius: 50%;

}
</style>
