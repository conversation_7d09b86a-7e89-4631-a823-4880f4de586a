<template>
	<view 
		class="popup-wrap" 
		:class="[getWrapClass]">
		<!-- 背景 -->
		<view 
			class="popup-bg"
			:style="{'background': background}"
			@tap.stop="close"></view>
		<!-- 内容 -->
		<view 
			class="popup-content"
			:style="[getContentStyle]">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	// 动画使用的class
	const ANIMATE_TYPE_CLASS = {
		top: {
			show: 'top-show',
			hide: 'top-hide',
		},
		bottom: {
			show: 'bottom-show',
			hide: 'bottom-hide',
		},
		left: {
			show: 'left-show',
			hide: 'left-hide',
		},
		right: {
			show: 'right-show',
			hide: 'right-hide',
		},
		middle: {
			show: 'middle-show',
			hide: 'middle-hide',
		}
	}
	export default {
		// 组件参数
		props: {
			isShow: {
				type: Boolean,
				default: false
			},

			// 类型 left => 左侧出现 right => 右侧出现 bottom => 底部出现 top => 顶部出现 默认底部出现
			animateType: {
				type: String,
				default: 'bottom'
			},

			// 内容宽度
			width: {
				type: String,
				default: '100%'
			},

			// 背景颜色
			background: {
				type: String,
				default: 'rgba(0, 0, 0, .4)'
			},

			// 内容出现的过渡时间
			duration: {
				type: String,
				default: '.5s'
			}
		},

		computed: {
			getContentStyle() {
				let { width, duration } = this
				let style = {}
				style.width = width
				style.transitionDuration = duration
				return style
			},

			getWrapClass() {
				let { isShow, animateType } = this
				let className = ''
				if (isShow) {
					className = 'active-show ' + ANIMATE_TYPE_CLASS[animateType].show
				} else {
					className = 'active-hide ' + ANIMATE_TYPE_CLASS[animateType].hide
				}
				return className
			}
		},

		data() {
			return {
			}
		},

		methods: {
			close() {
				if (!this.isShow) return
				this.$emit('close')
			},
		}
	}
</script>

<style lang="scss" scoped>
	.popup-wrap{
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		position: fixed;
		top: 0;
		left: 0;
		opacity: 0;
		z-index: -999;
		transition: top 0s ease 0s;

		&.active-show{
			z-index: 999;
			opacity: 1;
			transition: all 0s ease 0s;
			.popup-bg{
				opacity: 1;
			}
		}

		&.active-hide{
			transition: all .35s ease .1s;
            opacity: 0;
			.popup-bg{
				opacity: 0;
			}
		}

		&.top-show{
			.popup-content{
				top: 0;
				left: 0;
			}
		}

		&.top-hide{
            .popup-content{
				top: -100%;
				left: 0;
            }
		}
		
		&.bottom-show{
			.popup-content{
				bottom: 0;
				left: 0;
			}
		}

		&.bottom-hide{
            .popup-content{
				bottom: -100%;
				left: 0;
            }
		}

		&.left-show{
			.popup-content{
				left: 0;
				top: 0;
			}
		}

		&.left-hide{
            .popup-content{
				left: -100%;
				top: 0;
            }
		}
		
		&.right-show{
			.popup-content{
				right: 0;
				top: 0;
			}
		}

		&.right-hide{
            .popup-content{
				right: -100%;
				top: 0;
            }
		}

		&.middle-show{
			.popup-content{
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%) scale(1);
				opacity: 1;
			}
		}
		&.middle-hide{
			.popup-content{
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%) scale(1.15);
				opacity: 0;
			}
		}
		
		
		.popup-bg{
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, .4);
			position: fixed;
			z-index: 99;
			top: 0;
			left: 0;
			opacity: 0;
			transition: .35s opacity;
		}

		.popup-content{
			height: auto;
			overflow: hidden;
			position: fixed;
			z-index: 9999;
			transition-duration: .5s;
			transition-property: all;
		}
	}
</style>
