<view class='demo-home-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <view class='demo-home'>
    <view class='demo-home__title'>
      <image mode='aspectFit' class='demo-home__image' src='https://img.yzcdn.cn/vant/logo.png'>
      </image>
      <view class='demo-home__text'>
        Vant Aliapp
      </view>
    </view>
    <view class='demo-home__desc'>
      轻量、可靠的小程序 UI 组件库
    </view>
    <view a:for='{{ list }}' a:for-item='group' a:key='{{index}}' ref-numbers='{{ list }}'>
      <demo-home-nav group='{{ group }}' ref='saveChildRef1'>
      </demo-home-nav>
    </view>
  </view>
</view>