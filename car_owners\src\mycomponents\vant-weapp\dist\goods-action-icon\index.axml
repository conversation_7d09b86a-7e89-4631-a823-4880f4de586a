<view class='goods-action-icon-index {{className}}' style='{{style}}' ref='saveChildRef0'>
  <van-button square=" " id='{{ id }}' size='large' lang='{{ lang }}' loading='{{ loading }}' disabled='{{ disabled }}' open-type='{{ openType }}' business-id='{{ businessId }}' custom-class='van-goods-action-icon' session-from='{{ sessionFrom }}' app-parameter='{{ appParameter }}' send-message-img='{{ sendMessageImg }}' send-message-path='{{ sendMessagePath }}' show-message-card='{{ showMessageCard }}' send-message-title='{{ sendMessageTitle }}' onClick='onClick' onError='bindError' onContact='bindContact' onOpensetting='bindOpenSetting' onGetuserinfo='bindGetUserInfo' onGetphonenumber='bindGetPhoneNumber' onLaunchapp='bindLaunchApp' ref='saveChildRef1'>
    <van-icon a:if='{{ icon }}' name='{{ icon }}' dot='{{ dot }}' info='{{ info }}' class='van-goods-action-icon__icon' custom-class='{{iconClass}}' ref='saveChildRef2'>
    </van-icon>
    <slot a:else  name='icon'>
    </slot>
    <text class='{{textClass}}'>
      {{ text }}    </text>
  </van-button>
</view>