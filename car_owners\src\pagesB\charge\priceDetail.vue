<template>
	<view class="priceDetail">
		<!-- #ifdef MP-WEIXIN || APP-PLUS -->
		<view class="custom-back-nav"
			:style="{ top: statusBarHeight + 'px', height: navHeight + 'px', 'line-height': navHeight + 'px' }">
			<view class="flex-row alc">
				<van-icon @click="goBack" name="arrow-left" color="#ffff" size="36rpx" />
				<view @click="goBack" style="margin-left: 4rpx;color: #ffff;">价格详情</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<custom-nav-bar title="" color="#ffff"></custom-nav-bar>
		<!-- #endif -->
		<view class="header">
			<view class="title">
				<view class="interval">时段</view>
				<view class="calculation">
					<view class="text top">
						<view class="all title">价格</view>
						<view class="all">电价</view>
						<view class="all">服务费</view>
					</view>
					<view class="text bottom">
						<view class="all">(元/度)</view>
						<view class="all">(元/度)</view>
						<view class="all">(元/度)</view>
					</view>
				</view>
			</view>
		</view>
		<view class="content">
			<view v-for="(item, index) in timesList"
				:class="{ 'activity': item.activityCfee&&item.active, 'active': item.active, 'item': true, 'no-activity-item': !item.activityCfee }">
				<!-- 没活动 -->
				<view style="display:flex" v-if="!item.activityCfee">
					<view class="time-type" :style="{ background: item.color }">{{ item.title }}</view>
					<view class="interval">{{ item.btime + '-' + item.etime }}</view>
					<view class="list">
						<view class="sum" style="margin:0;font-size:36rpx">
							{{ (Number(item.sfee) + Number(item.cfee)).toFixed(4) }}
						</view>
						<view class="all">{{ Number(item.cfee).toFixed(4) }}</view>
						<view class="all">{{ Number(item.sfee).toFixed(4) }}</view>
					</view>
				</view>
				<!-- 有活动 -->
				<view style="display:flex" v-if="item.activityCfee">
					<view class="time-type" :style="{ background: item.color }">{{ item.title }}</view>
					<view class="interval">{{ item.btime + '-' + item.etime }}</view>
					<view class="list">
						<view class="sum">
							<view class="price-type">活动价</view>
							<view class="font28" style="font-size:36rpx">{{ (Number(item.activityCfee) + Number(item.activitySfee)).toFixed(4) }}</view>
						</view>
						<view class="all price">{{ Number(item.activityCfee).toFixed(4) }}</view>
						<view class="all price">{{ Number(item.activitySfee).toFixed(4) }}</view>
					</view>
				</view>

				<view class="container" v-if="item.activityCfee">
					<view class="list">
						<view class="sum original">
							<view class="price-type grey">原价</view>
							<view class="font28 grey">{{ (Number(item.sfee) + Number(item.cfee)).toFixed(4) }}</view>
						</view>
						<view class="all grey price" style="padding-left: 29rpx;">{{ Number(item.cfee).toFixed(4) }}</view>
						<view class="all grey price">{{ Number(item.sfee).toFixed(4) }}</view>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
import apis from "../../common/apis/index";
const app = getApp();

export default {
	data() {
		return {
			windowHeight: 0,
			statusBarHeight: 0,
			navHeight: 0,
			timesList: [{ billType: 2, btime: "00:00", cfee: "0.10", etime: "24:00", sfee: "0.05" }],
		}
	},
	onLoad(options) {
		console.log('options-->', options);
		getApp().getSystemInfo().then(res => {
			this.windowHeight = res.windowHeight;
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight;
		});
		if ('timesList' in options) {
			let timesList = JSON.parse(options.timesList)
			this.timesList = timesList
		}
		getApp().login().then(res => {

		})
	},
	onShow() {
		// 判断是否有token和session_key
		getApp().isloginSuccess();
	},
	methods: {
		goBack() {
			uni.navigateBack({
				delta: 1,
			});
		},
	}
}
</script>

<style lang="less">
.priceDetail {
	width: 100%;
}

.content {
	padding: 30rpx 24rpx;
	margin-top: 77rpx;
	// #ifdef H5
	margin-top: 8rpx;
	//#endif
	.active {
		background-image: url('http://image.bolink.club/Fr6EOuPj7Hkz3ZREuXwbHWvGvZMy');
		background-size: 100% 100%;
		background-position: center center;
		background-repeat: no-repeat;

	}

	.activity {
		background-image: url('http://image.bolink.club/FrNXuN4I21Vd8P52j1a4U4NvWEMP');
	}

	.item {
		font-size: 28rpx;
		font-weight: bold;
		color: #242E3E;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		background-color: #FFFFFF;
		box-shadow: 0px 4px 26px 0px rgba(0, 0, 0, 0.02);
		height: 220rpx;
		padding-top: 20rpx;

		.container {
			display: flex;
			margin: -20rpx 0 0 282rpx;
		}

		.price-type {
			height: 34rpx;
			font-size: 24rpx;
		}

		.font28 {
			font-size: 28rpx;
		}

		.time-type {
			width: 40rpx;
			height: 40rpx;
			background: #FF9812;
			border-radius: 26rpx;
			font-size: 24rpx;
			font-weight: 800;
			color: #FFFFFF;
			line-height: 40rpx;
			text-align: center;
			margin: 30rpx 0 0 1%;
		}

		.interval {
			width: 30%;
			height: 100rpx;
			line-height: 100rpx;
			text-align: center;
			font-size: 28rpx;
		}

		.all {
			width: 31%;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
			// #ifdef MP-ALIPAY
			width: 31%;
			//#endif
		}

		.list {
			flex: 1;
			display: flex;
			height: 100rpx;
			line-height: 100rpx;
			justify-content: space-between;
			padding-right: 40rpx;
			// #ifdef MP-ALIPAY
			padding-right: 0;
			//#endif
		}

		.sum {
			font-size: 24rpx;
			font-weight: bold;
			color: #242E3E;
			margin-top: -20rpx;
		}
	  .original{

				margin-left:-20rpx;

		}
	}

	.no-activity-item {
		height: 120rpx;

	}
}

.grey {
	color: #8894AA
}

.header {
	width: 100%;
	background-color: #376cee;
	height: 120px;
	padding-top: 100px;
	// #ifdef H5
	padding-top: 60px;
	//#endif
	// border-radius: 0 0 50rpx 50rpx;
	.title {
		display: flex;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
		background-color: #FFFFFF;
		height: 120rpx;

		.interval {
			width: 39%;
			height: 100rpx;
			font-size: 28rpx;
			line-height: 120rpx;
			font-weight: 800;
			color: #242E3E;
			text-align: center;
		}

		.calculation {
			display: flex;
			flex: 1;
			padding-right: 7rpx;
			flex-direction: column;
			height: 120rpx;

			.title {
				font-size: 32rpx;
				font-weight: 600;
			}

			.text {
				flex: 1;
				justify-content: space-between;
				display: flex;
			}

			.top {
				line-height: 50px;
				font-size: 28rpx;
				font-weight: 500;
				color: #242E3E;
			}

			.all {
				width: 37%;
				// #ifdef MP-ALIPAY
				width: 30%;
				//#endif

			}

			.bottom {
				font-size: 26rpx;
				font-weight: 400;
				color: #828EA6;
				margin-top: -53rpx;
				// #ifdef MP-ALIPAY
				margin-top: 0;
				//#endif
				.all{
					margin-left: -10rpx;
				}
			}
		}
	}
}
.price{
	line-height: 125rpx;
}
</style>
