<template>
	<view class="disclose">
		<view
			class="custom-back-nav"
			:style="{top: statusBarHeight+'px', height: navHeight+'px', 'line-height': navHeight+'px'}"
		>
			<view class="flex-row alc">
				<!-- #ifdef MP-WEIXIN -->
				<van-icon @click="goBack" name="arrow-left" color="#fff" size="36rpx" />
				<!-- #endif -->
				<!-- #ifdef MP-ALIPAY -->
				<van-icon @click="goBack" style="width: 18px;" color="#fff" size="36rpx" />
				<!-- #endif -->
				<view @click="goBack" style="margin-left: 4rpx;">分享墙</view>
			</view>
		</view>
		
		<view class="dis-par" style="min-height: 270px;">
			<lff-barrage ref="lffBarrage" :maxTop="240" height="270px"></lff-barrage>
			<view class="dis-title">分享 <span style="color: #999;font-size: 24rpx;">（{{distotal}}）</span></view>
		</view>
		
		<view style="height: 20px;background-color: #fff;"></view>
		
		<template v-if="newList.length>0">
			<scroll-view :scroll-y="true" @scrolltolower="scrolltolower" :style="{height: (windowHeight-290-55)+'px'}">
				<view class="msg-content flex-col flex-1">
					
					<view v-for="(item,index) in topList" :key="index" class="flex-col msg-item">
						<view class="flex-row alc">
							<view class="item-ava">
								<image :src="item.headPhoto"></image>
							</view>
							<view class="flex-col marl-20 fg1">
								<view>
									<span class="of-clamp1">{{item.nickName}}</span>
								</view>
								<!-- <view class="item-time">{{form.timestampToTime(item.ctime)}}</view> -->
							</view>
						</view>
						<view class="item-content" @click="clickConsult">
							<div>{{item.content}}</div>
							<div style="color: #2585FF;">400-1989098</div>
						</view>
					</view>
					
					<view v-for="(item,index) in newList" :key="index" class="flex-col msg-item">
						<view @click="checkOnce(item.parentInfo)" style="margin-bottom: 10px;">
							<view class="flex-row alc">
								<view class="item-ava">
									<image :src="item.parentInfo.headPhoto"></image>
								</view>
								<view class="flex-col marl-20 fg1">
									<view>
										<span class="of-clamp1">{{item.parentInfo.mobile | encryptionMobile}}</span>
									</view>
									<view class="item-time">{{form.timestampToTime(item.parentInfo.ctime)}}</view>
								</view>
								<view class="flex-row alc jue heart-img">
									<image @click.stop="praiseClick(item.parentInfo, index)" v-if="item.parentInfo.praise==1" src="https://image.bolink.club/yima/heartBeat.png"></image>
									<image @click.stop="praiseClick(item.parentInfo, index)" v-else src="https://image.bolink.club/yima/heart.png"></image>
									<view class="marl-10">{{item.parentInfo.praiseNum || 0}}</view>
								</view>
							</view>
							<view class="item-content">
								<span>{{item.parentInfo.content}}</span>
							</view>
						</view>
						<view v-for="(citem,cindex) in item.parentInfo.child" :key="cindex" class="flex-col msg-item item-content">
							<view @click="checkOnce(item.parentInfo, citem)">
								<view class="flex-row alc">
									<view class="item-ava" style="width: 50rpx;height: 50rpx;">
										<image :src="citem.headPhoto" style="width: 50rpx;height: 50rpx;"></image>
									</view>
									<view class="flex-col marl-20 fg1">
										<view>
											<span class="of-clamp1">{{citem.mobile | encryptionMobile}}</span>
										</view>
										<view class="item-time">{{form.timestampToTime(citem.ctime)}}</view>
									</view>
									<view class="flex-row alc jue heart-img">
										<image @click.stop="praiseClick(item.parentInfo, index,citem, cindex)" v-if="citem.praise==1" src="https://image.bolink.club/yima/heartBeat.png"></image>
										<image @click.stop="praiseClick(item.parentInfo, index,citem, cindex)" v-else src="https://image.bolink.club/yima/heart.png"></image>
										<view class="marl-10">{{citem.praiseNum || 0}}</view>
									</view>
								</view>
								<view class="item-content" style="margin-left: 70rpx;">
									<span>回复{{citem.byReplyMobile | encryptionMobile}}: {{citem.content}}</span>
								</view>
							</view>
						</view>
						<view class="getMore" v-if="item.subInfos.length > 1 && item.parentInfo.showMore || (index === 0 && item.parentInfo.showMore0)" @click="loadMore(item.parentInfo,index)">展开更多评论 V</view>
					</view>
					<view class="list-end">{{isEnd ? '亲，到底了' : '加载中...'}}</view>
				</view>
			</scroll-view>
		</template>
		<empty v-else />
		<view style="height: 100rpx;opacity: 0;">&nbsp;</view>
		<view class="bottom-input">
			<view class="flex-row alc pad-20">
				<view class="dis-input flex-row alc flex-1">
					<view class="open-ava">
							<image :src="avatarUrl"></image>
					</view>
					<input type="text" :focus='inputFocus' @blur="inputFocus = false" :maxlength="maxlength" :value="msg" @input="inputChange" :cursor-spacing="12" :placeholder="placeholder" />
				</view>
				<view class="marr-10" style="color: #999;width: 100rpx;">{{msg.length}}/{{maxlength}}</view>
				<!-- #ifdef MP-ALIPAY -->
				<van-button custom-class="sub-btn" color="linear-gradient(100deg,#4fa4ff 0%, #0e78ff 100%)" :loading="loading" size="small" onClick="disAdd">发送</van-button>
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				<van-button custom-class="sub-btn" color="linear-gradient(100deg,#4fa4ff 0%, #0e78ff 100%)" :loading="loading" size="small" @click="disAdd">发送</van-button>
				<!-- #endif -->  
			</view>
		</view>
		
		<!-- 点击客服上拉菜单 -->
		<!-- #ifdef MP-ALIPAY -->
		<van-action-sheet
		  className="custom"
		  class="custom"
		  :show="actionsShow"
		  :actions="actions"
		  cancel-text="取消"
		  :round="false"
		  onClick-overlay="clickConsultCancel"
		  onCancel="clickConsultCancel"
		  onSelect="clickConsultSelect"
		/>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<van-action-sheet
		  className="custom"
		  class="custom"
		  :show="actionsShow"
		  :actions="actions"
		  cancel-text="取消"
		  :round="false"
		  @click-overlay="clickConsultCancel"
		  @cancel="clickConsultCancel"
		  @select="clickConsultSelect"
		/>
		<!-- #endif -->  
		
	</view>
</template>

<script>
import apis from "../../common/apis/index";
import form from '../../common/utils/form.js';
import lffBarrage from '../../components/lff-barrage/lff-barrage.vue';
import empty from '../../components/empty/empty.vue';
const app = getApp();

export default {
	components: { 
		lffBarrage,
		empty
	},
	filters:{
		encryptionMobile(mobile){
			if (!mobile) return '***********'
			var reg = /^(\d{3})\d{4}(\d{4})$/;
			return mobile.replace(reg, "$1****$2");
		}
	},
	data() {
		return {
			inputFocus:false,
			morePageNum:1,
			backSome:{},
			placeholder:'我也要说点什么...',
			avatarUrl:'',//用户头像
			actionsShow: false,
			actions:  [
			  { name: '400-1989098', type: 1 },
			],
			topList: [{
					content: "工作日 9:00-18:00，客服电话：",
					ctime: 1623686400,
					headPhoto: "https://image.bolink.club/yima/ava.jpg",
					id: 1,
					nickName: "一码App",
				}],
			form: form,
			msg: '',
			timer: '',
			loading: false,
			userInfo: app.globalData.userInfo, // 用户信息
			pageNum: 1,
			pageSize: 10,
			pageNumDis: 1,
			pageSizeDis: 4,
			distotal: 0,
			total: 0,
			maxlength: 30,
			disList: [],
			newList: [],
			isShift: false,
			isEnd: false, // 吐槽列表是否到最后一页
			windowHeight: null,
			statusBarHeight: null,
			navHeight: null,
			item:{},
			moreIndex:'',
		};
	},
	onLoad(option) {
		console.log(option);
		this.item = option
		getApp().getSystemInfo().then(res => {
			this.windowHeight = res.windowHeight;
			this.statusBarHeight = res.statusBarHeight;
			this.navHeight = res.navHeight;
		});
		this.initData();
		this.initDataDis();
		apis.homeApis.uploadPic().then(res=>{
			this.avatarUrl=res.data.avatarUrl
		})
		app.userInfo().then(res => {
			this.userInfo = app.globalData.userInfo;
		}).catch((err) => {
			console.log(err);
		})
		// #ifdef MP-ALIPAY
		this.$scope.disAdd = this.disAdd.bind(this)
		this.$scope.clickConsultCancel = this.clickConsultCancel.bind(this)
		this.$scope.clickConsultSelect = this.clickConsultSelect.bind(this)
		// #endif
	},
	onShow () {
		this.timer = setInterval(() => {
			
			if (this.pageNumDis > Math.ceil(this.total / this.pageSizeDis)) {
				this.pageNumDis = 1;
			}
			this.initDataDis();
			// this.initData(true);
		}, 15000)
	},
	onUnload () {
		clearInterval(this.timer);
	},
	methods: {
		checkOnce(item, citem=null){
			console.log(citem);
			var reg = /^(\d{3})\d{4}(\d{4})$/;
		  let mobile = citem ? citem.mobile : item.mobile
			if(mobile === uni.getStorageSync('mobile')){
				uni.showModal({
					title:'是否删除该评论',
					success:(res)=>{
						console.log(res);
						if(res.confirm){
							let params = {
								id: citem ? citem.id : item.id
							}
							apis.homeApis.forumDelete(params).then((ret) => {
								console.log(ret);
								this.initData()
							})
						}
					}
				})
			}else{
				this.inputFocus=true
				this.placeholder = '回复:' + (mobile ? mobile.replace(reg, "$1****$2") : '***')
				this.backSome = {
					pid:item.id,
					byReplyMobile:citem ? citem.mobile : item.mobile,
					byReplyNickOpenId:citem ? citem.openId : item.openId,
				}
			}
			console.log(this.backSome);
		},
		loadMore(item,index){
			this.morePageNum = this.moreIndex === index ? this.morePageNum + 1 : 1
			this.moreIndex = index
			let params = {
				pid: item.id,
				pageNum: this.morePageNum,
				pageSize: 5,
			}
			apis.homeApis.forumGetReplyForum(params).then((res) => {
				if(this.morePageNum==1){
					item.child = res.data.rows
				}else{
					item.child = item.child.concat(res.data.rows)
				}
				item.showMore = item.child.length !== res.data.total
				if(item.showMore0 && index === 0 && res.data.rows.length === 0){
					item.showMore0 = false
					console.log(item);
				}
				this.$forceUpdate()
			})
		},
		// 吐槽点赞
		praiseClick (item, index, citem=null, cindex=null) {
			let state =citem ? citem.praise : item.praise;
			let praiseNum =citem ? (citem.praiseNum || 0) : (item.praiseNum || 0);
			let praise = state == 1 ? 0 : 1;
			state == 1 ? --praiseNum : ++praiseNum;
			if (praiseNum<0) praiseNum = 0;
			
			this.$set(citem ? citem : item, 'praise', praise);
			this.$set(citem ? citem : item, 'praiseNum', praiseNum);
			let params = {
				commentId:citem ? citem.id : item.id,
				state: state,
			}
			apis.homeApis.praiseComment(params).then((res) => {
				
			})
		},
		clickConsult () {
			this.actionsShow = true;
		},
		clickConsultCancel () {
			this.actionsShow = false;
		},
		clickConsultSelect (e) {
			let name = e.detail.name;
			uni.makePhoneCall({
			    phoneNumber: name
			});
		},
		goBack () {
			uni.navigateBack({
				fail: (err)=> {
					uni.switchTab({
						url: '/pages/park/index'
					})
				}
			});
		},
		scrolltolower () {
			this.pageNum = this.pageNum + 1;
			// uni.showLoading({
			// 	title: "请稍候",
			// 	mask: true
			// })
			this.initData();
		},
		inputChange (e) {
			this.msg = e.detail.value.trim().substr(0, this.maxlength);
		},
		// 弹幕墙
		initDataDis () {
			let params = {
				pageNum: this.pageNumDis,
				pageSize: this.pageSizeDis,
			}
			apis.homeApis.forumFind(params).then((res) => {
				let disList = res.data.rows.map(item=>{
					return item.parentInfo
				})
				this.disList = this.disList.concat(disList);
				this.total = res.data.total;
				this.pageNumDis = this.pageNumDis + 1;
				if (!this.isShift) {
					this.listShift();
				}
			})
		},
		// 吐槽列表
		initData (flag) {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
			}
			this.$nextTick(function(){
				
			})
			apis.homeApis.forumFind(params).then((res) => {
				this.distotal = res.data.total;
				if (this.pageNum === 1) {
					this.newList = res.data.rows
				} else {
					this.newList = this.newList.concat(res.data.rows);
				}
				this.isEnd = this.newList.length ===this.distotal;
				if(this.item.pid){
					apis.homeApis.forumGetReplyLocation(this.item).then((res) => {
						console.log(res);
						let data = res.data
						data.parentInfo.child = [data.subForum]
						data.parentInfo.showMore0 = true
						this.newList=this.newList.filter(item=>{
							return item.parentInfo.id !==data.parentInfo.id
						})
						this.newList.unshift(data)
						console.log(this.newList);
					})
				}
				this.newList.forEach(item=>{
					item.parentInfo.showMore = true,
					item.parentInfo.child = item.subInfos.length > 0 ? [item.subInfos[0]] : []
				})
				this.pageNum = 1;
				this.moreIndex = ''
				console.log(this.newList);
			})
		},
		listShift () {
			this.isShift = true;
			let item = this.disList.shift();
			setTimeout(()=>{
				this.add(item);
				if (this.disList.length > 0) {
					this.listShift();
				} else {
					this.isShift = false;
				}
			}, 1000)
		},
		add(item) {
			if (item && item.content) {
				this.$refs.lffBarrage.add({ item: item.content, nickName: item.nickName, avatarUrl: item.headPhoto });
			}
		},
		disAdd () {
			if (!this.msg) {
				uni.showToast({
					title: "请先输入吐槽内容",
					icon: "none"
				});
				return false;
			}
			this.loading = true;
			console.log(1111);
			let params = this.backSome
			params.content = this.msg
			params.headPhoto = this.avatarUrl,
			apis.homeApis.forumAdd(params).then((res) => {
				this.loading = false;
				if (res.status === 200) {
					this.msg = '';
					this.backSome = {}
					this.placeholder = '我也要说点什么...'
					this.add(params);
					this.pageNum = 1;
					this.moreIndex = ''
					this.morePageNum = 1;
					this.initData(true);
					// this.newList.unshift({...params, ctime: new Date().getTime().toString().substr(0,10)});
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					});
				}
			}).catch((err) => {
				this.loading = false;
			});
			getApp().eventRecord({
				keyWord: '吐槽页面-点击吐槽按钮',
				clickType: 'Button',
				jumpType: '其他',
				jumpDesc: '发送吐槽',
				result: '成功',
				startTime: new Date().getTime(),
				endTime: new Date().getTime()
			});
		},
		// 获取用户信息的回调
		getUserProfile (e) {
			app.updataUserInfo().then(()=>{
				this.userInfo = app.globalData.userInfo;
			});
		},
	}
};
</script>

<style scoped>
.disclose {
	background-color: #fff;
	width: 100%;
}
.dis-par {
	background-color: #eee;
	box-sizing: border-box;
	position: relative;
}

.dis-title {
	color: #333;
	font-size: 30rpx;
	font-weight: 600;
	padding-bottom: 12rpx;
	position: absolute;
	bottom: -36rpx;
	left: 34rpx;
}
.msg-content {
	box-sizing: border-box;
	padding: 48rpx 34rpx 34rpx 34rpx;
}
.msg-item {
	margin-bottom: 30rpx;
	font-size: 28rpx;
	color: #666;
}
.item-time {
	font-size: 24rpx;
	color: #C7C7C7;
}
.getMore{
	margin-left: 170rpx;
	margin-top: 10rpx;
	word-break: break-all;
	color: #9c9c9c;
	font-size: 28rpx;
}
.item-content {
	margin-left: 100rpx;
	margin-top: 10rpx;
	word-break: break-all;
	color: #333333;
	font-size: 30rpx;
}

.item-ava {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	box-shadow: 0px 4rpx 4rpx 0px rgba(0,0,0,0.1); 
}
.item-ava image{
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}
.bottom-input {
	position: fixed;
	bottom: 20rpx;
	width: 100%;
	box-sizing: border-box;
	height: 118rpx;
	background-color: #fff;
}
.dis-input {
	width: 100%;
	height: 80rpx;
	padding: 10rpx 8rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 50px;
}
.open-ava {
	overflow: hidden;
	display: block;
	width: 110rpx;
	height: 84rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	box-shadow: 4rpx 2rpx 12rpx 0px rgba(42,96,202,0.21); 
}
.open-ava image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	box-shadow: 0px 0px 10rpx 0px rgba(131, 131, 131, 0.15);
}
.dis-input input{
	width: 100%;
}
</style>
<style>
	.sub-btn {
		width: 152rpx!important;
		height: 80rpx!important;
		border-radius: 50rpx!important;
		font-size: 32rpx!important;
		font-weight: 600!important;
		background: linear-gradient(109deg,#4fa4ff 0%, #0e78ff 100%);
		box-shadow: 0px 4rpx 10rpx 0px rgba(65,153,255,0.30); 
	}
	.custom button {
		display: block!important;
		text-align: center!important;
	}
</style>