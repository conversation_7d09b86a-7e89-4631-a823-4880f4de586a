
import http from '../utils/http.js'
import BaseConfig from '../config/index.config.js'
export default {

	// 获取云平台token
	getCloudToken: (e, type = true, mask = true, loading = false, method = 'GET', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl + 'wit/chargePile/getToken', e, type, mask, loading, method, isCompleteUrl),
	// 获取充电桩信息
	queryCode: (e, type = true, mask = true, loading = false, method = 'GET', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl + 'wit/chargePile/queryCode', e, type, mask, loading, method, isCompleteUrl), 
	// 获取充电枪信息
	queryConnectorInfo: (e, type = true, mask = true, loading = false, method = 'GET', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl + 'wit/chargePile/queryConnectorInfo', e, type, mask, loading, method, isCompleteUrl), 
	// 获取实时电流
	getRealTimeData: (e, type = true, mask = true, loading = false, method = 'GET', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl + 'wit/chargePileSocket/getRealTimeData', e, type, mask, loading, method, isCompleteUrl),
	// 开启充电
	startCharge: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePileSocket/startCharge', e, type, mask, loading, method,isCompleteUrl),
	// 充电订单查询
	queryorder: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryOrder', e, type, mask, loading, method,isCompleteUrl), 
  	// 充电历史订单查询
	queryOrderNotPage: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryOrderNotPage', e, type, mask, loading, method,isCompleteUrl),
	// 未支付订单
	getPaidOrder: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryUnliquidatedOrder', e, type, mask, loading, method,isCompleteUrl),
	// 预约订单
	getAppointmentOrder: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryCameraReserve', e, type, mask, loading, method,isCompleteUrl),
	// 支付待支付订单
	toPayOrder: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePileSocket/arrearsPaymentOnline', e, type, mask, loading, method,isCompleteUrl),
	// 结束充电
	endCharge: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
	    BaseConfig.cloudUrl+'wit/chargePileSocket/endCharge', e, type, mask, loading, method,isCompleteUrl), 
	// 减免信息查询
	reductionMsg: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeReduction/isSupportReduction', e, type, mask, loading, method,isCompleteUrl), 
	// 下发优惠券
	sendDownReductionMes: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeReduction/sendDownReductionMes', e, type, mask, loading, method,isCompleteUrl),
	// 下发优惠券
	queryReductionByChargeOrderId: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeReduction/queryReductionByChargeOrderId', e, type, mask, loading, method,isCompleteUrl), 
	// 获取设备列表（充电）
	getEquipList: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryByPowerStationId', e, type, mask, loading, method,isCompleteUrl), 
	// 指令是否下发成功（充电桩）
	getOrderResult: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/deviceOnlineManage/getOrderResult', e, type, mask, loading, method,isCompleteUrl), 
	// 获取枪列表
	getConnector: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryConnectorByPowerId', e, type, mask, loading, method,isCompleteUrl),
	// 设备运维接口
	// 设备信息
	getDeviceInfo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeManager/queryDeviceInfo', e, type, mask, loading, method,isCompleteUrl), 
	// 查询参数
	getDevparameterSetting: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeManager/queryInfo', e, type, mask, loading, method,isCompleteUrl),
	// 保存参数
	saveDevparameterSetting: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeManager/parameterSetting', e, type, mask, loading, method, isCompleteUrl), 
	// 设备操作
	controlDev: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeManager/commandControl', e, type, mask, loading, method,isCompleteUrl),
	// 开闸|开闸充电
	toChargeLever: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePileSocket/chargeLever', e, type, mask, loading, method, isCompleteUrl), 
	// 重新开闸|开闸充电
	toReChargeLever: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePileSocket/repeatChargeLever', e, type, mask, loading, method, isCompleteUrl), 
	// 获取车位锁状态
	getChargePileLock: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryChargePileLocking', e, type, mask, loading, method, isCompleteUrl,contentType), 
	// 查询抬杆结果
	getLiftRodStatus: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargePileLocking/queryLiftRodStatus', e, type, mask, loading, method, isCompleteUrl,contentType), 
	// getStickTheGunStatus: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
	// 	BaseConfig.cloudUrl+'wit/chargePileSocket/queryStickTheGunStatus', e, type, mask, loading, method, isCompleteUrl,contentType), 
	// 两轮车
	// 常用桩
	getCommonlyUsedPile: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryCommonlyUsedPile', e, type, mask, loading, method,isCompleteUrl), 
	// 获取桩插口信息
	getJack: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/connector/queryConnectorByPile', e, type, mask, loading, method,isCompleteUrl), 
	// 获取附近充电桩
	getNearStation: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryPowerStationList', e, type, mask, loading, method,isCompleteUrl),
	// 获取计费规则
	getFeesStandard: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/billSet/queryByChargeCode', e, type, mask, loading, method,isCompleteUrl),
	// 获取车位信息数据
	getParkData: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/queryParkingByPowerStationId', e, type, mask, loading, method,isCompleteUrl,contentType),
	// 获取预约费计费规则
	getWaitBillSet: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/billSet/query', e, type, mask, loading, method,isCompleteUrl,contentType),
	// 支付车位预约费用
	// getWaitParkingPay: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
	// 	BaseConfig.cloudUrl+'wit/parkingReserve/reserveOnline', e, type, mask, loading, method,isCompleteUrl),
	getWaitParkingPay: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/reserve', e, type, mask, loading, method,isCompleteUrl),
	// 支付车位预约费用
	cancelWaitParkingOrder: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/updateReserveOrderStatus', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取待支付预约订单
	getWaitPayParkingOrder: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/cameraReserve/queryToBePaidOrder', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取待支付预约订单
	rePayWaitParkingOrder: (e, type = false, mask = true, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/toBePaidReserveOrderOnline', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取预约订单信息
	getWaitParkingOrder: (e, type = false, mask = true, loading = false, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/cameraReserve/queryCameraReserveById', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 获取历史功率
	getPowerByOpenId: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeOrder/queryPowerByOpenId', e, type, mask, loading, method,isCompleteUrl), 
	// 确认车牌开始充电
	confirmTheLicensePlate: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/confirmTheLicensePlateToStartCharging', e, type, mask, loading, method,isCompleteUrl),
	// 处理异常订单接口
	// updateCarNumberById: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(BaseConfig.cloudUrl+'wit/abnormalOrderManager/updateCarNumberById', e, type, mask, loading, method,isCompleteUrl),
	// 查询用户选择车牌
	queryCarNumber: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/parkingReserve/queryCarNumber', e, type, mask, loading, method,isCompleteUrl), 
	// 查询占位信息
	queryCarPlaceholderList: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/carPlaceholder/queryCarPlaceholderList', e, type, mask, loading, method,isCompleteUrl),
	// 用户提交申述
	abnormalOrderManagerAdd: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/abnormalOrderManager/add', e, type, mask, loading, method,isCompleteUrl),
	queryByStationId: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/billSet/queryByStationId', e, type, mask, loading, method,isCompleteUrl),
	// 获取单车黑白名单
	queryRoleLimit: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserManager/queryRoleLimit', e, type, mask, loading, method,isCompleteUrl), 	
	// 获取单车计费规则
	queryAllBilling: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
			BaseConfig.cloudUrl+'wit/billSet/queryAllBilling', e, type, mask, loading, method,isCompleteUrl,contentType), 	
	// 获取用户购买单车包月套餐
	queryBicycleSetMeal: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserMonthlyRecord/selectByParam', e, type, mask, loading, method,isCompleteUrl),
	// 用户购买包月套餐
	payBicycleSetMeal: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserMonthlyRecord/addOnline', e, type, mask, loading, method,isCompleteUrl), 
	// 获取用户已购买套餐
	getUserMonthlyRecord: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserMonthlyRecord/selectByParam', e, type, mask, loading, method,isCompleteUrl), 
	// 重新支付待支付套餐
	rePayBicycleMonthCard: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserMonthlyRecord/payComboOnline', e, type, mask, loading, method,isCompleteUrl,contentType),
	// 抬杆占位计费提醒
	getChargeLeverAlert: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePileSocket/chargeLeverAlert', e, type, mask, loading, method,isCompleteUrl), 

	// h5登录
	//发送验证码
	sendCode: (e, type = false, mask = false, loading = true, method = 'POST',isCompleteUrl = true, contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/h5SendCode', e, type, mask, loading, method,isCompleteUrl, contentType),
	// 登录
	h5Login: (e, type = false, mask = false, loading = true, method = 'POST',isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/h5Login', e, type, mask, loading, method,isCompleteUrl,contentType),
	// 钱包查询接口
	queryWallet: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/walletQueryWallet', e, type, mask, loading,method,isCompleteUrl),
	// 订单查询接口
	queryWalletOrder: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/orderQueryOrder', e, type, mask, loading,method,isCompleteUrl), 
	// 充值下单接口
	toRecharge: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/fundRecharge', e, type, mask, loading,method,isCompleteUrl),
	// 查询钱包订单明细接口
	queryWalletOrdersDetails: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/orderQueryOrders', e, type, mask, loading,method,isCompleteUrl), 
	// 钱包退款接口
	toWalletRefund: (e, type = true, mask = false, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/walletRefund', e, type, mask, loading,method,isCompleteUrl), 
	// 城市信息
	getcitytree: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/carenumGetcitytree', e, type, mask, loading,method,isCompleteUrl), 
	// 根据经纬获取城市名称
	getcode: (e, type = true, mask = false, loading = true, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/informationGetcode', e, type, mask, loading,method,isCompleteUrl), 
	// 限桩充电查询并更新用户充电等待时间
	waiting: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/orderlyPileLimit/enterWaiting', e, type, mask, loading, method,isCompleteUrl), 
	// 结束等待
	endWaiting: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/orderlyPileLimit/endWaiting', e, type, mask, loading, method,isCompleteUrl), 
	// 取消支付单车包月订单
	userMonthlyRecordCancel: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserMonthlyRecord/cancel', e, type, mask, loading, method,isCompleteUrl), 


	// 查询实体卡详情
	getCardDetail: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/getCardDetail', e, type, mask, loading, method,isCompleteUrl), 
	// 实体卡充值配置
	querySetting: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/querySetting', e, type, mask, loading, method,isCompleteUrl), 
	//实体卡消费，充值明细
	queryLastThreeMonthsRecord: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/queryLastThreeMonthsRecord', e, type, mask, loading, method,isCompleteUrl), 
	//实体卡充值
	entityCardManageCharge: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/charge', e, type, mask, loading, method,isCompleteUrl), 
	//查询实体卡列表
	queryCardDetailsByMobile: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/queryCardDetailsByMobile', e, type, mask, loading, method,isCompleteUrl),
	//查询充值优惠信息
	queryRechargeActive: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardChargeSettingTb/queryByPowerStationId', e, type, mask, loading, method,isCompleteUrl), 
	// 通过手机号和充电站查询人员
	chargeUserManager: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserManager/selectByPhone', e, type, mask, loading, method,isCompleteUrl), 
	// 扫码获取附近电站
	queryNameByChargeCode: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryNameByChargeCode', e, type, mask, loading, method,isCompleteUrl), 
	// 添加人员信息，单车个人中心
	chargeUserManagerAdd: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
			BaseConfig.cloudUrl+'wit/chargeUserManager/add', e, type, mask, loading, method,isCompleteUrl), 
	// 更新人员信息，单车个人中心
	chargeUserManagerUpdate: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
				BaseConfig.cloudUrl+'wit/chargeUserManager/update', e, type, mask, loading, method,isCompleteUrl), 
	// 通过用户手机号查询所有站点
	queryByPhone: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryByPhone', e, type, mask, loading, method,isCompleteUrl), 
	// 通过用户id查询人员有效期和有效次数
	selectByUserId: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/userAreaEffective/selectByUserId', e, type, mask, loading, method,isCompleteUrl), 
	// 购买单车停车包月
	bikeParkingOrder: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeParkingOrder/add', e, type, mask, loading, method,isCompleteUrl),
	// 根据充电站和车辆类型和计费类型查询单车计费
	selectByPowerStationId: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeBilling/selectByPowerStationId', e, type, mask, loading, method,isCompleteUrl),
	// 根据充电站id查询单车停车配置信息
	ParkingConfigInfo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/selectParkConfig', e, type, mask, loading, method,isCompleteUrl),	
	// 单车用户查询门禁设备
	selectDeviceByUserId: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/userAreaEffective/selectDeviceByUserId', e, type, mask, loading, method,isCompleteUrl), 
	// 单车用户出入记录进出分页查询
	queryByUser: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeAccessRecord/queryByUser', e, type, mask, loading, method,isCompleteUrl),		
	// 车辆添加
	bikeInfoTbAdd: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeInfo/add', e, type, mask, loading, method,isCompleteUrl),
	// 车辆修改
	bikeInfoTbUpdate: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeInfo/update', e, type, mask, loading, method,isCompleteUrl),
	// 车辆类型查询
	getType: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeType/getType', e, type, mask, loading, method,isCompleteUrl), 
	// 查询人员所有车辆
	queryByPage: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
			BaseConfig.cloudUrl+'wit/bikeInfo/queryByPage', e, type, mask, loading, method,isCompleteUrl),
	// 查询车辆信息
	bikeInfoQueryById: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeInfo/queryById', e, type, mask, loading, method,isCompleteUrl), 
	// 删除车辆
	bikeInfoDelete: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeInfo/deleteById', e, type, mask, loading, method,isCompleteUrl), 
	// 通过设备序列号判断空白码是单车还是智慧社区的
	queryByDeviceSn: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/blankQrcode/queryByDeviceSn', e, type, mask, loading, method,isCompleteUrl),
	// 判断用户需不需要登录
	queryBikeUser: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserManager/queryBikeUser', e, type, mask, loading, method,isCompleteUrl),
	// 通过桩编码查询充电站
	queryByChargeCode: (e, type = true, mask = true, loading = false, method = 'POST', isCompleteUrl = true,contentType="application/x-www-form-urlencoded") => http.request(
		BaseConfig.cloudUrl+'wit/chargePile/queryNameByChargeCode', e, type, mask, loading, method, isCompleteUrl,contentType), 
	// 待开票
	unInvoiced: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/invoicingInfo/unInvoiced', e, type, mask, loading, method,isCompleteUrl),
	// 开票历史
	invoicingInfoQuery: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/invoicingInfo/query', e, type, mask, loading, method,isCompleteUrl),
	// 开票详情
	invoicingInfoQueryById: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/invoicingInfo/queryById', e, type, mask, loading, method,isCompleteUrl),
	 // 开票
	invoicingInfoAdd: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/invoicingInfo/add', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站购买的卡（电子卡）
	getElectronicsCard: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/queryCardByStation', e, type, mask, loading, method,isCompleteUrl),
	// 查询两轮车支付配置
	getStationPayConfig: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStationPayConfig/select', e, type, mask, loading, method,isCompleteUrl),
	// 单车临时套餐远程开门
	toDeviceOperate: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/faceDeviceBikeInfo/toDeviceOperate', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站信息（收藏，是否支持开发票）
	queryStationDetails: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryStationDetails', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站信息（收藏，是否支持开发票）
	queryStationDetails: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryStationDetails', e, type, mask, loading, method,isCompleteUrl),
	// 收藏 | 不收藏电站
	collectionStation: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'/wit/powerStation/collectionStation', e, type, mask, loading, method,isCompleteUrl),
	// 获取枪列表
	getConnectorList: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'/wit/connector/getConnectorList', e, type, mask, loading, method,isCompleteUrl),
	//查询汽车实体卡、电子卡金额
	getAllFeeByMobile: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/getAllFeeByMobile', e, type, mask, loading, method,isCompleteUrl),
	//获取首页充电信息列表有活动金额
	queryCarBillingSet: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/billSet/queryCarBillingSet', e, type, mask, loading, method,isCompleteUrl),
	//查询汽车实体卡、电子卡列表
	queryCarCardDetailsByMobile: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/entityCardManage/queryCarCardDetailsByMobile', e, type, mask, loading, method,isCompleteUrl),
	//汽车自动生成用户
	autoGenerateCarUser: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeUserManager/autoGenerateCarUser', e, type, mask, loading, method,isCompleteUrl),
	// h5查车牌
	h5GetPlateNumber: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/getPlateNumber', e, type, mask, loading, method,isCompleteUrl),
	// 绑定车牌
	h5AddPlateCarNumber: (e, type = false, mask = false, loading = true, method = 'POST',isCompleteUrl = true,) => http.request(
		BaseConfig.cloudUrl+'wit/chargeH5Action/addPlateNumber', e, type, mask, loading, method,isCompleteUrl,),
	// 查询电站信息（收藏，是否支持开发票）
	queryStationDetails: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryStationDetails', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站信息
	getStationInfoById: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryStationById', e, type, mask, loading, method,isCompleteUrl),
	// 获取电站二维码
	getGunQrcode: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/connector/getConnectorSquareCode', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站经纬度，地址
	queryStationBaseInfo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/baseInfo', e, type, mask, loading, method,isCompleteUrl),
	// 查询电站经纬度，地址
	queryStationBaseInfo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/baseInfo', e, type, mask, loading, method,isCompleteUrl),
	// 查询订单冻结金额
	queryChargeWalletByOrderNo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit//userCurrency/queryChargeWalletByOrderNo', e, type, mask, loading, method,isCompleteUrl),
		
	// 预约
	// 获取车位状态（多个）
	getParkLotsStatus: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/parkingSpaceInfo/queryParkingSpaceStatusByBolinkId', e, type, mask, loading, method,isCompleteUrl),
	// 获取预约订单信息
	getWaitParkInfo: (e, type = true, mask = false, loading = false, method = 'GET') => http.request(
		BaseConfig.cloudUrl+'wit/cameraReserve/queryCameraReserve', e, type, mask, loading, method),
	// 预约订单扣款
	toBePaidReserveOrder: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'/wit/parkingReserve/toBePaidReserveOrder', e, type, mask, loading, method,isCompleteUrl),
	// 历史预约订单
	getCameraReserveList: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/cameraReserve/queryCameraReserve', e, type, mask, loading, method,isCompleteUrl),

	// 开始智慧充电
	toSmartCharge: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/smartCharging/creatSmartChargingRecord', e, type, mask, loading, method,isCompleteUrl),
	// 获取智慧充电等待时间
	getSmartCharge: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/smartCharging/querySmartChargeMes', e, type, mask, loading, method,isCompleteUrl),
	// 结束等待(智慧充电)
	endSmartCharge: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/smartCharging/endSmartCharge', e, type, mask, loading, method,isCompleteUrl), 
	// 查询电站客户联系
	getContact: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryById', e, type, mask, loading, method,isCompleteUrl),
	// 获取补缴状态
	getCheckChargeOrderPay: (e, type = true, mask = false, loading = false, method = 'POST') => http.request(
		'capp/api/pay/checkChargeOrderPay', e, type, mask, loading, method),
	// 获取减免信息
	getReduceInfo: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/carPlaceholder/occupyDiscountMes', e, type, mask, loading, method,isCompleteUrl),
	// 临时车扫码出场支付金额处理
	bikeParkingOrderPayMoney: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeParkingOrder/payMoney', e, type, mask, loading, method,isCompleteUrl),
	// 临时车后付费入场接口
	temporaryCarAdmission: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bikeParkingOrder/temporaryCarAdmission', e, type, mask, loading, method,isCompleteUrl),
	// 单车添加亲友信息
	addChargeRelatives: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeRelatives/add', e, type, mask, loading, method,isCompleteUrl),	
	// 单车编辑亲友信息
	updateChargeRelatives: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeRelatives/update', e, type, mask, loading, method,isCompleteUrl),
	// 单车删除亲友信息
	deleteUpdateChargeRelatives: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeRelatives/delete', e, type, mask, loading, method,isCompleteUrl),
	// 单车通过人员id查询亲友信息
	getchargeRelative: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargeRelatives/selectByChargeUserId', e, type, mask, loading, method,isCompleteUrl),
	// 查询用户的私有桩
	getPrivatePile: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateUser/queryPrivatePile', e, type, mask, loading, method,isCompleteUrl),
	// 取消亲友分享
	cancelShare: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateUser/cancelShare', e, type, mask, loading, method,isCompleteUrl),
	// 分享亲友
	chargePrivateUser: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateUser/share', e, type, mask, loading, method,isCompleteUrl),
	// 用户添加私有桩
	addChargePrivateManage: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateManage/add', e, type, mask, loading, method,isCompleteUrl),
	// 用户编辑私有桩
	editChargePrivateManage: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateManage/edit', e, type, mask, loading, method,isCompleteUrl),
	// 蓝牙指令解析
	explainHex: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bluetooth/explainHex', e, type, mask, loading, method,isCompleteUrl),
	// 获取指令
	getPileOrder: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/bluetooth/getOrder', e, type, mask, loading, method,isCompleteUrl),
	// 查询分享私有桩子用户下拉列表
	getSonPhoneList: (e, type = true, mask = true, loading = false, method = 'GET',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateUser/querySonPhoneList', e, type, mask, loading, method,isCompleteUrl),
	// 查询汽车充电桩版本
	getProgramPackageList: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/fileUpload/queryProgramPackageList', e, type, mask, loading, method,isCompleteUrl), 
	// 充电桩解绑
	cancelBinding: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateManage/cancelBinding', e, type, mask, loading, method,isCompleteUrl), 
	// 蓝牙指令解析
	getFrameInfo: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/encodingBLE/getFrameInfo', e, type, mask, loading, method,isCompleteUrl), 
	// 私有桩历史订单
	getChargePrivateOrder: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/chargePrivateOrder/queryByPage', e, type, mask, loading, method,isCompleteUrl),
	// 停车费查询
	getParkFees: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cloudUrl+'wit/powerStation/queryCouponConfig', e, type, mask, loading, method,isCompleteUrl),
	queryRenewDate: (e, type = true, mask = true, loading = false, method = 'POST',isCompleteUrl = true) => http.request(
		BaseConfig.cappUrl+'capp/coupon/park4gserver/queryRenewDate', e, type, mask, loading, method,isCompleteUrl),
}