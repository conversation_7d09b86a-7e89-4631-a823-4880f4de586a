<template>
	<view class="whole-item">
		<view class="abso-box" >
			<image src="../static/record/Group515.jpg" mode="" class="abso-box-img"  ></image>
			<view class="flex-row alc tb">
				<van-icon @click="goBack" name="arrow-left" size="36rpx" />
				<view @click="goBack" style="margin-left: 4rpx;" class="return-text ">记油耗</view>
			</view>
		</view>
		 <!-- 油耗信息 -->
		<view class="whole-item-con">
				<view class="whole-item-one">
					请输入油耗信息
				</view>
				 <view class="whole-item-two">
					 <view class="whole-name">
					 	 <view class="whole-radlus"></view>
					  <view class="whole-text">
					  	当前车辆
					  </view>
					 </view>
					 <view class="whole-item-twoFlex"></view>
					  <view class="whole-item-list"> 数据 </view>
				 </view>	
				<view class="whole-item-theer">
					<view class="whole-name-one">
						<view class="whole-radlus1"></view>
						<view class="whole-text1">加油日期</view>
					</view>
					<view class="whole-item-twoFlex"></view>
				    <view class="whole-item-list"> 数据 </view>
				</view>
				<view class="whole-item-fooer">
					 <view class="whole-name-two">
					 	<view class="whole-radlus2"></view>
						<view class="whole-text2">油号</view>
					 </view>
					 <view class="whole-item-twoFlex"></view>
					  <view class="whole-item-list"> 数据 </view>
				</view>
		</view>
			<!-- 行程总数 -->
		<view class="whole-item-zong">
			<view class="whole-item-km">
				行程总里程 (km)
			</view>
			<view class="item-inp"></view>
			
			<input type="text" value="上一次用多少"  placeholder="输入金额" class="input"   />
		</view>
		 <!-- 计算总金额 -->
		<view class="whole-item-bottom">
			<view class="whole-item-money">
			  <view class="whole-item-oilPrice">总金额(￥)</view>
			  <view class="whole-item-oilPrice1">油量(L)</view>
			  <view class="whole-item-oilPrice2">金额(￥/L)</view>
			</view>
			<view class="whole-item-money1">
				<view class="whole-item-mileage"><input type="text" value="" placeholder="输入金额" class="inp"></view>
				<text class="whole-item-text">=</text>
				<view class="whole-item-mileage"><input type="text" value="" placeholder="输入金额" class="inp"></view>
				<text class="whole-item-text">X</text>
				<view class="whole-item-mileage"><input type="text" value="" placeholder="输入金额" class="inp"></view>
			</view>
			
			 <view></view>
			<!-- <view class="charts-box">
			<qiun-data-charts type="column" :chartData="chartData" />
			</view> -->
			
		
			
			
		</view>
		
		
		
		
	</view>
</template>

<script>
	
		import BaseConfig from '../../common/config/index.config.js';
		import Dialog from '../../wxcomponents/vant-weapp/dialog/dialog';
		import apis from "../../common/apis/index";
		import form from '../../common/utils/form.js';
		import poster from '../components/poster/poster.vue'
		const app = getApp();
		
	export default {
		data() {
			return {
				chartData:{
					active:2,
				  series: [{
				    data: [
				      {
				        name: "一班",
				        value: 50
				      }, {
				        name: "二班",
				        value: 30
				      }, {
				        name: "三班",
				        value: 20
				      }, {
				        name: "四班",
				        value: 18
				      }, {
				        name: "五班",
				        value: 8
				      }
				    ]
				  }]
				}
			}
		},
		onReady () {
				app.pagePathRecord();
			},
			
		methods: {
			
			
			
			
			goBack () {
				uni.navigateBack({
					fail: (err)=> {
						uni.switchTab({
							url: '/pages/park/index'
						})
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	  .whole-item{
		  width: 100%;
	  }
      .abso-box-img{
		  width: 100%;
	  }
	  .tb{
			position: absolute;
			top: 100rpx;
			left: 20rpx;
		}
	 .whole-item-con{
		 width: 90%;
		 margin-left: 5%;
		 height: 215px;
		 
		 position: absolute;
		 top: 118px;
		 background: #ffffff;
		 border-radius: 15px;
		 box-shadow: 0px 3px 13px 0px rgba(0,0,0,0.04); 
		 display: flex;
		 flex-flow: column;
		 align-items: center;
	 }
	 .whole-item-one{
		 width: 90%;
		 height: 34px;
		 font-size: 17px;
		 font-weight: 700;
		 text-align: LEFT;
		 color: #333333;
		 margin-top: 25px;
	 }
	 .whole-item-two{
		 width: 90%;
		 height: 34px;
		 font-size: 15px;
		 font-weight: 700;
		 text-align: LEFT;
		 color: #333333;
		 margin-top: 10px;
		 border-bottom: 1px solid #999999;
		 display: flex;
		 justify-content: space-between;
		 .whole-radlus{
			 width: 6.5px;
			 height: 6.5px;
			 background: linear-gradient(117deg,#449fff 0%, #0070ff 100%);
			 border-radius: 50%;
			 margin-top: 7px;
		 }
		 .whole-text{
			font-size: 15px;
			font-weight: 500;
			text-align: LEFT;
			color: #333333;
			// line-height: 24px;
			margin-left: 10px;
			
		 }
		 .whole-item-twoFlex{
			 flex: 1;
		 }
		 
		 .whole-name{
			 width: 150px;
			 height: 30px;
			 display: flex;
		 }
	 }
	 .whole-item-theer{
	 		 width: 90%;
	 		 height: 34px;
	 		 border-bottom: 1px solid #999999;
	 		 font-size: 15px;
	 		 font-weight: 700;
	 		 text-align: LEFT;
	 		 color: #333333;
			 margin-top: 10px;
			 display: flex;
			 justify-content: space-between;
			 .whole-radlus1{
				 width: 6.5px;
				 height: 6.5px;
				 background: linear-gradient(180deg,#1fdada, #0ec7c7 100%);
				 border-radius: 50%;
				 margin-top: 7px;
			 }
			 .whole-name-one{
				 display: flex;
			 }
			 .whole-text1{
				 font-size: 15px;
				 font-weight: 500;
				 text-align: LEFT;
				 color: #333333;
				 // line-height: 24px;
				 margin-left: 10px;
			 }
	 }
	 .whole-item-fooer{
	 		 width: 90%;
	 		 height: 34px;
	 		 // border: 1px solid red;
	 		 font-size: 15px;
	 		 font-weight: 700;
	 		 text-align: LEFT;
	 		 color: #333333;
			 margin-top: 15px;
			 display: flex;
			 justify-content:space-between ;
			 .whole-name-two{
				 display: flex;
			 }
			 .whole-radlus2{
				 width: 6.5px;
				 height: 6.5px;
				 background: linear-gradient(180deg,#ffc369, #fcaa4a 100%);
				 border-radius: 50%;
				 margin-top: 7px;
			 }
		   .whole-text2{
			   margin-left: 10px;
		   } 
	 }
	 .whole-item-list{
		 font-size: 14px;
		 font-family: PingFang SC, PingFang SC-Medium;
		 font-weight: 500;
		 text-align: LEFT;
		 color: #999999;
	 }
	 .whole-item-zong{
		 width: 90%;
		 height: 66.5px;
		 margin-left: 5%;
		 // border: 1px solid red;
		 position: absolute;
		 top: 342px;
		 background: #ffffff;
		 border-radius: 15px;
		 box-shadow: 0px 6px 26px 0px rgba(0,0,0,0.04); 
		 display: flex;
		 align-items: center;
		 justify-content: selector-append;
		 
		 .item-inp{
			 flex: 1;
		 }
		 .whole-item-km{
			 margin-left: 10px;
			 font-size: 15px;
			 font-weight: 500;
			 text-align: LEFT;
			 color: #333333;
		 }
		.input{
			width:110px;
			height: 37px;
			border: 2px solid #eeeeee;
			border-radius: 5px;
		    margin-right: 10px;
			font-size: 12px;
			font-weight: 500;
			text-align: LEFT;
			color: #999999;
		}
	 }
	 .whole-item-bottom{
		 width: 90%;
		 height: 300px;
		 margin-left: 5%;
		 border: 1px solid red;
		 position: absolute;
		 top: 419px;
	 }
	 .whole-item-money{
		 display: flex;
		justify-content: selector-append;
		align-items: center;
		.whole-item-oilPrice{
			margin-top: 15px;
			margin-left: 30px;
			font-size: 12px;
			font-weight: 500;
			text-align: LEFT;
			color: #999999;
		}
		.whole-item-oilPrice1{
			margin-top: 15px;
			margin-left: 65px;
			font-size: 12px;
			font-weight: 500;
			text-align: LEFT;
			color: #999999;
		}
		.whole-item-oilPrice2{
			margin-top: 15px;
			margin-left: 60px;
			font-size: 12px;
			font-weight: 500;
			text-align: LEFT;
			color: #999999;
		}
	 }
	 .whole-item-money1{
		 display: flex;
		 justify-content: selector-append;
		 align-items: center;
		 .inp{
			 width: 77px;
			 height: 37px;
			 border: 2px solid #eeeeee;
			 border-radius: 5px;
			 margin-top: 10px;
			 font-size: 12px;
			 font-weight: 500;
			 text-align: LEFT;
			 color: #999999;
		 }
		 .whole-item-mileage{
			 margin-left: 20px;
		 }
		 .whole-item-text{
			 margin-left: 5px;
			 margin-top: 5px;
			 font-size: 12px;
			 font-weight: 500;
			 text-align: LEFT;
			 color: #999999;
		 }
	 }
	 .charts-box{
		 width: 300px;
		 height: 300px;
	 }
</style>
