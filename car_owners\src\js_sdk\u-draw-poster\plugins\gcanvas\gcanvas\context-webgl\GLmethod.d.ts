export default GLmethod;
declare namespace GLmethod {
    const activeTexture: number;
    const attachShader: number;
    const bindAttribLocation: number;
    const bindBuffer: number;
    const bindFramebuffer: number;
    const bindRenderbuffer: number;
    const bindTexture: number;
    const blendColor: number;
    const blendEquation: number;
    const blendEquationSeparate: number;
    const blendFunc: number;
    const blendFuncSeparate: number;
    const bufferData: number;
    const bufferSubData: number;
    const checkFramebufferStatus: number;
    const clear: number;
    const clearColor: number;
    const clearDepth: number;
    const clearStencil: number;
    const colorMask: number;
    const compileShader: number;
    const compressedTexImage2D: number;
    const compressedTexSubImage2D: number;
    const copyTexImage2D: number;
    const copyTexSubImage2D: number;
    const createBuffer: number;
    const createFramebuffer: number;
    const createProgram: number;
    const createRenderbuffer: number;
    const createShader: number;
    const createTexture: number;
    const cullFace: number;
    const deleteBuffer: number;
    const deleteFramebuffer: number;
    const deleteProgram: number;
    const deleteRenderbuffer: number;
    const deleteShader: number;
    const deleteTexture: number;
    const depthFunc: number;
    const depthMask: number;
    const depthRange: number;
    const detachShader: number;
    const disable: number;
    const disableVertexAttribArray: number;
    const drawArrays: number;
    const drawArraysInstancedANGLE: number;
    const drawElements: number;
    const drawElementsInstancedANGLE: number;
    const enable: number;
    const enableVertexAttribArray: number;
    const flush: number;
    const framebufferRenderbuffer: number;
    const framebufferTexture2D: number;
    const frontFace: number;
    const generateMipmap: number;
    const getActiveAttrib: number;
    const getActiveUniform: number;
    const getAttachedShaders: number;
    const getAttribLocation: number;
    const getBufferParameter: number;
    const getContextAttributes: number;
    const getError: number;
    const getExtension: number;
    const getFramebufferAttachmentParameter: number;
    const getParameter: number;
    const getProgramInfoLog: number;
    const getProgramParameter: number;
    const getRenderbufferParameter: number;
    const getShaderInfoLog: number;
    const getShaderParameter: number;
    const getShaderPrecisionFormat: number;
    const getShaderSource: number;
    const getSupportedExtensions: number;
    const getTexParameter: number;
    const getUniform: number;
    const getUniformLocation: number;
    const getVertexAttrib: number;
    const getVertexAttribOffset: number;
    const isBuffer: number;
    const isContextLost: number;
    const isEnabled: number;
    const isFramebuffer: number;
    const isProgram: number;
    const isRenderbuffer: number;
    const isShader: number;
    const isTexture: number;
    const lineWidth: number;
    const linkProgram: number;
    const pixelStorei: number;
    const polygonOffset: number;
    const readPixels: number;
    const renderbufferStorage: number;
    const sampleCoverage: number;
    const scissor: number;
    const shaderSource: number;
    const stencilFunc: number;
    const stencilFuncSeparate: number;
    const stencilMask: number;
    const stencilMaskSeparate: number;
    const stencilOp: number;
    const stencilOpSeparate: number;
    const texImage2D: number;
    const texParameterf: number;
    const texParameteri: number;
    const texSubImage2D: number;
    const uniform1f: number;
    const uniform1fv: number;
    const uniform1i: number;
    const uniform1iv: number;
    const uniform2f: number;
    const uniform2fv: number;
    const uniform2i: number;
    const uniform2iv: number;
    const uniform3f: number;
    const uniform3fv: number;
    const uniform3i: number;
    const uniform3iv: number;
    const uniform4f: number;
    const uniform4fv: number;
    const uniform4i: number;
    const uniform4iv: number;
    const uniformMatrix2fv: number;
    const uniformMatrix3fv: number;
    const uniformMatrix4fv: number;
    const useProgram: number;
    const validateProgram: number;
    const vertexAttrib1f: number;
    const vertexAttrib2f: number;
    const vertexAttrib3f: number;
    const vertexAttrib4f: number;
    const vertexAttrib1fv: number;
    const vertexAttrib2fv: number;
    const vertexAttrib3fv: number;
    const vertexAttrib4fv: number;
    const vertexAttribPointer: number;
    const viewport: number;
}
